(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,65759,n=>{"use strict";function e(n){return window.go.main.App.AddBackend(n)}function t(n){return window.go.main.App.AddTarget(n)}function i(n){return window.go.main.App.DeleteBackend(n)}function o(n){return window.go.main.App.DeleteTarget(n)}function r(){return window.go.main.App.GetActiveBackend()}function a(){return window.go.main.App.GetBackends()}function c(){return window.go.main.App.GetConfig()}function d(){return window.go.main.App.GetConfigPath()}function u(){return window.go.main.App.GetDefaultOverrides()}function p(){return window.go.main.App.GetLink()}function w(){return window.go.main.App.GetScanAuthorization()}function g(){return window.go.main.App.GetSettings()}function A(){return window.go.main.App.GetSeverityCounts()}function f(){return window.go.main.App.GetTargetAgent()}function m(){return window.go.main.App.GetTargets()}function s(n){return window.go.main.App.Greet(n)}function G(n){return window.go.main.App.SetActiveBackend(n)}function k(n){return window.go.main.App.TestBackendConnection(n)}function B(n){return window.go.main.App.TestBackendConnectionDirect(n)}function T(n,e){return window.go.main.App.UpdateBackend(n,e)}function C(n){return window.go.main.App.UpdateSettings(n)}n.s(["AddBackend",()=>e,"AddTarget",()=>t,"DeleteBackend",()=>i,"DeleteTarget",()=>o,"GetActiveBackend",()=>r,"GetBackends",()=>a,"GetConfig",()=>c,"GetConfigPath",()=>d,"GetDefaultOverrides",()=>u,"GetLink",()=>p,"GetScanAuthorization",()=>w,"GetSettings",()=>g,"GetSeverityCounts",()=>A,"GetTargetAgent",()=>f,"GetTargets",()=>m,"Greet",()=>s,"SetActiveBackend",()=>G,"TestBackendConnection",()=>k,"TestBackendConnectionDirect",()=>B,"UpdateBackend",()=>T,"UpdateSettings",()=>C])},43988,n=>{n.v(n=>Promise.resolve().then(()=>n(65759)))}]);