{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/wailsjs/go/main/App.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "// @ts-check\n// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL\n// This file is automatically generated. DO NOT EDIT\n\nexport function AddBackend(arg1) {\n  return window['go']['main']['App']['AddBackend'](arg1);\n}\n\nexport function AddTarget(arg1) {\n  return window['go']['main']['App']['AddTarget'](arg1);\n}\n\nexport function DeleteBackend(arg1) {\n  return window['go']['main']['App']['DeleteBackend'](arg1);\n}\n\nexport function DeleteTarget(arg1) {\n  return window['go']['main']['App']['DeleteTarget'](arg1);\n}\n\nexport function GetActiveBackend() {\n  return window['go']['main']['App']['GetActiveBackend']();\n}\n\nexport function GetBackends() {\n  return window['go']['main']['App']['GetBackends']();\n}\n\nexport function GetConfig() {\n  return window['go']['main']['App']['GetConfig']();\n}\n\nexport function GetConfigPath() {\n  return window['go']['main']['App']['GetConfigPath']();\n}\n\nexport function GetDefaultOverrides() {\n  return window['go']['main']['App']['GetDefaultOverrides']();\n}\n\nexport function GetLink() {\n  return window['go']['main']['App']['GetLink']();\n}\n\nexport function GetScanAuthorization() {\n  return window['go']['main']['App']['GetScanAuthorization']();\n}\n\nexport function GetSettings() {\n  return window['go']['main']['App']['GetSettings']();\n}\n\nexport function GetSeverityCounts() {\n  return window['go']['main']['App']['GetSeverityCounts']();\n}\n\nexport function GetTargetAgent() {\n  return window['go']['main']['App']['GetTargetAgent']();\n}\n\nexport function GetTargets() {\n  return window['go']['main']['App']['GetTargets']();\n}\n\nexport function Greet(arg1) {\n  return window['go']['main']['App']['Greet'](arg1);\n}\n\nexport function SetActiveBackend(arg1) {\n  return window['go']['main']['App']['SetActiveBackend'](arg1);\n}\n\nexport function TestBackendConnection(arg1) {\n  return window['go']['main']['App']['TestBackendConnection'](arg1);\n}\n\nexport function TestBackendConnectionDirect(arg1) {\n  return window['go']['main']['App']['TestBackendConnectionDirect'](arg1);\n}\n\nexport function UpdateBackend(arg1, arg2) {\n  return window['go']['main']['App']['UpdateBackend'](arg1, arg2);\n}\n\nexport function UpdateSettings(arg1) {\n  return window['go']['main']['App']['UpdateSettings'](arg1);\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React", "ReactDOM"], "mappings": "yNA0BQG,GAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,8BCF9BR,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEG,QAAQ,wBCE1B,SAAS,EAAW,CAAI,EAC7B,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,UAAa,CAAC,EACnD,CAEO,SAAS,EAAU,CAAI,EAC5B,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,SAAY,CAAC,EAClD,CAEO,SAAS,EAAc,CAAI,EAChC,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,aAAgB,CAAC,EACtD,CAEO,SAAS,EAAa,CAAI,EAC/B,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,YAAe,CAAC,EACrD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,gBAAmB,EACxD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,WAAc,EACnD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,SAAY,EACjD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,aAAgB,EACrD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,mBAAsB,EAC3D,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,OAAU,EAC/C,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,oBAAuB,EAC5D,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,WAAc,EACnD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,iBAAoB,EACzD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,cAAiB,EACtD,CAEO,SAAS,IACd,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,UAAa,EAClD,CAEO,SAAS,EAAM,CAAI,EACxB,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,KAAQ,CAAC,EAC9C,CAEO,SAAS,EAAiB,CAAI,EACnC,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,gBAAmB,CAAC,EACzD,CAEO,SAAS,EAAsB,CAAI,EACxC,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,qBAAwB,CAAC,EAC9D,CAEO,SAAS,EAA4B,CAAI,EAC9C,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,2BAA8B,CAAC,EACpE,CAEO,SAAS,EAAc,CAAI,CAAE,CAAI,EACtC,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,aAAgB,CAAC,EAAM,EAC5D,CAEO,SAAS,EAAe,CAAI,EACjC,OAAO,MAAM,CAAC,EAAK,CAAC,IAAO,CAAC,GAAM,CAAC,cAAiB,CAAC,EACvD", "ignoreList": [0, 1, 2, 3]}