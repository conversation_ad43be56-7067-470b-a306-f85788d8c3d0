export namespace main {
	
	export class Backend {
	    id: string;
	    name: string;
	    url: string;
	    api_key: string;
	    is_active: boolean;
	    // Go type: time
	    created_at: any;
	    // Go type: time
	    updated_at: any;
	    // Go type: time
	    last_tested: any;
	    status: string;
	    latency: number;
	
	    static createFrom(source: any = {}) {
	        return new Backend(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.url = source["url"];
	        this.api_key = source["api_key"];
	        this.is_active = source["is_active"];
	        this.created_at = this.convertValues(source["created_at"], null);
	        this.updated_at = this.convertValues(source["updated_at"], null);
	        this.last_tested = this.convertValues(source["last_tested"], null);
	        this.status = source["status"];
	        this.latency = source["latency"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class BackendConnectionTest {
	    success: boolean;
	    message: string;
	    status: number;
	    latency: number;
	    version?: string;
	    // Go type: time
	    tested_at: any;
	
	    static createFrom(source: any = {}) {
	        return new BackendConnectionTest(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.success = source["success"];
	        this.message = source["message"];
	        this.status = source["status"];
	        this.latency = source["latency"];
	        this.version = source["version"];
	        this.tested_at = this.convertValues(source["tested_at"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class BackendList {
	    backends: Backend[];
	    count: number;
	
	    static createFrom(source: any = {}) {
	        return new BackendList(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.backends = this.convertValues(source["backends"], Backend);
	        this.count = source["count"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Settings {
	    auto_connect: boolean;
	    check_interval: number;
	    theme: string;
	    language: string;
	    log_level: string;
	    max_retries: number;
	    connection_timeout: number;
	
	    static createFrom(source: any = {}) {
	        return new Settings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.auto_connect = source["auto_connect"];
	        this.check_interval = source["check_interval"];
	        this.theme = source["theme"];
	        this.language = source["language"];
	        this.log_level = source["log_level"];
	        this.max_retries = source["max_retries"];
	        this.connection_timeout = source["connection_timeout"];
	    }
	}
	export class Config {
	    version: string;
	    backends: Backend[];
	    settings: Settings;
	    // Go type: time
	    last_save: any;
	
	    static createFrom(source: any = {}) {
	        return new Config(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.version = source["version"];
	        this.backends = this.convertValues(source["backends"], Backend);
	        this.settings = this.convertValues(source["settings"], Settings);
	        this.last_save = this.convertValues(source["last_save"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class DefaultOverrides {
	    auth: string;
	    scan: string;
	
	    static createFrom(source: any = {}) {
	        return new DefaultOverrides(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.auth = source["auth"];
	        this.scan = source["scan"];
	    }
	}
	export class Link {
	    rel: string;
	    href: string;
	
	    static createFrom(source: any = {}) {
	        return new Link(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.rel = source["rel"];
	        this.href = source["href"];
	    }
	}
	export class NewBackendRequest {
	    name: string;
	    url: string;
	    api_key: string;
	
	    static createFrom(source: any = {}) {
	        return new NewBackendRequest(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.url = source["url"];
	        this.api_key = source["api_key"];
	    }
	}
	export class NewTargetRequest {
	    address: string;
	    description: string;
	    criticality: number;
	
	    static createFrom(source: any = {}) {
	        return new NewTargetRequest(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.address = source["address"];
	        this.description = source["description"];
	        this.criticality = source["criticality"];
	    }
	}
	export class ScanAuthorization {
	    url: string;
	    content: string;
	
	    static createFrom(source: any = {}) {
	        return new ScanAuthorization(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.content = source["content"];
	    }
	}
	
	export class SeverityCounts {
	    critical: number;
	    high: number;
	    medium: number;
	    low: number;
	    info: number;
	
	    static createFrom(source: any = {}) {
	        return new SeverityCounts(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.critical = source["critical"];
	        this.high = source["high"];
	        this.medium = source["medium"];
	        this.low = source["low"];
	        this.info = source["info"];
	    }
	}
	export class TargetAgent {
	    agent_id: string;
	    name: string;
	
	    static createFrom(source: any = {}) {
	        return new TargetAgent(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.agent_id = source["agent_id"];
	        this.name = source["name"];
	    }
	}
	export class TargetItemResponse {
	    address: string;
	    description: string;
	    type: string;
	    criticality: number;
	    fqdn_status?: string;
	    fqdn_tm_hash?: string;
	    deleted_at?: string;
	    fqdn?: string;
	    fqdn_hash?: string;
	    default_scanning_profile_id?: string;
	    agents?: TargetAgent[];
	    default_overrides?: DefaultOverrides;
	    target_id: string;
	    scan_authorization?: ScanAuthorization;
	    continuous_mode: boolean;
	    last_scan_date?: string;
	    last_scan_id?: string;
	    last_scan_session_id?: string;
	    last_scan_session_status?: string;
	    severity_counts?: SeverityCounts;
	    threat: number;
	    links?: Link[];
	    manual_intervention: boolean;
	    verification?: string;
	
	    static createFrom(source: any = {}) {
	        return new TargetItemResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.address = source["address"];
	        this.description = source["description"];
	        this.type = source["type"];
	        this.criticality = source["criticality"];
	        this.fqdn_status = source["fqdn_status"];
	        this.fqdn_tm_hash = source["fqdn_tm_hash"];
	        this.deleted_at = source["deleted_at"];
	        this.fqdn = source["fqdn"];
	        this.fqdn_hash = source["fqdn_hash"];
	        this.default_scanning_profile_id = source["default_scanning_profile_id"];
	        this.agents = this.convertValues(source["agents"], TargetAgent);
	        this.default_overrides = this.convertValues(source["default_overrides"], DefaultOverrides);
	        this.target_id = source["target_id"];
	        this.scan_authorization = this.convertValues(source["scan_authorization"], ScanAuthorization);
	        this.continuous_mode = source["continuous_mode"];
	        this.last_scan_date = source["last_scan_date"];
	        this.last_scan_id = source["last_scan_id"];
	        this.last_scan_session_id = source["last_scan_session_id"];
	        this.last_scan_session_status = source["last_scan_session_status"];
	        this.severity_counts = this.convertValues(source["severity_counts"], SeverityCounts);
	        this.threat = source["threat"];
	        this.links = this.convertValues(source["links"], Link);
	        this.manual_intervention = source["manual_intervention"];
	        this.verification = source["verification"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class TargetList {
	    targets: TargetItemResponse[];
	    count: number;
	
	    static createFrom(source: any = {}) {
	        return new TargetList(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.targets = this.convertValues(source["targets"], TargetItemResponse);
	        this.count = source["count"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

