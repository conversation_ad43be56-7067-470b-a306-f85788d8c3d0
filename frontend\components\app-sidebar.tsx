"use client"

import * as React from "react"
import {
  Radar,
  Frame,
  ChartColumn,
  Bug,
  Target,
  Home,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavSettings } from "@/components/nav-settings"
import { BackendSwitcher } from "@/components/backend-switcher"
import { AddBackendDialog } from "@/components/add-backend-dialog"
import { EditBackendDialog } from "@/components/edit-backend-dialog"
import { main } from "@/wailsjs/go/models"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onNavigate?: (view: string) => void
}

export function AppSidebar({ onNavigate, ...props }: AppSidebarProps) {
  const [showAddBackend, setShowAddBackend] = React.useState(false)
  const [showEditBackend, setShowEditBackend] = React.useState(false)
  const [editingBackend, setEditingBackend] = React.useState<main.Backend | null>(null)
  const [refreshTrigger, setRefreshTrigger] = React.useState(0)

  // This is sample data.
  const data = {
    user: {
      name: "shadcn",
      email: "<EMAIL>",
      avatar: "/avatars/shadcn.jpg",
    },
    navMain: [
      {
        title: "Dashboard",
        url: "#",
        icon: Home,
        isActive: true,
        onClick: () => onNavigate?.("dashboard"),
      },
      {
        title: "Targets",
        url: "#",
        icon: Target,
        onClick: () => onNavigate?.("targets"),
        items: [
          {
            title: "Manage Targets",
            url: "#",
            onClick: () => onNavigate?.("targets"),
          },
        {
          title: "Target Groups",
          url: "#",
        },
      ],
    },
    {
      title: "Scans",
      url: "#",
      icon: Radar,
    },
    {
      title: "Vulnerabilities",
      url: "#",
      icon: Bug,
    },
    {
      title: "Reports",
      url: "#",
      icon: ChartColumn,
    },
  ],
  projects: [
    {
      name: "Project A",
      url: "#",
      icon: Frame,
    },
    {
      name: "Project B",
      url: "#",
      icon: Frame,
    },
    {
      name: "Project C",
      url: "#",
      icon: Frame,
    },
  ],
}

  const handleAddBackend = () => {
    setShowAddBackend(true)
  }

  const handleEditBackend = (backend: main.Backend) => {
    setEditingBackend(backend)
    setShowEditBackend(true)
  }

  const handleBackendAdded = () => {
    // Trigger refresh of the backend list
    setRefreshTrigger(prev => prev + 1)
  }

  const handleBackendUpdated = () => {
    // Trigger refresh of the backend list
    setRefreshTrigger(prev => prev + 1)
  }

  const handleBackendDeleted = () => {
    // Trigger refresh of the backend list
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <>
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <BackendSwitcher
            onAddBackend={handleAddBackend}
            onEditBackend={handleEditBackend}
            refreshTrigger={refreshTrigger}
          />
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={data.navMain} onNavigate={onNavigate} />
          <NavProjects projects={data.projects} />
        </SidebarContent>
        <SidebarFooter>
          <NavSettings />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>

      <AddBackendDialog
        open={showAddBackend}
        onOpenChange={setShowAddBackend}
        onBackendAdded={handleBackendAdded}
      />

      <EditBackendDialog
        open={showEditBackend}
        onOpenChange={setShowEditBackend}
        backend={editingBackend}
        onBackendUpdated={handleBackendUpdated}
        onBackendDeleted={handleBackendDeleted}
      />
    </>
  )
}
