// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddBackend(arg1) {
  return window['go']['main']['App']['AddBackend'](arg1);
}

export function AddTarget(arg1) {
  return window['go']['main']['App']['AddTarget'](arg1);
}

export function DeleteBackend(arg1) {
  return window['go']['main']['App']['DeleteBackend'](arg1);
}

export function DeleteTarget(arg1) {
  return window['go']['main']['App']['DeleteTarget'](arg1);
}

export function GetActiveBackend() {
  return window['go']['main']['App']['GetActiveBackend']();
}

export function GetBackends() {
  return window['go']['main']['App']['GetBackends']();
}

export function GetConfig() {
  return window['go']['main']['App']['GetConfig']();
}

export function GetConfigPath() {
  return window['go']['main']['App']['GetConfigPath']();
}

export function GetSettings() {
  return window['go']['main']['App']['GetSettings']();
}

export function GetTargets() {
  return window['go']['main']['App']['GetTargets']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function SetActiveBackend(arg1) {
  return window['go']['main']['App']['SetActiveBackend'](arg1);
}

export function TestBackendConnection(arg1) {
  return window['go']['main']['App']['TestBackendConnection'](arg1);
}

export function TestBackendConnectionDirect(arg1) {
  return window['go']['main']['App']['TestBackendConnectionDirect'](arg1);
}

export function UpdateBackend(arg1, arg2) {
  return window['go']['main']['App']['UpdateBackend'](arg1, arg2);
}

export function UpdateSettings(arg1) {
  return window['go']['main']['App']['UpdateSettings'](arg1);
}
