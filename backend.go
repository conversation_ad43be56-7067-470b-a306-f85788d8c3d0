package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// Backend represents an Acunetix scanner backend configuration
type Backend struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	URL        string    `json:"url"`
	APIKey     string    `json:"api_key"`
	IsActive   bool      `json:"is_active"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	LastTested time.Time `json:"last_tested"`
	Status     string    `json:"status"`  // "connected", "error", "unknown"
	Latency    int64     `json:"latency"` // in milliseconds, -1 means no data
}

// BackendConnectionTest represents the result of testing a backend connection
type BackendConnectionTest struct {
	Success  bool      `json:"success"`
	Message  string    `json:"message"`
	Status   int       `json:"status"`
	Latency  int64     `json:"latency"` // in milliseconds
	Version  string    `json:"version,omitempty"`
	TestedAt time.Time `json:"tested_at"`
}

// BackendList represents a list of backends
type BackendList struct {
	Backends []Backend `json:"backends"`
	Count    int       `json:"count"`
}

// NewBackendRequest represents the request to create a new backend
type NewBackendRequest struct {
	Name   string `json:"name"`
	URL    string `json:"url"`
	APIKey string `json:"api_key"`
}

// BackendManager manages backend configurations
type BackendManager struct {
	configManager *ConfigManager
	stopTesting   chan bool
}

// NewBackendManager creates a new backend manager
func NewBackendManager() *BackendManager {
	bm := &BackendManager{
		configManager: NewConfigManager(),
		stopTesting:   make(chan bool),
	}

	// Start periodic latency testing
	go bm.startPeriodicTesting()

	return bm
}

// normalizeURL normalizes various URL formats to a consistent format
func normalizeURL(rawURL string) string {
	// Remove trailing slashes
	url := strings.TrimSuffix(rawURL, "/")

	// Add https:// if no protocol specified
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	// Handle different API path formats
	if strings.HasSuffix(url, "/api/v1") {
		// Already has the correct API path
		return url
	} else if strings.Contains(url, "/api/v1/") {
		// Has /api/v1/ with additional path, extract base URL + /api/v1
		parts := strings.Split(url, "/api/v1/")
		return parts[0] + "/api/v1"
	} else if strings.HasSuffix(url, "/api") {
		// Add /v1 to /api
		return url + "/v1"
	} else {
		// No API path, add /api/v1
		return url + "/api/v1"
	}
}

// AddBackend adds a new backend configuration
func (bm *BackendManager) AddBackend(req NewBackendRequest) (*Backend, error) {
	// Validate required fields
	if req.Name == "" {
		return nil, fmt.Errorf("backend name is required")
	}
	if req.URL == "" {
		return nil, fmt.Errorf("backend URL is required")
	}
	if req.APIKey == "" {
		return nil, fmt.Errorf("API key is required")
	}

	// Normalize URL
	url := normalizeURL(req.URL)

	// Generate ID (simple implementation - in production, use UUID)
	id := fmt.Sprintf("backend_%d", time.Now().Unix())

	backend := Backend{
		ID:        id,
		Name:      req.Name,
		URL:       url,
		APIKey:    req.APIKey,
		IsActive:  false,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Status:    "unknown",
		Latency:   -1, // No data initially
	}

	err := bm.configManager.AddBackend(backend)
	if err != nil {
		return nil, err
	}
	return &backend, nil
}

// GetBackends returns all backend configurations
func (bm *BackendManager) GetBackends() BackendList {
	backends := bm.configManager.GetBackends()
	return BackendList{
		Backends: backends,
		Count:    len(backends),
	}
}

// GetBackend returns a specific backend by ID
func (bm *BackendManager) GetBackend(id string) (*Backend, error) {
	return bm.configManager.GetBackend(id)
}

// UpdateBackend updates an existing backend
func (bm *BackendManager) UpdateBackend(id string, req NewBackendRequest) (*Backend, error) {
	backend, err := bm.configManager.GetBackend(id)
	if err != nil {
		return nil, err
	}

	// Update fields - always update all fields from request
	backend.Name = req.Name

	// Normalize URL
	url := normalizeURL(req.URL)
	backend.URL = url

	backend.APIKey = req.APIKey

	err = bm.configManager.UpdateBackend(id, *backend)
	if err != nil {
		return nil, err
	}

	return backend, nil
}

// DeleteBackend removes a backend configuration
func (bm *BackendManager) DeleteBackend(id string) error {
	return bm.configManager.DeleteBackend(id)
}

// SetActiveBackend sets a backend as active and deactivates others
func (bm *BackendManager) SetActiveBackend(id string) error {
	return bm.configManager.SetActiveBackend(id)
}

// GetActiveBackend returns the currently active backend
func (bm *BackendManager) GetActiveBackend() (*Backend, error) {
	return bm.configManager.GetActiveBackend()
}

// TestBackendConnection tests the connection to a backend
func (bm *BackendManager) TestBackendConnection(backend *Backend) BackendConnectionTest {
	start := time.Now()

	// Create HTTP client with timeout and SSL verification disabled for self-signed certificates
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// Test endpoint - try to get targets (requires authentication)
	testURL := backend.URL + "/targets"

	req, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		return BackendConnectionTest{
			Success:  false,
			Message:  fmt.Sprintf("Failed to create request: %v", err),
			Status:   0,
			Latency:  time.Since(start).Milliseconds(),
			TestedAt: time.Now(),
		}
	}

	// Add authentication header
	req.Header.Set("X-Auth", backend.APIKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return BackendConnectionTest{
			Success:  false,
			Message:  fmt.Sprintf("Connection failed: %v", err),
			Status:   0,
			Latency:  time.Since(start).Milliseconds(),
			TestedAt: time.Now(),
		}
	}
	defer resp.Body.Close()

	latency := time.Since(start).Milliseconds()

	if resp.StatusCode == 200 {
		// Try to read response body for additional info
		body, _ := io.ReadAll(resp.Body)
		var targetsResponse map[string]interface{}
		json.Unmarshal(body, &targetsResponse)

		// Extract some info from targets response
		message := "Connection successful - API accessible"
		if targets, ok := targetsResponse["targets"].([]interface{}); ok {
			message = fmt.Sprintf("Connection successful - %d targets found", len(targets))
		}

		return BackendConnectionTest{
			Success:  true,
			Message:  message,
			Status:   resp.StatusCode,
			Latency:  latency,
			TestedAt: time.Now(),
		}
	}

	// Read error response
	body, _ := io.ReadAll(resp.Body)
	message := fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body))
	if len(message) > 200 {
		message = message[:200] + "..."
	}

	return BackendConnectionTest{
		Success:  false,
		Message:  message,
		Status:   resp.StatusCode,
		Latency:  latency,
		TestedAt: time.Now(),
	}
}

// UpdateBackendStatus updates the status of a backend after testing
func (bm *BackendManager) UpdateBackendStatus(id string, test BackendConnectionTest) error {
	status := "error"
	if test.Success {
		status = "connected"
	}
	return bm.configManager.UpdateBackendStatusAndLatency(id, status, test.TestedAt, test.Latency)
}

// startPeriodicTesting starts a goroutine that tests all backends every 5 seconds
func (bm *BackendManager) startPeriodicTesting() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			bm.testAllBackends()
		case <-bm.stopTesting:
			return
		}
	}
}

// testAllBackends tests latency for all backends
func (bm *BackendManager) testAllBackends() {
	backends := bm.configManager.GetBackends()

	for _, backend := range backends {
		// Test each backend in a separate goroutine to avoid blocking
		go func(b Backend) {
			test := bm.TestBackendConnection(&b)
			bm.UpdateBackendStatus(b.ID, test)
		}(backend)
	}
}

// StopPeriodicTesting stops the periodic testing
func (bm *BackendManager) StopPeriodicTesting() {
	close(bm.stopTesting)
}

// Target represents an Acunetix scan target
type Target struct {
	ID          string `json:"target_id"`
	Address     string `json:"address"`
	Description string `json:"description"`
	Criticality int    `json:"criticality"`
	Type        string `json:"type"`
}

// TargetList represents a list of targets
type TargetList struct {
	Targets []Target `json:"targets"`
	Count   int      `json:"count"`
}

// NewTargetRequest represents the request to create a new target
type NewTargetRequest struct {
	Address     string `json:"address"`
	Description string `json:"description"`
	Criticality int    `json:"criticality"`
}

// GetTargets retrieves all targets from the active backend
func (bm *BackendManager) GetTargets() (*TargetList, error) {
	activeBackend, err := bm.GetActiveBackend()
	if err != nil {
		return nil, fmt.Errorf("no active backend: %v", err)
	}

	// Create HTTP client with timeout and SSL verification disabled
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// Get targets endpoint
	url := activeBackend.URL + "/targets"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("X-Auth", activeBackend.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// Parse response
	var response struct {
		Targets []Target `json:"targets"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &TargetList{
		Targets: response.Targets,
		Count:   len(response.Targets),
	}, nil
}

// AddTarget adds a new target to the active backend
func (bm *BackendManager) AddTarget(req NewTargetRequest) (*Target, error) {
	activeBackend, err := bm.GetActiveBackend()
	if err != nil {
		return nil, fmt.Errorf("no active backend: %v", err)
	}

	// Validate required fields
	if req.Address == "" {
		return nil, fmt.Errorf("target address is required")
	}

	// Create HTTP client
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// Prepare request body
	requestBody := map[string]interface{}{
		"address":     req.Address,
		"description": req.Description,
		"criticality": req.Criticality,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Create request
	url := activeBackend.URL + "/targets"
	httpReq, err := http.NewRequest("POST", url, strings.NewReader(string(jsonBody)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	httpReq.Header.Set("X-Auth", activeBackend.APIKey)
	httpReq.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// Parse response
	var target Target
	if err := json.Unmarshal(body, &target); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &target, nil
}

// DeleteTarget deletes a target from the active backend
func (bm *BackendManager) DeleteTarget(targetID string) error {
	activeBackend, err := bm.GetActiveBackend()
	if err != nil {
		return fmt.Errorf("no active backend: %v", err)
	}

	// Create HTTP client
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// Create request
	url := activeBackend.URL + "/targets/" + targetID
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("X-Auth", activeBackend.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 204 && resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}
