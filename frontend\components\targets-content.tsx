"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { GetTargets, AddTarget, DeleteTarget } from "@/wailsjs/go/main/App"
import { DataTable } from "@/components/targets/data-table"
import { createColumns, Target } from "@/components/targets/columns"

export function TargetsContent() {
  const [targets, setTargets] = React.useState<Target[]>([])
  const [loading, setLoading] = React.useState(false)
  const [refreshing, setRefreshing] = React.useState(false)
  const [addDialogOpen, setAddDialogOpen] = React.useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [selectedTarget, setSelectedTarget] = React.useState<Target | null>(null)
  const [formData, setFormData] = React.useState({
    address: "",
    description: "",
    criticality: 10,
  })
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  const loadTargets = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      const result = await GetTargets()
      setTargets(result.targets || [])
    } catch (error) {
      console.error("Failed to load targets:", error)
      setTargets([])
    } finally {
      if (isRefresh) {
        setRefreshing(false)
      } else {
        setLoading(false)
      }
    }
  }

  React.useEffect(() => {
    loadTargets()
  }, [])

  const handleRefresh = React.useCallback(() => {
    loadTargets(true)
  }, [])

  const handleAddTarget = async () => {
    try {
      // Validate form
      const newErrors: Record<string, string> = {}
      if (!formData.address.trim()) {
        newErrors.address = "Target address is required"
      }
      
      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors)
        return
      }

      setLoading(true)
      await AddTarget({
        address: formData.address.trim(),
        description: formData.description.trim(),
        criticality: formData.criticality,
      })
      
      // Reset form and close dialog
      setFormData({ address: "", description: "", criticality: 10 })
      setErrors({})
      setAddDialogOpen(false)
      
      // Reload targets
      await loadTargets()
    } catch (error) {
      setErrors({ submit: `Failed to add target: ${error}` })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteTarget = async () => {
    if (!selectedTarget) return

    try {
      setLoading(true)
      await DeleteTarget(selectedTarget.target_id)
      setDeleteDialogOpen(false)
      setSelectedTarget(null)
      await loadTargets()
    } catch (error) {
      console.error("Failed to delete target:", error)
    } finally {
      setLoading(false)
    }
  }

  const openDeleteDialog = React.useCallback((target: Target) => {
    setSelectedTarget(target)
    setDeleteDialogOpen(true)
  }, [])

  // Create columns with delete handler
  const columns = React.useMemo(
    () => createColumns({ onDelete: openDeleteDialog }),
    [openDeleteDialog]
  )

  return (
    <>
      {/* Data Table */}
      <DataTable
        columns={columns}
        data={targets}
        loading={loading}
        refreshing={refreshing}
        onAddTarget={() => setAddDialogOpen(true)}
        onRefresh={handleRefresh}
      />

      {/* Add Target Dialog */}
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Target</DialogTitle>
            <DialogDescription>
              Add a new target for security scanning. Enter the target URL or IP address.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="address">Target Address *</Label>
              <Input
                id="address"
                placeholder="https://example.com or *************"
                value={formData.address}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, address: e.target.value }))
                  if (errors.address) setErrors(prev => ({ ...prev, address: "" }))
                }}
              />
              {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                placeholder="Optional description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="criticality">Criticality Level</Label>
              <Select
                value={formData.criticality.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, criticality: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">Normal (10)</SelectItem>
                  <SelectItem value="20">High (20)</SelectItem>
                  <SelectItem value="30">Critical (30)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {errors.submit && (
              <p className="text-sm text-red-500">{errors.submit}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setAddDialogOpen(false)
                setFormData({ address: "", description: "", criticality: 10 })
                setErrors({})
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button onClick={handleAddTarget} disabled={loading}>
              {loading ? "Adding..." : "Add Target"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Target</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the target &quot;{selectedTarget?.address}&quot;? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false)
                setSelectedTarget(null)
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTarget}
              disabled={loading}
            >
              {loading ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
