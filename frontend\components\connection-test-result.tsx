import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Loader2, CheckCircle, XCircle, Clock } from "lucide-react"
import { main } from "@/wailsjs/go/models"

interface ConnectionTestResultProps {
  formData: {
    name: string
    url: string
    api_key: string
  }
  disabled?: boolean
}

export function ConnectionTestResult({ formData, disabled = false }: ConnectionTestResultProps) {
  const [isTesting, setIsTesting] = React.useState(false)
  const [testResult, setTestResult] = React.useState<main.BackendConnectionTest | null>(null)
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  const handleTestConnection = async () => {
    if (!formData.url || !formData.api_key) {
      setErrors({
        url: !formData.url ? "URL is required for testing" : "",
        api_key: !formData.api_key ? "API Key is required for testing" : "",
      })
      return
    }

    setIsTesting(true)
    setTestResult(null)
    setErrors({})

    try {
      const { TestBackendConnectionDirect } = await import("@/wailsjs/go/main/App")
      const result = await TestBackendConnectionDirect({
        name: formData.name || "Test",
        url: formData.url,
        api_key: formData.api_key,
      })
      setTestResult(result)
    } catch (error) {
      const errorResult = new main.BackendConnectionTest({
        success: false,
        message: `Test failed: ${error}`,
        status: 0,
        latency: 0,
        tested_at: new Date(),
      })
      setTestResult(errorResult)
    } finally {
      setIsTesting(false)
    }
  }

  // Clear test result when form data changes
  React.useEffect(() => {
    setTestResult(null)
    setErrors({})
  }, [formData.url, formData.api_key])

  return (
    <div className="space-y-2">
      <Label>Connection Test</Label>
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleTestConnection}
        disabled={disabled || isTesting || !formData.url || !formData.api_key}
      >
        {isTesting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Testing...
          </>
        ) : (
          "Test Connection"
        )}
      </Button>

      {/* Test Result Display */}
      {testResult && (
        <div className={`p-3 rounded-md border ${
          testResult.success 
            ? "bg-green-50 border-green-200 text-green-800" 
            : "bg-red-50 border-red-200 text-red-800"
        }`}>
          <div className="flex items-center gap-2">
            {testResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <span className="font-medium">
              {testResult.success ? "Connection Successful" : "Connection Failed"}
            </span>
          </div>
          
          <p className="text-sm mt-1">{testResult.message}</p>
          
          {testResult.success && (
            <div className="flex items-center gap-4 mt-2 text-xs">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Latency: {testResult.latency}ms</span>
              </div>
              <div>Status: {testResult.status}</div>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {(errors.url || errors.api_key) && (
        <div className="text-sm text-red-500">
          {errors.url && <p>{errors.url}</p>}
          {errors.api_key && <p>{errors.api_key}</p>}
        </div>
      )}
    </div>
  )
}
