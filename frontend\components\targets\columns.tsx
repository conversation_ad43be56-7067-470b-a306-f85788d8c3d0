"use client"

import { ColumnDef } from "@tanstack/react-table"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ArrowUpDown, MoreHorizontal, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react"
import { main } from "@/wailsjs/go/models"

export type Target = main.TargetItemResponse

interface ColumnsProps {
  onDelete: (target: Target) => void
}

export const createColumns = ({ onDelete }: ColumnsProps): ColumnDef<Target>[] => [
  {
    accessorKey: "address",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Address
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const address = row.getValue("address") as string
      return (
        <div className="font-medium">
          {address}
        </div>
      )
    },
  },
  {
    accessorKey: "description",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Description
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const description = row.getValue("description") as string
      return (
        <div className="max-w-[200px] truncate">
          {description || "-"}
        </div>
      )
    },
  },
  {
    id: "vulnerabilities",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Vulnerabilities
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const target = row.original
      const severityCounts = target.severity_counts

      if (!severityCounts) {
        return (
          <div className="text-muted-foreground">
            No scan data
          </div>
        )
      }

      const total = severityCounts.critical + severityCounts.high + severityCounts.medium + severityCounts.low + severityCounts.info

      if (total === 0) {
        return (
          <Badge variant="outline" className="flex items-center gap-1 w-fit">
            <CheckCircle className="h-3 w-3 text-green-500" />
            Clean
          </Badge>
        )
      }

      return (
        <div className="flex flex-col gap-1">
          <div className="flex gap-1 text-xs">
            {severityCounts.critical > 0 && (
              <Badge
                variant="outline"
                className="px-1 py-0 text-xs border-0"
                style={{
                  backgroundColor: '#f3e8f1',
                  color: '#883678'
                }}
              >
                {severityCounts.critical}C
              </Badge>
            )}
            {severityCounts.high > 0 && (
              <Badge
                variant="outline"
                className="px-1 py-0 text-xs border-0"
                style={{
                  backgroundColor: '#fdeaea',
                  color: '#d94e4e'
                }}
              >
                {severityCounts.high}H
              </Badge>
            )}
            {severityCounts.medium > 0 && (
              <Badge
                variant="outline"
                className="px-1 py-0 text-xs border-0"
                style={{
                  backgroundColor: '#fdf0e8',
                  color: '#d65f3c'
                }}
              >
                {severityCounts.medium}M
              </Badge>
            )}
            {severityCounts.low > 0 && (
              <Badge
                variant="outline"
                className="px-1 py-0 text-xs border-0"
                style={{
                  backgroundColor: '#fdf5e8',
                  color: '#bf7534'
                }}
              >
                {severityCounts.low}L
              </Badge>
            )}
            {severityCounts.info > 0 && (
              <Badge
                variant="outline"
                className="px-1 py-0 text-xs border-0"
                style={{
                  backgroundColor: '#eef0f7',
                  color: '#4c5595'
                }}
              >
                {severityCounts.info}I
              </Badge>
            )}
          </div>
          <div className="text-xs text-muted-foreground">
            Total: {total}
          </div>
        </div>
      )
    },
    sortingFn: (rowA, rowB) => {
      const aTarget = rowA.original
      const bTarget = rowB.original
      const aSeverity = aTarget.severity_counts
      const bSeverity = bTarget.severity_counts

      if (!aSeverity && !bSeverity) return 0
      if (!aSeverity) return 1
      if (!bSeverity) return -1

      // Calculate weighted threat score (higher weights for more severe vulnerabilities)
      const calculateThreatScore = (severity: { critical: number; high: number; medium: number; low: number; info: number }) => {
        return (severity.critical * 1000) +
               (severity.high * 100) +
               (severity.medium * 10) +
               (severity.low * 1) +
               (severity.info * 0.1)
      }

      const aThreatScore = calculateThreatScore(aSeverity)
      const bThreatScore = calculateThreatScore(bSeverity)

      return bThreatScore - aThreatScore // Descending order (higher threat first)
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as string
      return (
        <Badge variant="outline">
          {type || "default"}
        </Badge>
      )
    },
  },
  {
    id: "last_scan_status",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Last Scan Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const target = row.original
      const lastScanDate = target.last_scan_date
      const lastScanStatus = target.last_scan_session_status

      if (!lastScanDate) {
        return (
          <div className="flex items-center gap-2">
            <XCircle className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Never scanned</span>
          </div>
        )
      }

      const formatDateTime = (dateStr: string) => {
        try {
          const date = new Date(dateStr)
          const dateOptions: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }
          const timeOptions: Intl.DateTimeFormatOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }

          const formattedDate = date.toLocaleDateString('en-CA', dateOptions) // YYYY-MM-DD format
          const formattedTime = date.toLocaleTimeString('en-GB', timeOptions) // HH:MM:SS format

          return {
            date: formattedDate,
            time: formattedTime,
            full: `${formattedDate} ${formattedTime}`
          }
        } catch {
          return {
            date: dateStr,
            time: '',
            full: dateStr
          }
        }
      }

      const getStatusBadge = (status: string | undefined) => {
        switch (status?.toLowerCase()) {
          case 'completed':
            return (
              <Badge variant="outline" className="flex items-center gap-1 w-fit text-green-700 border-green-200">
                <CheckCircle className="h-3 w-3" />
                Completed
              </Badge>
            )
          case 'running':
          case 'scanning':
            return (
              <Badge variant="outline" className="flex items-center gap-1 w-fit text-blue-700 border-blue-200">
                <Clock className="h-3 w-3" />
                Running
              </Badge>
            )
          case 'failed':
          case 'error':
            return (
              <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                <AlertCircle className="h-3 w-3" />
                Failed
              </Badge>
            )
          case 'aborted':
          case 'cancelled':
            return (
              <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                <XCircle className="h-3 w-3" />
                Aborted
              </Badge>
            )
          default:
            return (
              <Badge variant="outline" className="flex items-center gap-1 w-fit">
                <AlertCircle className="h-3 w-3" />
                {status || 'Unknown'}
              </Badge>
            )
        }
      }

      return (
        <div className="flex flex-col gap-1">
          {getStatusBadge(lastScanStatus)}
          <div className="text-xs text-muted-foreground">
            {formatDateTime(lastScanDate).date} {formatDateTime(lastScanDate).time}
          </div>
        </div>
      )
    },
    sortingFn: (rowA, rowB) => {
      const aTarget = rowA.original
      const bTarget = rowB.original
      const aDate = aTarget.last_scan_date
      const bDate = bTarget.last_scan_date

      if (!aDate && !bDate) return 0
      if (!aDate) return 1
      if (!bDate) return -1

      return new Date(bDate).getTime() - new Date(aDate).getTime() // Most recent first
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const target = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0" size="icon">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(target.address)}
            >
              Copy address
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(target)}
              className="text-red-600 focus:text-red-600"
            >
              Delete target
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
