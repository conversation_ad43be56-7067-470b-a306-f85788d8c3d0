"use client"

import { ColumnDef } from "@tanstack/react-table"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ArrowUpDown, MoreHorizontal, Globe, Shield, AlertTriangle, Trash2 } from "lucide-react"
import { main } from "@/wailsjs/go/models"

export type Target = main.Target

interface ColumnsProps {
  onDelete: (target: Target) => void
}

export const createColumns = ({ onDelete }: ColumnsProps): ColumnDef<Target>[] => [
  {
    accessorKey: "address",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Address
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const address = row.getValue("address") as string
      return (
        <div className="font-medium">
          {address}
        </div>
      )
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string
      return (
        <div className="max-w-[200px] truncate">
          {description || "-"}
        </div>
      )
    },
  },
  {
    accessorKey: "criticality",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2 lg:px-3"
        >
          Criticality
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const criticality = row.getValue("criticality") as number
      
      if (criticality >= 30) {
        return (
          <Badge variant="destructive" className="flex items-center gap-1 w-fit">
            <AlertTriangle className="h-3 w-3" />
            Critical
          </Badge>
        )
      } else if (criticality >= 20) {
        return (
          <Badge variant="secondary" className="flex items-center gap-1 w-fit">
            <Shield className="h-3 w-3" />
            High
          </Badge>
        )
      } else if (criticality >= 10) {
        return (
          <Badge variant="outline" className="flex items-center gap-1 w-fit">
            <Globe className="h-3 w-3" />
            Normal
          </Badge>
        )
      } else {
        return <Badge variant="secondary">Low</Badge>
      }
    },
    sortingFn: (rowA, rowB) => {
      const a = rowA.getValue("criticality") as number
      const b = rowB.getValue("criticality") as number
      return a - b
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as string
      return (
        <Badge variant="outline">
          {type || "Web"}
        </Badge>
      )
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const target = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0" size="icon">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(target.address)}
            >
              Copy address
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(target)}
              className="text-red-600 focus:text-red-600"
            >
              Delete target
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
