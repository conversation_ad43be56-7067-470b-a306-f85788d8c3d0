(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,31713,e=>{"use strict";e.s(["default",()=>tZ],31713);var t=e.i(43476),l=e.i(71645),n=e.i(60313),o=e.i(91918),r=e.i(63059),i=e.i(41071),a=e.i(47163);function s(e){let{...l}=e;return(0,t.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...l})}function u(e){let{className:l,...n}=e;return(0,t.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,a.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",l),...n})}function d(e){let{className:l,...n}=e;return(0,t.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,a.cn)("inline-flex items-center gap-1.5",l),...n})}function g(e){let{asChild:l,className:n,...r}=e,i=l?o.Slot:"a";return(0,t.jsx)(i,{"data-slot":"breadcrumb-link",className:(0,a.cn)("hover:text-foreground transition-colors",n),...r})}function c(e){let{className:l,...n}=e;return(0,t.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,a.cn)("text-foreground font-normal",l),...n})}function p(e){let{children:l,className:n,...o}=e;return(0,t.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,a.cn)("[&>svg]:size-3.5",n),...o,children:null!=l?l:(0,t.jsx)(r.ChevronRight,{})})}var m=e.i(69035),f=e.i(11522),h=e.i(67881),v=e.i(23750),w=e.i(10708),x=e.i(30374),b=e.i(74080);function C(e,t){let[l,n]=t;return Math.min(n,Math.max(l,e))}var S=e.i(81140),R=e.i(75830),y=e.i(20783),j=e.i(30030),F=e.i(86318),M=e.i(26330),P=e.i(3536),I=e.i(65491),N=e.i(10772),V=e.i(53660),_=e.i(74606),E=e.i(48425),D=e.i(30207),L=e.i(69340),A=e.i(34620),H=e.i(59411),k=e.i(86312),T=e.i(85369),G=[" ","Enter","ArrowUp","ArrowDown"],z=[" ","Enter"],O="Select",[B,q,U]=(0,R.createCollection)(O),[K,W]=(0,j.createContextScope)(O,[U,V.createPopperScope]),X=(0,V.createPopperScope)(),[Y,$]=K(O),[Z,J]=K(O),Q=e=>{let{__scopeSelect:n,children:o,open:r,defaultOpen:i,onOpenChange:a,value:s,defaultValue:u,onValueChange:d,dir:g,name:c,autoComplete:p,disabled:m,required:f,form:h}=e,v=X(n),[w,x]=l.useState(null),[b,C]=l.useState(null),[S,R]=l.useState(!1),y=(0,F.useDirection)(g),[j,M]=(0,L.useControllableState)({prop:r,defaultProp:null!=i&&i,onChange:a,caller:O}),[P,I]=(0,L.useControllableState)({prop:s,defaultProp:u,onChange:d,caller:O}),_=l.useRef(null),E=!w||h||!!w.closest("form"),[D,A]=l.useState(new Set),H=Array.from(D).map(e=>e.props.value).join(";");return(0,t.jsx)(V.Root,{...v,children:(0,t.jsxs)(Y,{required:f,scope:n,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:C,valueNodeHasChildren:S,onValueNodeHasChildrenChange:R,contentId:(0,N.useId)(),value:P,onValueChange:I,open:j,onOpenChange:M,dir:y,triggerPointerDownPosRef:_,disabled:m,children:[(0,t.jsx)(B.Provider,{scope:n,children:(0,t.jsx)(Z,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{A(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{A(t=>{let l=new Set(t);return l.delete(e),l})},[]),children:o})}),E?(0,t.jsxs)(ek,{"aria-hidden":!0,required:f,tabIndex:-1,name:c,autoComplete:p,value:P,onChange:e=>I(e.target.value),disabled:m,form:h,children:[void 0===P?(0,t.jsx)("option",{value:""}):null,Array.from(D)]},H):null]})})};Q.displayName=O;var ee="SelectTrigger",et=l.forwardRef((e,n)=>{let{__scopeSelect:o,disabled:r=!1,...i}=e,a=X(o),s=$(ee,o),u=s.disabled||r,d=(0,y.useComposedRefs)(n,s.onTriggerChange),g=q(o),c=l.useRef("touch"),[p,m,f]=eG(e=>{let t=g().filter(e=>!e.disabled),l=t.find(e=>e.value===s.value),n=ez(t,e,l);void 0!==n&&s.onValueChange(n.value)}),h=e=>{u||(s.onOpenChange(!0),f()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,t.jsx)(V.Anchor,{asChild:!0,...a,children:(0,t.jsx)(E.Primitive.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(s.value)?"":void 0,...i,ref:d,onClick:(0,S.composeEventHandlers)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==c.current&&h(e)}),onPointerDown:(0,S.composeEventHandlers)(i.onPointerDown,e=>{c.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(h(e),e.preventDefault())}),onKeyDown:(0,S.composeEventHandlers)(i.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&G.includes(e.key)&&(h(),e.preventDefault())})})})});et.displayName=ee;var el="SelectValue",en=l.forwardRef((e,l)=>{let{__scopeSelect:n,className:o,style:r,children:i,placeholder:a="",...s}=e,u=$(el,n),{onValueNodeHasChildrenChange:d}=u,g=void 0!==i,c=(0,y.useComposedRefs)(l,u.onValueNodeChange);return(0,A.useLayoutEffect)(()=>{d(g)},[d,g]),(0,t.jsx)(E.Primitive.span,{...s,ref:c,style:{pointerEvents:"none"},children:eT(u.value)?(0,t.jsx)(t.Fragment,{children:a}):i})});en.displayName=el;var eo=l.forwardRef((e,l)=>{let{__scopeSelect:n,children:o,...r}=e;return(0,t.jsx)(E.Primitive.span,{"aria-hidden":!0,...r,ref:l,children:o||"▼"})});eo.displayName="SelectIcon";var er=e=>(0,t.jsx)(_.Portal,{asChild:!0,...e});er.displayName="SelectPortal";var ei="SelectContent",ea=l.forwardRef((e,n)=>{let o=$(ei,e.__scopeSelect),[r,i]=l.useState();return((0,A.useLayoutEffect)(()=>{i(new DocumentFragment)},[]),o.open)?(0,t.jsx)(eg,{...e,ref:n}):r?b.createPortal((0,t.jsx)(es,{scope:e.__scopeSelect,children:(0,t.jsx)(B.Slot,{scope:e.__scopeSelect,children:(0,t.jsx)("div",{children:e.children})})}),r):null});ea.displayName=ei;var[es,eu]=K(ei),ed=(0,o.createSlot)("SelectContent.RemoveScroll"),eg=l.forwardRef((e,n)=>{let{__scopeSelect:o,position:r="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:u,sideOffset:d,align:g,alignOffset:c,arrowPadding:p,collisionBoundary:m,collisionPadding:f,sticky:h,hideWhenDetached:v,avoidCollisions:w,...x}=e,b=$(ei,o),[C,R]=l.useState(null),[j,F]=l.useState(null),N=(0,y.useComposedRefs)(n,e=>R(e)),[V,_]=l.useState(null),[E,D]=l.useState(null),L=q(o),[A,H]=l.useState(!1),G=l.useRef(!1);l.useEffect(()=>{if(C)return(0,k.hideOthers)(C)},[C]),(0,P.useFocusGuards)();let z=l.useCallback(e=>{let[t,...l]=L().map(e=>e.ref.current),[n]=l.slice(-1),o=document.activeElement;for(let l of e)if(l===o||(null==l||l.scrollIntoView({block:"nearest"}),l===t&&j&&(j.scrollTop=0),l===n&&j&&(j.scrollTop=j.scrollHeight),null==l||l.focus(),document.activeElement!==o))return},[L,j]),O=l.useCallback(()=>z([V,C]),[z,V,C]);l.useEffect(()=>{A&&O()},[A,O]);let{onOpenChange:B,triggerPointerDownPosRef:U}=b;l.useEffect(()=>{if(C){let e={x:0,y:0},t=t=>{var l,n,o,r;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(l=U.current)?void 0:l.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(r=null==(n=U.current)?void 0:n.y)?r:0))}},l=l=>{e.x<=10&&e.y<=10?l.preventDefault():C.contains(l.target)||B(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",l,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",l,{capture:!0})}}},[C,B,U]),l.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[K,W]=eG(e=>{let t=L().filter(e=>!e.disabled),l=t.find(e=>e.ref.current===document.activeElement),n=ez(t,e,l);n&&setTimeout(()=>n.ref.current.focus())}),X=l.useCallback((e,t,l)=>{let n=!G.current&&!l;(void 0!==b.value&&b.value===t||n)&&(_(e),n&&(G.current=!0))},[b.value]),Y=l.useCallback(()=>null==C?void 0:C.focus(),[C]),Z=l.useCallback((e,t,l)=>{let n=!G.current&&!l;(void 0!==b.value&&b.value===t||n)&&D(e)},[b.value]),J="popper"===r?ep:ec,Q=J===ep?{side:u,sideOffset:d,align:g,alignOffset:c,arrowPadding:p,collisionBoundary:m,collisionPadding:f,sticky:h,hideWhenDetached:v,avoidCollisions:w}:{};return(0,t.jsx)(es,{scope:o,content:C,viewport:j,onViewportChange:F,itemRefCallback:X,selectedItem:V,onItemLeave:Y,itemTextRefCallback:Z,focusSelectedItem:O,selectedItemText:E,position:r,isPositioned:A,searchRef:K,children:(0,t.jsx)(T.RemoveScroll,{as:ed,allowPinchZoom:!0,children:(0,t.jsx)(I.FocusScope,{asChild:!0,trapped:b.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,S.composeEventHandlers)(i,e=>{var t;null==(t=b.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,t.jsx)(M.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>b.onOpenChange(!1),children:(0,t.jsx)(J,{role:"listbox",id:b.contentId,"data-state":b.open?"open":"closed",dir:b.dir,onContextMenu:e=>e.preventDefault(),...x,...Q,onPlaced:()=>H(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:(0,S.composeEventHandlers)(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||W(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=L().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let l=e.target,n=t.indexOf(l);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});eg.displayName="SelectContentImpl";var ec=l.forwardRef((e,n)=>{let{__scopeSelect:o,onPlaced:r,...i}=e,a=$(ei,o),s=eu(ei,o),[u,d]=l.useState(null),[g,c]=l.useState(null),p=(0,y.useComposedRefs)(n,e=>c(e)),m=q(o),f=l.useRef(!1),h=l.useRef(!0),{viewport:v,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=s,S=l.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&g&&v&&w&&x){let e=a.trigger.getBoundingClientRect(),t=g.getBoundingClientRect(),l=a.valueNode.getBoundingClientRect(),n=x.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,r=l.left-o,i=e.left-r,a=e.width+i,s=Math.max(a,t.width),d=C(r,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let o=t.right-n.right,r=window.innerWidth-l.right-o,i=window.innerWidth-e.right-r,a=e.width+i,s=Math.max(a,t.width),d=C(r,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let o=m(),i=window.innerHeight-20,s=v.scrollHeight,d=window.getComputedStyle(g),c=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),h=parseInt(d.borderBottomWidth,10),b=c+p+s+parseInt(d.paddingBottom,10)+h,S=Math.min(5*w.offsetHeight,b),R=window.getComputedStyle(v),y=parseInt(R.paddingTop,10),j=parseInt(R.paddingBottom,10),F=e.top+e.height/2-10,M=w.offsetHeight/2,P=c+p+(w.offsetTop+M);if(P<=F){let e=o.length>0&&w===o[o.length-1].ref.current;u.style.bottom="0px";let t=Math.max(i-F,M+(e?j:0)+(g.clientHeight-v.offsetTop-v.offsetHeight)+h);u.style.height=P+t+"px"}else{let e=o.length>0&&w===o[0].ref.current;u.style.top="0px";let t=Math.max(F,c+v.offsetTop+(e?y:0)+M);u.style.height=t+(b-P)+"px",v.scrollTop=P-F+v.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=S+"px",u.style.maxHeight=i+"px",null==r||r(),requestAnimationFrame(()=>f.current=!0)}},[m,a.trigger,a.valueNode,u,g,v,w,x,a.dir,r]);(0,A.useLayoutEffect)(()=>S(),[S]);let[R,j]=l.useState();(0,A.useLayoutEffect)(()=>{g&&j(window.getComputedStyle(g).zIndex)},[g]);let F=l.useCallback(e=>{e&&!0===h.current&&(S(),null==b||b(),h.current=!1)},[S,b]);return(0,t.jsx)(em,{scope:o,contentWrapper:u,shouldExpandOnScrollRef:f,onScrollButtonChange:F,children:(0,t.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,t.jsx)(E.Primitive.div,{...i,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});ec.displayName="SelectItemAlignedPosition";var ep=l.forwardRef((e,l)=>{let{__scopeSelect:n,align:o="start",collisionPadding:r=10,...i}=e,a=X(n);return(0,t.jsx)(V.Content,{...a,...i,ref:l,align:o,collisionPadding:r,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ep.displayName="SelectPopperPosition";var[em,ef]=K(ei,{}),eh="SelectViewport",ev=l.forwardRef((e,n)=>{let{__scopeSelect:o,nonce:r,...i}=e,a=eu(eh,o),s=ef(eh,o),u=(0,y.useComposedRefs)(n,a.onViewportChange),d=l.useRef(0);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,t.jsx)(B.Slot,{scope:o,children:(0,t.jsx)(E.Primitive.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,S.composeEventHandlers)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:l,shouldExpandOnScrollRef:n}=s;if((null==n?void 0:n.current)&&l){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(l.style.minHeight),parseFloat(l.style.height));if(o<n){let r=o+e,i=Math.min(n,r),a=r-i;l.style.height=i+"px","0px"===l.style.bottom&&(t.scrollTop=a>0?a:0,l.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});ev.displayName=eh;var ew="SelectGroup",[ex,eb]=K(ew);l.forwardRef((e,l)=>{let{__scopeSelect:n,...o}=e,r=(0,N.useId)();return(0,t.jsx)(ex,{scope:n,id:r,children:(0,t.jsx)(E.Primitive.div,{role:"group","aria-labelledby":r,...o,ref:l})})}).displayName=ew;var eC="SelectLabel",eS=l.forwardRef((e,l)=>{let{__scopeSelect:n,...o}=e,r=eb(eC,n);return(0,t.jsx)(E.Primitive.div,{id:r.id,...o,ref:l})});eS.displayName=eC;var eR="SelectItem",[ey,ej]=K(eR),eF=l.forwardRef((e,n)=>{let{__scopeSelect:o,value:r,disabled:i=!1,textValue:a,...s}=e,u=$(eR,o),d=eu(eR,o),g=u.value===r,[c,p]=l.useState(null!=a?a:""),[m,f]=l.useState(!1),h=(0,y.useComposedRefs)(n,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,r,i)}),v=(0,N.useId)(),w=l.useRef("touch"),x=()=>{i||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,t.jsx)(ey,{scope:o,value:r,disabled:i,textId:v,isSelected:g,onItemTextChange:l.useCallback(e=>{p(t=>{var l;return t||(null!=(l=null==e?void 0:e.textContent)?l:"").trim()})},[]),children:(0,t.jsx)(B.ItemSlot,{scope:o,value:r,disabled:i,textValue:c,children:(0,t.jsx)(E.Primitive.div,{role:"option","aria-labelledby":v,"data-highlighted":m?"":void 0,"aria-selected":g&&m,"data-state":g?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:h,onFocus:(0,S.composeEventHandlers)(s.onFocus,()=>f(!0)),onBlur:(0,S.composeEventHandlers)(s.onBlur,()=>f(!1)),onClick:(0,S.composeEventHandlers)(s.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,S.composeEventHandlers)(s.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,S.composeEventHandlers)(s.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,S.composeEventHandlers)(s.onPointerMove,e=>{if(w.current=e.pointerType,i){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,S.composeEventHandlers)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,S.composeEventHandlers)(s.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(z.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});eF.displayName=eR;var eM="SelectItemText",eP=l.forwardRef((e,n)=>{let{__scopeSelect:o,className:r,style:i,...a}=e,s=$(eM,o),u=eu(eM,o),d=ej(eM,o),g=J(eM,o),[c,p]=l.useState(null),m=(0,y.useComposedRefs)(n,e=>p(e),d.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,d.value,d.disabled)}),f=null==c?void 0:c.textContent,h=l.useMemo(()=>(0,t.jsx)("option",{value:d.value,disabled:d.disabled,children:f},d.value),[d.disabled,d.value,f]),{onNativeOptionAdd:v,onNativeOptionRemove:w}=g;return(0,A.useLayoutEffect)(()=>(v(h),()=>w(h)),[v,w,h]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(E.Primitive.span,{id:d.textId,...a,ref:m}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?b.createPortal(a.children,s.valueNode):null]})});eP.displayName=eM;var eI="SelectItemIndicator",eN=l.forwardRef((e,l)=>{let{__scopeSelect:n,...o}=e;return ej(eI,n).isSelected?(0,t.jsx)(E.Primitive.span,{"aria-hidden":!0,...o,ref:l}):null});eN.displayName=eI;var eV="SelectScrollUpButton",e_=l.forwardRef((e,n)=>{let o=eu(eV,e.__scopeSelect),r=ef(eV,e.__scopeSelect),[i,a]=l.useState(!1),s=(0,y.useComposedRefs)(n,r.onScrollButtonChange);return(0,A.useLayoutEffect)(()=>{if(o.viewport&&o.isPositioned){let e=function(){a(t.scrollTop>0)},t=o.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[o.viewport,o.isPositioned]),i?(0,t.jsx)(eL,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=o;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});e_.displayName=eV;var eE="SelectScrollDownButton",eD=l.forwardRef((e,n)=>{let o=eu(eE,e.__scopeSelect),r=ef(eE,e.__scopeSelect),[i,a]=l.useState(!1),s=(0,y.useComposedRefs)(n,r.onScrollButtonChange);return(0,A.useLayoutEffect)(()=>{if(o.viewport&&o.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=o.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[o.viewport,o.isPositioned]),i?(0,t.jsx)(eL,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=o;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eD.displayName=eE;var eL=l.forwardRef((e,n)=>{let{__scopeSelect:o,onAutoScroll:r,...i}=e,a=eu("SelectScrollButton",o),s=l.useRef(null),u=q(o),d=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,A.useLayoutEffect)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,t.jsx)(E.Primitive.div,{"aria-hidden":!0,...i,ref:n,style:{flexShrink:0,...i.style},onPointerDown:(0,S.composeEventHandlers)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,S.composeEventHandlers)(i.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,S.composeEventHandlers)(i.onPointerLeave,()=>{d()})})}),eA=l.forwardRef((e,l)=>{let{__scopeSelect:n,...o}=e;return(0,t.jsx)(E.Primitive.div,{"aria-hidden":!0,...o,ref:l})});eA.displayName="SelectSeparator";var eH="SelectArrow";l.forwardRef((e,l)=>{let{__scopeSelect:n,...o}=e,r=X(n),i=$(eH,n),a=eu(eH,n);return i.open&&"popper"===a.position?(0,t.jsx)(V.Arrow,{...r,...o,ref:l}):null}).displayName=eH;var ek=l.forwardRef((e,n)=>{let{__scopeSelect:o,value:r,...i}=e,a=l.useRef(null),s=(0,y.useComposedRefs)(n,a),u=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return l.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let l=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(l)}},[u,r]),(0,t.jsx)(E.Primitive.select,{...i,style:{...H.VISUALLY_HIDDEN_STYLES,...i.style},ref:s,defaultValue:r})});function eT(e){return""===e||void 0===e}function eG(e){let t=(0,D.useCallbackRef)(e),n=l.useRef(""),o=l.useRef(0),r=l.useCallback(e=>{let l=n.current+e;t(l),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,i]}function ez(e,t,l){var n,o;let r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=l?e.indexOf(l):-1,a=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===r.length&&(a=a.filter(e=>e!==l));let s=a.find(e=>e.textValue.toLowerCase().startsWith(r.toLowerCase()));return s!==l?s:void 0}ek.displayName="SelectBubbleInput";var eO=e.i(78745),eO=eO,eB=e.i(75254);let eq=(0,eB.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),eU=(0,eB.default)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),eK=l.forwardRef((e,l)=>{let{className:n,children:o,...r}=e;return(0,t.jsxs)(et,{ref:l,className:(0,a.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),...r,children:[o,(0,t.jsx)(eo,{asChild:!0,children:(0,t.jsx)(eq,{className:"h-4 w-4 opacity-50"})})]})});eK.displayName=et.displayName;let eW=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)(e_,{ref:l,className:(0,a.cn)("flex cursor-default items-center justify-center py-1",n),...o,children:(0,t.jsx)(eU,{className:"h-4 w-4"})})});eW.displayName=e_.displayName;let eX=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)(eD,{ref:l,className:(0,a.cn)("flex cursor-default items-center justify-center py-1",n),...o,children:(0,t.jsx)(eq,{className:"h-4 w-4"})})});eX.displayName=eD.displayName;let eY=l.forwardRef((e,l)=>{let{className:n,children:o,position:r="popper",...i}=e;return(0,t.jsx)(er,{children:(0,t.jsxs)(ea,{ref:l,className:(0,a.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:r,...i,children:[(0,t.jsx)(eW,{}),(0,t.jsx)(ev,{className:(0,a.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:o}),(0,t.jsx)(eX,{})]})})});eY.displayName=ea.displayName,l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)(eS,{ref:l,className:(0,a.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",n),...o})}).displayName=eS.displayName;let e$=l.forwardRef((e,l)=>{let{className:n,children:o,...r}=e;return(0,t.jsxs)(eF,{ref:l,className:(0,a.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(eN,{children:(0,t.jsx)(eO.default,{className:"h-4 w-4"})})}),(0,t.jsx)(eP,{children:o})]})});e$.displayName=eF.displayName,l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)(eA,{ref:l,className:(0,a.cn)("-mx-1 my-1 h-px bg-muted",n),...o})}).displayName=eA.displayName;var eZ=e.i(65759);function eJ(e,t){return"function"==typeof e?e(t):e}function eQ(e,t){return l=>{t.setState(t=>({...t,[e]:eJ(l,t[e])}))}}function e0(e){return e instanceof Function}function e1(e,t,l){let n,o=[];return r=>{let i,a;l.key&&l.debug&&(i=Date.now());let s=e(r);if(!(s.length!==o.length||s.some((e,t)=>o[t]!==e)))return n;if(o=s,l.key&&l.debug&&(a=Date.now()),n=t(...s),null==l||null==l.onChange||l.onChange(n),l.key&&l.debug&&null!=l&&l.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-a)*100)/100,n=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info("%c⏱ ".concat(o(t,5)," /").concat(o(e,5)," ms"),"\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(".concat(Math.max(0,Math.min(120-120*n,120)),"deg 100% 31%);"),null==l?void 0:l.key)}return n}}function e2(e,t,l,n){return{debug:()=>{var l;return null!=(l=null==e?void 0:e.debugAll)?l:e[t]},key:!1,onChange:n}}e.i(47167);let e4="debugHeaders";function e3(e,t,l){var n;let o={id:null!=(n=l.id)?n:t.id,column:t,index:l.index,isPlaceholder:!!l.isPlaceholder,placeholderId:l.placeholderId,depth:l.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(t),e.push(l)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function e5(e,t,l,n){var o,r;let i=0,a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var l;null!=(l=e.columns)&&l.length&&a(e.columns,t+1)},0)};a(e);let s=[],u=(e,t)=>{let o={depth:t,id:[n,"".concat(t)].filter(Boolean).join("_"),headers:[]},r=[];e.forEach(e=>{let i,a=[...r].reverse()[0],s=e.column.depth===o.depth,u=!1;if(s&&e.column.parent?i=e.column.parent:(i=e.column,u=!0),a&&(null==a?void 0:a.column)===i)a.subHeaders.push(e);else{let o=e3(l,i,{id:[n,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:u,placeholderId:u?"".concat(r.filter(e=>e.column===i).length):void 0,depth:t,index:r.length});o.subHeaders.push(e),r.push(o)}o.headers.push(e),e.headerGroup=o}),s.push(o),t>0&&u(r,t-1)};u(t.map((e,t)=>e3(l,e,{depth:i,index:t})),i-1),s.reverse();let d=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,l=0,n=[0];return e.subHeaders&&e.subHeaders.length?(n=[],d(e.subHeaders).forEach(e=>{let{colSpan:l,rowSpan:o}=e;t+=l,n.push(o)})):t=1,l+=Math.min(...n),e.colSpan=t,e.rowSpan=l,{colSpan:t,rowSpan:l}});return d(null!=(o=null==(r=s[0])?void 0:r.headers)?o:[]),s}let e6=(e,t,l,n,o,r,i)=>{let a={id:t,index:n,original:l,depth:o,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(a._valuesCache.hasOwnProperty(t))return a._valuesCache[t];let l=e.getColumn(t);if(null!=l&&l.accessorFn)return a._valuesCache[t]=l.accessorFn(a.original,n),a._valuesCache[t]},getUniqueValues:t=>{if(a._uniqueValuesCache.hasOwnProperty(t))return a._uniqueValuesCache[t];let l=e.getColumn(t);if(null!=l&&l.accessorFn)return l.columnDef.getUniqueValues?a._uniqueValuesCache[t]=l.columnDef.getUniqueValues(a.original,n):a._uniqueValuesCache[t]=[a.getValue(t)],a._uniqueValuesCache[t]},renderValue:t=>{var l;return null!=(l=a.getValue(t))?l:e.options.renderFallbackValue},subRows:null!=r?r:[],getLeafRows:()=>(function(e,t){let l=[],n=e=>{e.forEach(e=>{l.push(e);let o=t(e);null!=o&&o.length&&n(o)})};return n(e),l})(a.subRows,e=>e.subRows),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let e=[],t=a;for(;;){let l=t.getParentRow();if(!l)break;e.push(l),t=l}return e.reverse()},getAllCells:e1(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,l,n){let o={id:"".concat(t.id,"_").concat(l.id),row:t,column:l,getValue:()=>t.getValue(n),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:e1(()=>[e,l,t,o],(e,t,l,n)=>({table:e,column:t,row:l,cell:n,getValue:n.getValue,renderValue:n.renderValue}),e2(e.options,"debugCells","cell.getContext"))};return e._features.forEach(n=>{null==n.createCell||n.createCell(o,l,t,e)},{}),o})(e,a,t,t.id)),e2(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:e1(()=>[a.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),e2(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let l=e._features[t];null==l||null==l.createRow||l.createRow(a,e)}return a},e8=(e,t,l)=>{var n,o;let r=null==l||null==(n=l.toString())?void 0:n.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(r))};e8.autoRemove=e=>ta(e);let e7=(e,t,l)=>{var n;return!!(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.includes(l))};e7.autoRemove=e=>ta(e);let e9=(e,t,l)=>{var n;return(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.toLowerCase())===(null==l?void 0:l.toLowerCase())};e9.autoRemove=e=>ta(e);let te=(e,t,l)=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(l)};te.autoRemove=e=>ta(e);let tt=(e,t,l)=>!l.some(l=>{var n;return!(null!=(n=e.getValue(t))&&n.includes(l))});tt.autoRemove=e=>ta(e)||!(null!=e&&e.length);let tl=(e,t,l)=>l.some(l=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(l)});tl.autoRemove=e=>ta(e)||!(null!=e&&e.length);let tn=(e,t,l)=>e.getValue(t)===l;tn.autoRemove=e=>ta(e);let to=(e,t,l)=>e.getValue(t)==l;to.autoRemove=e=>ta(e);let tr=(e,t,l)=>{let[n,o]=l,r=e.getValue(t);return r>=n&&r<=o};tr.resolveFilterValue=e=>{let[t,l]=e,n="number"!=typeof t?parseFloat(t):t,o="number"!=typeof l?parseFloat(l):l,r=null===t||Number.isNaN(n)?-1/0:n,i=null===l||Number.isNaN(o)?1/0:o;if(r>i){let e=r;r=i,i=e}return[r,i]},tr.autoRemove=e=>ta(e)||ta(e[0])&&ta(e[1]);let ti={includesString:e8,includesStringSensitive:e7,equalsString:e9,arrIncludes:te,arrIncludesAll:tt,arrIncludesSome:tl,equals:tn,weakEquals:to,inNumberRange:tr};function ta(e){return null==e||""===e}function ts(e,t,l){return!!e&&!!e.autoRemove&&e.autoRemove(t,l)||void 0===t||"string"==typeof t&&!t}let tu={sum:(e,t,l)=>l.reduce((t,l)=>{let n=l.getValue(e);return t+("number"==typeof n?n:0)},0),min:(e,t,l)=>{let n;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(n>l||void 0===n&&l>=l)&&(n=l)}),n},max:(e,t,l)=>{let n;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(n<l||void 0===n&&l>=l)&&(n=l)}),n},extent:(e,t,l)=>{let n,o;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(void 0===n?l>=l&&(n=o=l):(n>l&&(n=l),o<l&&(o=l)))}),[n,o]},mean:(e,t)=>{let l=0,n=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o*=1)>=o&&(++l,n+=o)}),l)return n/l},median:(e,t)=>{if(!t.length)return;let l=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(l))return;if(1===l.length)return l[0];let n=Math.floor(l.length/2),o=l.sort((e,t)=>e-t);return l.length%2!=0?o[n]:(o[n-1]+o[n])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},td=()=>({left:[],right:[]}),tg={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},tc=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),tp=null;function tm(e){return"touchstart"===e.type}function tf(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let th=()=>({pageIndex:0,pageSize:10}),tv=()=>({top:[],bottom:[]}),tw=(e,t,l,n,o)=>{var r;let i=o.getRow(t,!0);l?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],n&&null!=(r=i.subRows)&&r.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>tw(e,t.id,l,n,o))};function tx(e,t){let l=e.getState().rowSelection,n=[],o={},r=function(e,t){return e.map(e=>{var t;let i=tb(e,l);if(i&&(n.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:r(e.subRows)}),i)return e}).filter(Boolean)};return{rows:r(t.rows),flatRows:n,rowsById:o}}function tb(e,t){var l;return null!=(l=t[e.id])&&l}function tC(e,t,l){var n;if(!(null!=(n=e.subRows)&&n.length))return!1;let o=!0,r=!1;return e.subRows.forEach(e=>{if((!r||o)&&(e.getCanSelect()&&(tb(e,t)?r=!0:o=!1),e.subRows&&e.subRows.length)){let l=tC(e,t);"all"===l?r=!0:("some"===l&&(r=!0),o=!1)}}),o?"all":!!r&&"some"}let tS=/([0-9]+)/gm;function tR(e,t){return e===t?0:e>t?1:-1}function ty(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function tj(e,t){let l=e.split(tS).filter(Boolean),n=t.split(tS).filter(Boolean);for(;l.length&&n.length;){let e=l.shift(),t=n.shift(),o=parseInt(e,10),r=parseInt(t,10),i=[o,r].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>r)return 1;if(r>o)return -1}return l.length-n.length}let tF={alphanumeric:(e,t,l)=>tj(ty(e.getValue(l)).toLowerCase(),ty(t.getValue(l)).toLowerCase()),alphanumericCaseSensitive:(e,t,l)=>tj(ty(e.getValue(l)),ty(t.getValue(l))),text:(e,t,l)=>tR(ty(e.getValue(l)).toLowerCase(),ty(t.getValue(l)).toLowerCase()),textCaseSensitive:(e,t,l)=>tR(ty(e.getValue(l)),ty(t.getValue(l))),datetime:(e,t,l)=>{let n=e.getValue(l),o=t.getValue(l);return n>o?1:n<o?-1:0},basic:(e,t,l)=>tR(e.getValue(l),t.getValue(l))},tM=[{createTable:e=>{e.getHeaderGroups=e1(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,l,n,o)=>{var r,i;let a=null!=(r=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?r:[],s=null!=(i=null==o?void 0:o.map(e=>l.find(t=>t.id===e)).filter(Boolean))?i:[];return e5(t,[...a,...l.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=o&&o.includes(e.id))),...s],e)},e2(e.options,e4,"getHeaderGroups")),e.getCenterHeaderGroups=e1(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,l,n,o)=>e5(t,l=l.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),e2(e.options,e4,"getCenterHeaderGroups")),e.getLeftHeaderGroups=e1(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,l,n)=>{var o;return e5(t,null!=(o=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},e2(e.options,e4,"getLeftHeaderGroups")),e.getRightHeaderGroups=e1(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,l,n)=>{var o;return e5(t,null!=(o=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},e2(e.options,e4,"getRightHeaderGroups")),e.getFooterGroups=e1(()=>[e.getHeaderGroups()],e=>[...e].reverse(),e2(e.options,e4,"getFooterGroups")),e.getLeftFooterGroups=e1(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),e2(e.options,e4,"getLeftFooterGroups")),e.getCenterFooterGroups=e1(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),e2(e.options,e4,"getCenterFooterGroups")),e.getRightFooterGroups=e1(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),e2(e.options,e4,"getRightFooterGroups")),e.getFlatHeaders=e1(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),e2(e.options,e4,"getFlatHeaders")),e.getLeftFlatHeaders=e1(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),e2(e.options,e4,"getLeftFlatHeaders")),e.getCenterFlatHeaders=e1(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),e2(e.options,e4,"getCenterFlatHeaders")),e.getRightFlatHeaders=e1(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),e2(e.options,e4,"getRightFlatHeaders")),e.getCenterLeafHeaders=e1(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),e2(e.options,e4,"getCenterLeafHeaders")),e.getLeftLeafHeaders=e1(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),e2(e.options,e4,"getLeftLeafHeaders")),e.getRightLeafHeaders=e1(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),e2(e.options,e4,"getRightLeafHeaders")),e.getLeafHeaders=e1(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,l)=>{var n,o,r,i,a,s;return[...null!=(n=null==(o=e[0])?void 0:o.headers)?n:[],...null!=(r=null==(i=t[0])?void 0:i.headers)?r:[],...null!=(a=null==(s=l[0])?void 0:s.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},e2(e.options,e4,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:eQ("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=l=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=l?l:!e.getIsVisible()}))},e.getIsVisible=()=>{var l,n;let o=e.columns;return null==(l=o.length?o.some(e=>e.getIsVisible()):null==(n=t.getState().columnVisibility)?void 0:n[e.id])||l},e.getCanHide=()=>{var l,n;return(null==(l=e.columnDef.enableHiding)||l)&&(null==(n=t.options.enableHiding)||n)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=e1(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),e2(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=e1(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,l)=>[...e,...t,...l],e2(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,l)=>e1(()=>[l(),l().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e2(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var l;e.setColumnVisibility(t?{}:null!=(l=e.initialState.columnVisibility)?l:{})},e.toggleAllColumnsVisible=t=>{var l;t=null!=(l=t)?l:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,l)=>({...e,[l.id]:t||!(null!=l.getCanHide&&l.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var l;e.toggleAllColumnsVisible(null==(l=t.target)?void 0:l.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:eQ("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=e1(e=>[tf(t,e)],t=>t.findIndex(t=>t.id===e.id),e2(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=l=>{var n;return(null==(n=tf(t,l)[0])?void 0:n.id)===e.id},e.getIsLastColumn=l=>{var n;let o=tf(t,l);return(null==(n=o[o.length-1])?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var l;e.setColumnOrder(t?[]:null!=(l=e.initialState.columnOrder)?l:[])},e._getOrderColumnsFn=e1(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,l)=>n=>{let o=[];if(null!=e&&e.length){let t=[...e],l=[...n];for(;l.length&&t.length;){let e=t.shift(),n=l.findIndex(t=>t.id===e);n>-1&&o.push(l.splice(n,1)[0])}o=[...o,...l]}else o=n;return function(e,t,l){if(!(null!=t&&t.length)||!l)return e;let n=e.filter(e=>!t.includes(e.id));return"remove"===l?n:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...n]}(o,t,l)},e2(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:td(),...e}),getDefaultOptions:e=>({onColumnPinningChange:eQ("columnPinning",e)}),createColumn:(e,t)=>{e.pin=l=>{let n=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,r,i,a,s;return"right"===l?{left:(null!=(r=null==e?void 0:e.left)?r:[]).filter(e=>!(null!=n&&n.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=n&&n.includes(e))),...n]}:"left"===l?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=n&&n.includes(e))),...n],right:(null!=(s=null==e?void 0:e.right)?s:[]).filter(e=>!(null!=n&&n.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=n&&n.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=n&&n.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var l,n,o;return(null==(l=e.columnDef.enablePinning)||l)&&(null==(n=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||n)}),e.getIsPinned=()=>{let l=e.getLeafColumns().map(e=>e.id),{left:n,right:o}=t.getState().columnPinning,r=l.some(e=>null==n?void 0:n.includes(e)),i=l.some(e=>null==o?void 0:o.includes(e));return r?"left":!!i&&"right"},e.getPinnedIndex=()=>{var l,n;let o=e.getIsPinned();return o?null!=(l=null==(n=t.getState().columnPinning)||null==(n=n[o])?void 0:n.indexOf(e.id))?l:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=e1(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,l)=>{let n=[...null!=t?t:[],...null!=l?l:[]];return e.filter(e=>!n.includes(e.column.id))},e2(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=e1(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),e2(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=e1(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),e2(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var l,n;return e.setColumnPinning(t?td():null!=(l=null==(n=e.initialState)?void 0:n.columnPinning)?l:td())},e.getIsSomeColumnsPinned=t=>{var l,n,o;let r=e.getState().columnPinning;return t?!!(null==(l=r[t])?void 0:l.length):!!((null==(n=r.left)?void 0:n.length)||(null==(o=r.right)?void 0:o.length))},e.getLeftLeafColumns=e1(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),e2(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=e1(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),e2(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=e1(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,l)=>{let n=[...null!=t?t:[],...null!=l?l:[]];return e.filter(e=>!n.includes(e.id))},e2(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:eQ("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let l=t.getCoreRowModel().flatRows[0],n=null==l?void 0:l.getValue(e.id);return"string"==typeof n?ti.includesString:"number"==typeof n?ti.inNumberRange:"boolean"==typeof n||null!==n&&"object"==typeof n?ti.equals:Array.isArray(n)?ti.arrIncludes:ti.weakEquals},e.getFilterFn=()=>{var l,n;return e0(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(l=null==(n=t.options.filterFns)?void 0:n[e.columnDef.filterFn])?l:ti[e.columnDef.filterFn]},e.getCanFilter=()=>{var l,n,o;return(null==(l=e.columnDef.enableColumnFilter)||l)&&(null==(n=t.options.enableColumnFilters)||n)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var l;return null==(l=t.getState().columnFilters)||null==(l=l.find(t=>t.id===e.id))?void 0:l.value},e.getFilterIndex=()=>{var l,n;return null!=(l=null==(n=t.getState().columnFilters)?void 0:n.findIndex(t=>t.id===e.id))?l:-1},e.setFilterValue=l=>{t.setColumnFilters(t=>{var n,o;let r=e.getFilterFn(),i=null==t?void 0:t.find(t=>t.id===e.id),a=eJ(l,i?i.value:void 0);if(ts(r,a,e))return null!=(n=null==t?void 0:t.filter(t=>t.id!==e.id))?n:[];let s={id:e.id,value:a};return i?null!=(o=null==t?void 0:t.map(t=>t.id===e.id?s:t))?o:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let l=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var n;return null==(n=eJ(t,e))?void 0:n.filter(e=>{let t=l.find(t=>t.id===e.id);return!(t&&ts(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var l,n;e.setColumnFilters(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.columnFilters)?l:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:eQ("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var l;let n=null==(l=e.getCoreRowModel().flatRows[0])||null==(l=l._getAllCellsByColumnId()[t.id])?void 0:l.getValue();return"string"==typeof n||"number"==typeof n}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var l,n,o,r;return(null==(l=e.columnDef.enableGlobalFilter)||l)&&(null==(n=t.options.enableGlobalFilter)||n)&&(null==(o=t.options.enableFilters)||o)&&(null==(r=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||r)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>ti.includesString,e.getGlobalFilterFn=()=>{var t,l;let{globalFilterFn:n}=e.options;return e0(n)?n:"auto"===n?e.getGlobalAutoFilterFn():null!=(t=null==(l=e.options.filterFns)?void 0:l[n])?t:ti[n]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:eQ("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let l=t.getFilteredRowModel().flatRows.slice(10),n=!1;for(let t of l){let l=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(l))return tF.datetime;if("string"==typeof l&&(n=!0,l.split(tS).length>1))return tF.alphanumeric}return n?tF.text:tF.basic},e.getAutoSortDir=()=>{let l=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==l?void 0:l.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var l,n;if(!e)throw Error();return e0(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(l=null==(n=t.options.sortingFns)?void 0:n[e.columnDef.sortingFn])?l:tF[e.columnDef.sortingFn]},e.toggleSorting=(l,n)=>{let o=e.getNextSortingOrder(),r=null!=l;t.setSorting(i=>{let a,s=null==i?void 0:i.find(t=>t.id===e.id),u=null==i?void 0:i.findIndex(t=>t.id===e.id),d=[],g=r?l:"desc"===o;if("toggle"!=(a=null!=i&&i.length&&e.getCanMultiSort()&&n?s?"toggle":"add":null!=i&&i.length&&u!==i.length-1?"replace":s?"toggle":"replace")||r||o||(a="remove"),"add"===a){var c;(d=[...i,{id:e.id,desc:g}]).splice(0,d.length-(null!=(c=t.options.maxMultiSortColCount)?c:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?i.map(t=>t.id===e.id?{...t,desc:g}:t):"remove"===a?i.filter(t=>t.id!==e.id):[{id:e.id,desc:g}];return d})},e.getFirstSortDir=()=>{var l,n;return(null!=(l=null!=(n=e.columnDef.sortDescFirst)?n:t.options.sortDescFirst)?l:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=l=>{var n,o;let r=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===r||null!=(n=t.options.enableSortingRemoval)&&!n||!!l&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):r},e.getCanSort=()=>{var l,n;return(null==(l=e.columnDef.enableSorting)||l)&&(null==(n=t.options.enableSorting)||n)&&!!e.accessorFn},e.getCanMultiSort=()=>{var l,n;return null!=(l=null!=(n=e.columnDef.enableMultiSort)?n:t.options.enableMultiSort)?l:!!e.accessorFn},e.getIsSorted=()=>{var l;let n=null==(l=t.getState().sorting)?void 0:l.find(t=>t.id===e.id);return!!n&&(n.desc?"desc":"asc")},e.getSortIndex=()=>{var l,n;return null!=(l=null==(n=t.getState().sorting)?void 0:n.findIndex(t=>t.id===e.id))?l:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let l=e.getCanSort();return n=>{l&&(null==n.persist||n.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(n))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var l,n;e.setSorting(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.sorting)?l:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,l;return null!=(t=null==(l=e.getValue())||null==l.toString?void 0:l.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:eQ("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var l,n;return(null==(l=e.columnDef.enableGrouping)||l)&&(null==(n=t.options.enableGrouping)||n)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var l;return null==(l=t.getState().grouping)?void 0:l.includes(e.id)},e.getGroupedIndex=()=>{var l;return null==(l=t.getState().grouping)?void 0:l.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let l=t.getCoreRowModel().flatRows[0],n=null==l?void 0:l.getValue(e.id);return"number"==typeof n?tu.sum:"[object Date]"===Object.prototype.toString.call(n)?tu.extent:void 0},e.getAggregationFn=()=>{var l,n;if(!e)throw Error();return e0(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(l=null==(n=t.options.aggregationFns)?void 0:n[e.columnDef.aggregationFn])?l:tu[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var l,n;e.setGrouping(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.grouping)?l:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=l=>{if(e._groupingValuesCache.hasOwnProperty(l))return e._groupingValuesCache[l];let n=t.getColumn(l);return null!=n&&n.columnDef.getGroupingValue?(e._groupingValuesCache[l]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[l]):e.getValue(l)},e._groupingValuesCache={}},createCell:(e,t,l,n)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===l.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=l.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:eQ("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,l=!1;e._autoResetExpanded=()=>{var n,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?n:!e.options.manualExpanding){if(l)return;l=!0,e._queue(()=>{e.resetExpanded(),l=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var l,n;e.setExpanded(t?{}:null!=(l=null==(n=e.initialState)?void 0:n.expanded)?l:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let l=e.split(".");t=Math.max(t,l.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=l=>{t.setExpanded(n=>{var o;let r=!0===n||!!(null!=n&&n[e.id]),i={};if(!0===n?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=n,l=null!=(o=l)?o:!r,!r&&l)return{...i,[e.id]:!0};if(r&&!l){let{[e.id]:t,...l}=i;return l}return n})},e.getIsExpanded=()=>{var l;let n=t.getState().expanded;return!!(null!=(l=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?l:!0===n||(null==n?void 0:n[e.id]))},e.getCanExpand=()=>{var l,n,o;return null!=(l=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?l:(null==(n=t.options.enableExpanding)||n)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let l=!0,n=e;for(;l&&n.parentId;)l=(n=t.getRow(n.parentId,!0)).getIsExpanded();return l},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...th(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:eQ("pagination",e)}),createTable:e=>{let t=!1,l=!1;e._autoResetPageIndex=()=>{var n,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?n:!e.options.manualPagination){if(l)return;l=!0,e._queue(()=>{e.resetPageIndex(),l=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>eJ(t,e)),e.resetPagination=t=>{var l;e.setPagination(t?th():null!=(l=e.initialState.pagination)?l:th())},e.setPageIndex=t=>{e.setPagination(l=>{let n=eJ(t,l.pageIndex);return n=Math.max(0,Math.min(n,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...l,pageIndex:n}})},e.resetPageIndex=t=>{var l,n;e.setPageIndex(t?0:null!=(l=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageIndex)?l:0)},e.resetPageSize=t=>{var l,n;e.setPageSize(t?10:null!=(l=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageSize)?l:10)},e.setPageSize=t=>{e.setPagination(e=>{let l=Math.max(1,eJ(t,e.pageSize)),n=Math.floor(e.pageSize*e.pageIndex/l);return{...e,pageIndex:n,pageSize:l}})},e.setPageCount=t=>e.setPagination(l=>{var n;let o=eJ(t,null!=(n=e.options.pageCount)?n:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...l,pageCount:o}}),e.getPageOptions=e1(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},e2(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,l=e.getPageCount();return -1===l||0!==l&&t<l-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:tv(),...e}),getDefaultOptions:e=>({onRowPinningChange:eQ("rowPinning",e)}),createRow:(e,t)=>{e.pin=(l,n,o)=>{let r=n?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...r]);t.setRowPinning(e=>{var t,n,o,r,a,s;return"bottom"===l?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=i&&i.has(e))),bottom:[...(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)]}:"top"===l?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)],bottom:(null!=(s=null==e?void 0:e.bottom)?s:[]).filter(e=>!(null!=i&&i.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=i&&i.has(e))),bottom:(null!=(n=null==e?void 0:e.bottom)?n:[]).filter(e=>!(null!=i&&i.has(e)))}})},e.getCanPin=()=>{var l;let{enableRowPinning:n,enablePinning:o}=t.options;return"function"==typeof n?n(e):null==(l=null!=n?n:o)||l},e.getIsPinned=()=>{let l=[e.id],{top:n,bottom:o}=t.getState().rowPinning,r=l.some(e=>null==n?void 0:n.includes(e)),i=l.some(e=>null==o?void 0:o.includes(e));return r?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var l,n;let o=e.getIsPinned();if(!o)return -1;let r=null==(l="top"===o?t.getTopRows():t.getBottomRows())?void 0:l.map(e=>{let{id:t}=e;return t});return null!=(n=null==r?void 0:r.indexOf(e.id))?n:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var l,n;return e.setRowPinning(t?tv():null!=(l=null==(n=e.initialState)?void 0:n.rowPinning)?l:tv())},e.getIsSomeRowsPinned=t=>{var l,n,o;let r=e.getState().rowPinning;return t?!!(null==(l=r[t])?void 0:l.length):!!((null==(n=r.top)?void 0:n.length)||(null==(o=r.bottom)?void 0:o.length))},e._getPinnedRows=(t,l,n)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=l?l:[]).map(t=>{let l=e.getRow(t,!0);return l.getIsAllParentsExpanded()?l:null}):(null!=l?l:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:n}))},e.getTopRows=e1(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,l)=>e._getPinnedRows(t,l,"top"),e2(e.options,"debugRows","getTopRows")),e.getBottomRows=e1(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,l)=>e._getPinnedRows(t,l,"bottom"),e2(e.options,"debugRows","getBottomRows")),e.getCenterRows=e1(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,l)=>{let n=new Set([...null!=t?t:[],...null!=l?l:[]]);return e.filter(e=>!n.has(e.id))},e2(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:eQ("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var l;return e.setRowSelection(t?{}:null!=(l=e.initialState.rowSelection)?l:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(l=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let n={...l},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(n[e.id]=!0)}):o.forEach(e=>{delete n[e.id]}),n})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(l=>{let n=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...l};return e.getRowModel().rows.forEach(t=>{tw(o,t.id,n,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=e1(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,l)=>Object.keys(t).length?tx(e,l):{rows:[],flatRows:[],rowsById:{}},e2(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=e1(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,l)=>Object.keys(t).length?tx(e,l):{rows:[],flatRows:[],rowsById:{}},e2(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=e1(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,l)=>Object.keys(t).length?tx(e,l):{rows:[],flatRows:[],rowsById:{}},e2(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:l}=e.getState(),n=!!(t.length&&Object.keys(l).length);return n&&t.some(e=>e.getCanSelect()&&!l[e.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:l}=e.getState(),n=!!t.length;return n&&t.some(e=>!l[e.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var t;let l=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return l>0&&l<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(l,n)=>{let o=e.getIsSelected();t.setRowSelection(r=>{var i;if(l=void 0!==l?l:!o,e.getCanSelect()&&o===l)return r;let a={...r};return tw(a,e.id,l,null==(i=null==n?void 0:n.selectChildren)||i,t),a})},e.getIsSelected=()=>{let{rowSelection:l}=t.getState();return tb(e,l)},e.getIsSomeSelected=()=>{let{rowSelection:l}=t.getState();return"some"===tC(e,l)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:l}=t.getState();return"all"===tC(e,l)},e.getCanSelect=()=>{var l;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(l=t.options.enableRowSelection)||l},e.getCanSelectSubRows=()=>{var l;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(l=t.options.enableSubRowSelection)||l},e.getCanMultiSelect=()=>{var l;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(l=t.options.enableMultiRowSelection)||l},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return l=>{var n;t&&e.toggleSelected(null==(n=l.target)?void 0:n.checked)}}}},{getDefaultColumnDef:()=>tg,getInitialState:e=>({columnSizing:{},columnSizingInfo:tc(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:eQ("columnSizing",e),onColumnSizingInfoChange:eQ("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var l,n,o;let r=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(l=e.columnDef.minSize)?l:tg.minSize,null!=(n=null!=r?r:e.columnDef.size)?n:tg.size),null!=(o=e.columnDef.maxSize)?o:tg.maxSize)},e.getStart=e1(e=>[e,tf(t,e),t.getState().columnSizing],(t,l)=>l.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),e2(t.options,"debugColumns","getStart")),e.getAfter=e1(e=>[e,tf(t,e),t.getState().columnSizing],(t,l)=>l.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),e2(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:l,...n}=t;return n})},e.getCanResize=()=>{var l,n;return(null==(l=e.columnDef.enableResizing)||l)&&(null==(n=t.options.enableColumnResizing)||n)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,l=e=>{if(e.subHeaders.length)e.subHeaders.forEach(l);else{var n;t+=null!=(n=e.column.getSize())?n:0}};return l(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=l=>{let n=t.getColumn(e.column.id),o=null==n?void 0:n.getCanResize();return r=>{if(!n||!o||(null==r.persist||r.persist(),tm(r)&&r.touches&&r.touches.length>1))return;let i=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[n.id,n.getSize()]],s=tm(r)?Math.round(r.touches[0].clientX):r.clientX,u={},d=(e,l)=>{"number"==typeof l&&(t.setColumnSizingInfo(e=>{var n,o;let r="rtl"===t.options.columnResizeDirection?-1:1,i=(l-(null!=(n=null==e?void 0:e.startOffset)?n:0))*r,a=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,l]=e;u[t]=Math.round(100*Math.max(l+l*a,0))/100}),{...e,deltaOffset:i,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...u})))},g=e=>d("move",e),c=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},p=l||("undefined"!=typeof document?document:null),m={moveHandler:e=>g(e.clientX),upHandler:e=>{null==p||p.removeEventListener("mousemove",m.moveHandler),null==p||p.removeEventListener("mouseup",m.upHandler),c(e.clientX)}},f={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(e.touches[0].clientX),!1),upHandler:e=>{var t;null==p||p.removeEventListener("touchmove",f.moveHandler),null==p||p.removeEventListener("touchend",f.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(null==(t=e.touches[0])?void 0:t.clientX)}},h=!!function(){if("boolean"==typeof tp)return tp;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return tp=e}()&&{passive:!1};tm(r)?(null==p||p.addEventListener("touchmove",f.moveHandler,h),null==p||p.addEventListener("touchend",f.upHandler,h)):(null==p||p.addEventListener("mousemove",m.moveHandler,h),null==p||p.addEventListener("mouseup",m.upHandler,h)),t.setColumnSizingInfo(e=>({...e,startOffset:s,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var l;e.setColumnSizing(t?{}:null!=(l=e.initialState.columnSizing)?l:{})},e.resetHeaderSizeInfo=t=>{var l;e.setColumnSizingInfo(t?tc():null!=(l=e.initialState.columnSizingInfo)?l:tc())},e.getTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getLeftHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getCenterHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getRightHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function tP(e,t){var n,o,r;return e?"function"==typeof(o=n=e)&&(()=>{let e=Object.getPrototypeOf(o);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof n||"object"==typeof(r=n)&&"symbol"==typeof r.$$typeof&&["react.memo","react.forward_ref"].includes(r.$$typeof.description)?l.createElement(e,t):e:null}var tI=e.i(63415);let tN=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:l,className:(0,a.cn)("w-full caption-bottom text-sm",n),...o})})});tN.displayName="Table";let tV=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("thead",{ref:l,className:(0,a.cn)("[&_tr]:border-b",n),...o})});tV.displayName="TableHeader";let t_=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("tbody",{ref:l,className:(0,a.cn)("[&_tr:last-child]:border-0",n),...o})});t_.displayName="TableBody",l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("tfoot",{ref:l,className:(0,a.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...o})}).displayName="TableFooter";let tE=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("tr",{ref:l,className:(0,a.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...o})});tE.displayName="TableRow";let tD=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("th",{ref:l,className:(0,a.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...o})});tD.displayName="TableHead";let tL=l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("td",{ref:l,className:(0,a.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...o})});tL.displayName="TableCell",l.forwardRef((e,l)=>{let{className:n,...o}=e;return(0,t.jsx)("caption",{ref:l,className:(0,a.cn)("mt-4 text-sm text-muted-foreground",n),...o})}).displayName="TableCaption";var tA=e.i(7233),tH=e.i(71428);function tk(e){let{rows:l=5,columns:n=5}=e;return(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(tN,{children:[(0,t.jsx)(tV,{children:(0,t.jsx)(tE,{children:Array.from({length:n}).map((e,l)=>(0,t.jsx)(tD,{children:(0,t.jsx)(tH.Skeleton,{className:"h-4 w-20"})},l))})}),(0,t.jsx)(t_,{children:Array.from({length:l}).map((e,l)=>(0,t.jsx)(tE,{children:Array.from({length:n}).map((e,l)=>(0,t.jsx)(tL,{children:0===l?(0,t.jsx)(tH.Skeleton,{className:"h-4 w-32"}):1===l?(0,t.jsx)(tH.Skeleton,{className:"h-4 w-24"}):2===l?(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(tH.Skeleton,{className:"h-3 w-3 rounded-full"}),(0,t.jsx)(tH.Skeleton,{className:"h-4 w-16"})]}):3===l?(0,t.jsx)(tH.Skeleton,{className:"h-4 w-12"}):(0,t.jsx)(tH.Skeleton,{className:"h-8 w-8 rounded"})},l))},l))})]})})}let tT=(0,eB.default)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function tG(e){let{isRefreshing:n=!1,className:o}=e,[r,i]=l.useState(!1);return(0,t.jsx)(tT,{className:(0,a.cn)("h-4 w-4 transition-all duration-300 ease-in-out",n&&"animate-spin",!n&&r&&"rotate-180 scale-110",o),onMouseEnter:()=>!n&&i(!0),onMouseLeave:()=>i(!1),style:{transformOrigin:"center"}})}function tz(e){var n,o,r;let{columns:i,data:a,loading:s=!1,refreshing:u=!1,onAddTarget:d,onRefresh:g}=e,[c,p]=l.useState([]),[m,f]=l.useState([]),[w,x]=l.useState({}),[b,C]=l.useState({}),S=function(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=l.useState(()=>({current:function(e){var t,l;let n=[...tM,...null!=(t=e._features)?t:[]],o={_features:n},r=o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o)),{}),i={...null!=(l=e.initialState)?l:{}};o._features.forEach(e=>{var t;i=null!=(t=null==e.getInitialState?void 0:e.getInitialState(i))?t:i});let a=[],s=!1,u={_features:n,options:{...r,...e},initialState:i,_queue:e=>{a.push(e),s||(s=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();s=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{var t;t=eJ(e,o.options),o.options=o.options.mergeOptions?o.options.mergeOptions(r,t):{...r,...t}},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,l)=>{var n;return null!=(n=null==o.options.getRowId?void 0:o.options.getRowId(e,t,l))?n:"".concat(l?[l.id,t].join("."):t)},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let l=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!l&&!(l=o.getCoreRowModel().rowsById[e]))throw Error();return l},_getDefaultColumnDef:e1(()=>[o.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,l;return null!=(t=null==(l=e.renderValue())||null==l.toString?void 0:l.toString())?t:null},...o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},e2(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:e1(()=>[o._getColumnDefs()],e=>{let t=function(e,l,n){return void 0===n&&(n=0),e.map(e=>{let r=function(e,t,l,n){var o,r;let i,a={...e._getDefaultColumnDef(),...t},s=a.accessorKey,u=null!=(o=null!=(r=a.id)?r:s?"function"==typeof String.prototype.replaceAll?s.replaceAll(".","_"):s.replace(/\./g,"_"):void 0)?o:"string"==typeof a.header?a.header:void 0;if(a.accessorFn?i=a.accessorFn:s&&(i=s.includes(".")?e=>{let t=e;for(let e of s.split(".")){var l;t=null==(l=t)?void 0:l[e]}return t}:e=>e[a.accessorKey]),!u)throw Error();let d={id:"".concat(String(u)),accessorFn:i,parent:n,depth:l,columnDef:a,columns:[],getFlatColumns:e1(()=>[!0],()=>{var e;return[d,...null==(e=d.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},e2(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:e1(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=d.columns)&&t.length?e(d.columns.flatMap(e=>e.getLeafColumns())):[d]},e2(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(d,e);return d}(o,e,n,l);return r.columns=e.columns?t(e.columns,r,n+1):[],r})};return t(e)},e2(e,"debugColumns","getAllColumns")),getAllFlatColumns:e1(()=>[o.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),e2(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:e1(()=>[o.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),e2(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:e1(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),e2(e,"debugColumns","getAllLeafColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,u);for(let e=0;e<o._features.length;e++){let t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}(t)})),[o,r]=l.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...o,...e.state},onStateChange:t=>{r(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}({data:a,columns:i,onSortingChange:p,onColumnFiltersChange:f,getCoreRowModel:e=>e1(()=>[e.options.data],t=>{let l={rows:[],flatRows:[],rowsById:{}},n=function(t,o,r){void 0===o&&(o=0);let i=[];for(let s=0;s<t.length;s++){let u=e6(e,e._getRowId(t[s],s,r),t[s],s,o,void 0,null==r?void 0:r.id);if(l.flatRows.push(u),l.rowsById[u.id]=u,i.push(u),e.options.getSubRows){var a;u.originalSubRows=e.options.getSubRows(t[s],s),null!=(a=u.originalSubRows)&&a.length&&(u.subRows=n(u.originalSubRows,o+1,u))}}return i};return l.rows=n(t),l},e2(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex())),getPaginationRowModel:e=>e1(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,l)=>{let n;if(!l.rows.length)return l;let{pageSize:o,pageIndex:r}=t,{rows:i,flatRows:a,rowsById:s}=l,u=o*r;i=i.slice(u,u+o),(n=e.options.paginateExpandedRows?{rows:i,flatRows:a,rowsById:s}:function(e){let t=[],l=e=>{var n;t.push(e),null!=(n=e.subRows)&&n.length&&e.getIsExpanded()&&e.subRows.forEach(l)};return e.rows.forEach(l),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:a,rowsById:s})).flatRows=[];let d=e=>{n.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return n.rows.forEach(d),n},e2(e.options,"debugTable","getPaginationRowModel")),getSortedRowModel:e=>e1(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,l)=>{if(!l.rows.length||!(null!=t&&t.length))return l;let n=e.getState().sorting,o=[],r=n.filter(t=>{var l;return null==(l=e.getColumn(t.id))?void 0:l.getCanSort()}),i={};r.forEach(t=>{let l=e.getColumn(t.id);l&&(i[t.id]={sortUndefined:l.columnDef.sortUndefined,invertSorting:l.columnDef.invertSorting,sortingFn:l.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let n=0;n<r.length;n+=1){var l;let o=r[n],a=i[o.id],s=a.sortUndefined,u=null!=(l=null==o?void 0:o.desc)&&l,d=0;if(s){let l=e.getValue(o.id),n=t.getValue(o.id),r=void 0===l,i=void 0===n;if(r||i){if("first"===s)return r?-1:1;if("last"===s)return r?1:-1;d=r&&i?0:r?s:-s}}if(0===d&&(d=a.sortingFn(e,t,o.id)),0!==d)return u&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(l.rows),flatRows:o,rowsById:l.rowsById}},e2(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex())),getFilteredRowModel:e=>e1(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,l,n)=>{var o,r;let i,a;if(!t.rows.length||!(null!=l&&l.length)&&!n){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let s=[],u=[];(null!=l?l:[]).forEach(t=>{var l;let n=e.getColumn(t.id);if(!n)return;let o=n.getFilterFn();o&&s.push({id:t.id,filterFn:o,resolvedValue:null!=(l=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?l:t.value})});let d=(null!=l?l:[]).map(e=>e.id),g=e.getGlobalFilterFn(),c=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());n&&g&&c.length&&(d.push("__global__"),c.forEach(e=>{var t;u.push({id:e.id,filterFn:g,resolvedValue:null!=(t=null==g.resolveFilterValue?void 0:g.resolveFilterValue(n))?t:n})}));for(let e=0;e<t.flatRows.length;e++){let l=t.flatRows[e];if(l.columnFilters={},s.length)for(let e=0;e<s.length;e++){let t=(i=s[e]).id;l.columnFilters[t]=i.filterFn(l,t,i.resolvedValue,e=>{l.columnFiltersMeta[t]=e})}if(u.length){for(let e=0;e<u.length;e++){let t=(a=u[e]).id;if(a.filterFn(l,t,a.resolvedValue,e=>{l.columnFiltersMeta[t]=e})){l.columnFilters.__global__=!0;break}}!0!==l.columnFilters.__global__&&(l.columnFilters.__global__=!1)}}return o=t.rows,r=e=>{for(let t=0;t<d.length;t++)if(!1===e.columnFilters[d[t]])return!1;return!0},e.options.filterFromLeafRows?function(e,t,l){var n;let o=[],r={},i=null!=(n=l.options.maxLeafRowFilterDepth)?n:100,a=function(e,n){void 0===n&&(n=0);let s=[];for(let d=0;d<e.length;d++){var u;let g=e[d],c=e6(l,g.id,g.original,g.index,g.depth,void 0,g.parentId);if(c.columnFilters=g.columnFilters,null!=(u=g.subRows)&&u.length&&n<i){if(c.subRows=a(g.subRows,n+1),t(g=c)&&!c.subRows.length||t(g)||c.subRows.length){s.push(g),r[g.id]=g,o.push(g);continue}}else t(g=c)&&(s.push(g),r[g.id]=g,o.push(g))}return s};return{rows:a(e),flatRows:o,rowsById:r}}(o,r,e):function(e,t,l){var n;let o=[],r={},i=null!=(n=l.options.maxLeafRowFilterDepth)?n:100,a=function(e,n){void 0===n&&(n=0);let s=[];for(let d=0;d<e.length;d++){let g=e[d];if(t(g)){var u;if(null!=(u=g.subRows)&&u.length&&n<i){let e=e6(l,g.id,g.original,g.index,g.depth,void 0,g.parentId);e.subRows=a(g.subRows,n+1),g=e}s.push(g),o.push(g),r[g.id]=g}}return s};return{rows:a(e),flatRows:o,rowsById:r}}(o,r,e)},e2(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex())),onColumnVisibilityChange:x,onRowSelectionChange:C,state:{sorting:c,columnFilters:m,columnVisibility:w,rowSelection:b}});return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.Input,{placeholder:"Filter addresses...",value:null!=(r=null==(n=S.getColumn("address"))?void 0:n.getFilterValue())?r:"",onChange:e=>{var t;return null==(t=S.getColumn("address"))?void 0:t.setFilterValue(e.target.value)},className:"max-w-sm"}),(0,t.jsxs)(tI.DropdownMenu,{children:[(0,t.jsx)(tI.DropdownMenuTrigger,{asChild:!0,children:(0,t.jsxs)(h.Button,{variant:"outline",className:"ml-auto",children:["Columns ",(0,t.jsx)(eq,{className:"ml-2 h-4 w-4"})]})}),(0,t.jsx)(tI.DropdownMenuContent,{align:"end",children:S.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,t.jsx)(tI.DropdownMenuCheckboxItem,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(h.Button,{variant:"outline",onClick:g,disabled:s||u,className:"group transition-all duration-200 hover:bg-muted/50",children:[(0,t.jsx)(tG,{isRefreshing:u,className:"mr-2"}),"Refresh"]}),(0,t.jsxs)(h.Button,{onClick:d,children:[(0,t.jsx)(tA.Plus,{className:"mr-2 h-4 w-4"}),"Add Target"]})]})]}),s?(0,t.jsx)(tk,{rows:5,columns:5}):(0,t.jsx)("div",{className:"rounded-md border transition-opacity duration-300 ".concat(u?"opacity-60":"opacity-100"),children:(0,t.jsxs)(tN,{children:[(0,t.jsx)(tV,{children:S.getHeaderGroups().map(e=>(0,t.jsx)(tE,{children:e.headers.map(e=>(0,t.jsx)(tD,{children:e.isPlaceholder?null:tP(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,t.jsx)(t_,{children:(null==(o=S.getRowModel().rows)?void 0:o.length)?S.getRowModel().rows.map(e=>(0,t.jsx)(tE,{"data-state":e.getIsSelected()&&"selected",className:"transition-all duration-200 hover:bg-muted/50 animate-in fade-in-0 slide-in-from-left-1",children:e.getVisibleCells().map(e=>(0,t.jsx)(tL,{children:tP(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,t.jsx)(tE,{children:(0,t.jsx)(tL,{colSpan:i.length,className:"h-24 text-center",children:"No targets found. Add your first target to get started."})})})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,t.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[S.getFilteredSelectedRowModel().rows.length," of"," ",S.getFilteredRowModel().rows.length," row(s) selected."]}),(0,t.jsxs)("div",{className:"space-x-2",children:[(0,t.jsx)(h.Button,{variant:"outline",size:"sm",onClick:()=>S.previousPage(),disabled:!S.getCanPreviousPage(),children:"Previous"}),(0,t.jsx)(h.Button,{variant:"outline",size:"sm",onClick:()=>S.nextPage(),disabled:!S.getCanNextPage(),children:"Next"})]})]})]})}let tO=(0,e.i(25913).cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function tB(e){let{className:l,variant:n,...o}=e;return(0,t.jsx)("div",{className:(0,a.cn)(tO({variant:n}),l),...o})}let tq=(0,eB.default)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var tU=e.i(69638),tK=e.i(73884),tW=e.i(3116);let tX=(0,eB.default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function tY(){let[e,n]=l.useState([]),[o,r]=l.useState(!1),[a,s]=l.useState(!1),[u,d]=l.useState(!1),[g,c]=l.useState(!1),[p,m]=l.useState(null),[f,b]=l.useState({address:"",description:"",criticality:10}),[C,S]=l.useState({}),R=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{e?s(!0):r(!0);let t=await (0,eZ.GetTargets)();n(t.targets||[])}catch(e){console.error("Failed to load targets:",e),n([])}finally{e?s(!1):r(!1)}};l.useEffect(()=>{R()},[]);let y=l.useCallback(()=>{R(!0)},[]),j=async()=>{try{let e={};if(f.address.trim()||(e.address="Target address is required"),Object.keys(e).length>0)return void S(e);r(!0),await (0,eZ.AddTarget)({address:f.address.trim(),description:f.description.trim(),criticality:f.criticality}),b({address:"",description:"",criticality:10}),S({}),d(!1),await R()}catch(e){S({submit:"Failed to add target: ".concat(e)})}finally{r(!1)}},F=async()=>{if(p)try{r(!0),await (0,eZ.DeleteTarget)(p.target_id),c(!1),m(null),await R()}catch(e){console.error("Failed to delete target:",e)}finally{r(!1)}},M=l.useCallback(e=>{m(e),c(!0)},[]),P=l.useMemo(()=>(e=>{let{onDelete:l}=e;return[{accessorKey:"address",header:e=>{let{column:l}=e;return(0,t.jsxs)(h.Button,{variant:"ghost",onClick:()=>l.toggleSorting("asc"===l.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Address",(0,t.jsx)(tq,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:l}=e,n=l.getValue("address");return(0,t.jsx)("div",{className:"font-medium",children:n})}},{accessorKey:"description",header:e=>{let{column:l}=e;return(0,t.jsxs)(h.Button,{variant:"ghost",onClick:()=>l.toggleSorting("asc"===l.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Description",(0,t.jsx)(tq,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:l}=e,n=l.getValue("description");return(0,t.jsx)("div",{className:"max-w-[200px] truncate",children:n||"-"})}},{id:"vulnerabilities",header:e=>{let{column:l}=e;return(0,t.jsxs)(h.Button,{variant:"ghost",onClick:()=>l.toggleSorting("asc"===l.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Vulnerabilities",(0,t.jsx)(tq,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:l}=e,n=l.original.severity_counts;if(!n)return(0,t.jsx)("div",{className:"text-muted-foreground",children:"No scan data"});let o=n.critical+n.high+n.medium+n.low+n.info;return 0===o?(0,t.jsxs)(tB,{variant:"outline",className:"flex items-center gap-1 w-fit",children:[(0,t.jsx)(tU.CheckCircle,{className:"h-3 w-3 text-green-500"}),"Clean"]}):(0,t.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,t.jsxs)("div",{className:"flex gap-1 text-xs",children:[n.critical>0&&(0,t.jsxs)(tB,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#f3e8f1",color:"#883678"},children:[n.critical,"C"]}),n.high>0&&(0,t.jsxs)(tB,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdeaea",color:"#d94e4e"},children:[n.high,"H"]}),n.medium>0&&(0,t.jsxs)(tB,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdf0e8",color:"#d65f3c"},children:[n.medium,"M"]}),n.low>0&&(0,t.jsxs)(tB,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdf5e8",color:"#bf7534"},children:[n.low,"L"]}),n.info>0&&(0,t.jsxs)(tB,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#eef0f7",color:"#4c5595"},children:[n.info,"I"]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Total: ",o]})]})},sortingFn:(e,t)=>{let l=e.original,n=t.original,o=l.severity_counts,r=n.severity_counts;if(!o&&!r)return 0;if(!o)return 1;if(!r)return -1;let i=e=>1e3*e.critical+100*e.high+10*e.medium+ +e.low+.1*e.info,a=i(o);return i(r)-a}},{accessorKey:"type",header:"Type",cell:e=>{let{row:l}=e,n=l.getValue("type");return(0,t.jsx)(tB,{variant:"outline",children:n||"default"})}},{id:"last_scan_status",header:e=>{let{column:l}=e;return(0,t.jsxs)(h.Button,{variant:"ghost",onClick:()=>l.toggleSorting("asc"===l.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Last Scan Status",(0,t.jsx)(tq,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:l}=e,n=l.original,o=n.last_scan_date,r=n.last_scan_session_status;if(!o)return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(tK.XCircle,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Never scanned"})]});let i=e=>{try{let t=new Date(e),l=t.toLocaleDateString("en-CA",{year:"numeric",month:"2-digit",day:"2-digit"}),n=t.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1});return{date:l,time:n,full:"".concat(l," ").concat(n)}}catch(t){return{date:e,time:"",full:e}}};return(0,t.jsxs)("div",{className:"flex flex-col gap-1",children:[(e=>{switch(null==e?void 0:e.toLowerCase()){case"completed":return(0,t.jsxs)(tB,{variant:"outline",className:"flex items-center gap-1 w-fit text-green-700 border-green-200",children:[(0,t.jsx)(tU.CheckCircle,{className:"h-3 w-3"}),"Completed"]});case"running":case"scanning":return(0,t.jsxs)(tB,{variant:"outline",className:"flex items-center gap-1 w-fit text-blue-700 border-blue-200",children:[(0,t.jsx)(tW.Clock,{className:"h-3 w-3"}),"Running"]});case"failed":case"error":return(0,t.jsxs)(tB,{variant:"destructive",className:"flex items-center gap-1 w-fit",children:[(0,t.jsx)(tX,{className:"h-3 w-3"}),"Failed"]});case"aborted":case"cancelled":return(0,t.jsxs)(tB,{variant:"secondary",className:"flex items-center gap-1 w-fit",children:[(0,t.jsx)(tK.XCircle,{className:"h-3 w-3"}),"Aborted"]});default:return(0,t.jsxs)(tB,{variant:"outline",className:"flex items-center gap-1 w-fit",children:[(0,t.jsx)(tX,{className:"h-3 w-3"}),e||"Unknown"]})}})(r),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[i(o).date," ",i(o).time]})]})},sortingFn:(e,t)=>{let l=e.original,n=t.original,o=l.last_scan_date,r=n.last_scan_date;return o||r?o?r?new Date(r).getTime()-new Date(o).getTime():-1:1:0}},{id:"actions",header:"Actions",cell:e=>{let{row:n}=e,o=n.original;return(0,t.jsxs)(tI.DropdownMenu,{children:[(0,t.jsx)(tI.DropdownMenuTrigger,{asChild:!0,children:(0,t.jsxs)(h.Button,{variant:"ghost",className:"h-8 w-8 p-0",size:"icon",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,t.jsx)(i.MoreHorizontal,{className:"h-4 w-4"})]})}),(0,t.jsxs)(tI.DropdownMenuContent,{align:"end",children:[(0,t.jsx)(tI.DropdownMenuItem,{onClick:()=>navigator.clipboard.writeText(o.address),children:"Copy address"}),(0,t.jsx)(tI.DropdownMenuSeparator,{}),(0,t.jsx)(tI.DropdownMenuItem,{onClick:()=>l(o),className:"text-red-600 focus:text-red-600",children:"Delete target"})]})]})}}]})({onDelete:M}),[M]);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(tz,{columns:P,data:e,loading:o,refreshing:a,onAddTarget:()=>d(!0),onRefresh:y}),(0,t.jsx)(x.Dialog,{open:u,onOpenChange:d,children:(0,t.jsxs)(x.DialogContent,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(x.DialogHeader,{children:[(0,t.jsx)(x.DialogTitle,{children:"Add New Target"}),(0,t.jsx)(x.DialogDescription,{children:"Add a new target for security scanning. Enter the target URL or IP address."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.Label,{htmlFor:"address",children:"Target Address *"}),(0,t.jsx)(v.Input,{id:"address",placeholder:"https://example.com or *************",value:f.address,onChange:e=>{b(t=>({...t,address:e.target.value})),C.address&&S(e=>({...e,address:""}))}}),C.address&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:C.address})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.Label,{htmlFor:"description",children:"Description"}),(0,t.jsx)(v.Input,{id:"description",placeholder:"Optional description",value:f.description,onChange:e=>b(t=>({...t,description:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.Label,{htmlFor:"criticality",children:"Criticality Level"}),(0,t.jsxs)(Q,{value:f.criticality.toString(),onValueChange:e=>b(t=>({...t,criticality:parseInt(e)})),children:[(0,t.jsx)(eK,{children:(0,t.jsx)(en,{})}),(0,t.jsxs)(eY,{children:[(0,t.jsx)(e$,{value:"10",children:"Normal (10)"}),(0,t.jsx)(e$,{value:"20",children:"High (20)"}),(0,t.jsx)(e$,{value:"30",children:"Critical (30)"})]})]})]}),C.submit&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:C.submit})]}),(0,t.jsxs)(x.DialogFooter,{children:[(0,t.jsx)(h.Button,{variant:"outline",onClick:()=>{d(!1),b({address:"",description:"",criticality:10}),S({})},disabled:o,children:"Cancel"}),(0,t.jsx)(h.Button,{onClick:j,disabled:o,children:o?"Adding...":"Add Target"})]})]})}),(0,t.jsx)(x.Dialog,{open:g,onOpenChange:c,children:(0,t.jsxs)(x.DialogContent,{children:[(0,t.jsxs)(x.DialogHeader,{children:[(0,t.jsx)(x.DialogTitle,{children:"Delete Target"}),(0,t.jsxs)(x.DialogDescription,{children:['Are you sure you want to delete the target "',null==p?void 0:p.address,'"? This action cannot be undone.']})]}),(0,t.jsxs)(x.DialogFooter,{children:[(0,t.jsx)(h.Button,{variant:"outline",onClick:()=>{c(!1),m(null)},disabled:o,children:"Cancel"}),(0,t.jsx)(h.Button,{variant:"destructive",onClick:F,disabled:o,children:o?"Deleting...":"Delete"})]})]})})]})}function t$(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:[(0,t.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 1"})}),(0,t.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 2"})}),(0,t.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 3"})})]}),(0,t.jsx)("div",{className:"bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Welcome to Acunetix Desktop"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your security scanning infrastructure from this centralized dashboard."})]})})]})}function tZ(){let[e,o]=l.useState("dashboard"),r="targets"===e?{category:"Security",page:"Targets"}:{category:"Overview",page:"Dashboard"};return(0,t.jsxs)(f.SidebarProvider,{children:[(0,t.jsx)(n.AppSidebar,{onNavigate:o}),(0,t.jsxs)(f.SidebarInset,{children:[(0,t.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,t.jsx)(f.SidebarTrigger,{className:"-ml-1"}),(0,t.jsx)(m.Separator,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),(0,t.jsx)(s,{children:(0,t.jsxs)(u,{children:[(0,t.jsx)(d,{className:"hidden md:block",children:(0,t.jsx)(g,{href:"#",onClick:()=>o("dashboard"),children:r.category})}),(0,t.jsx)(p,{className:"hidden md:block"}),(0,t.jsx)(d,{children:(0,t.jsx)(c,{children:r.page})})]})})]})}),(0,t.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:"targets"===e?(0,t.jsx)(tY,{}):(0,t.jsx)(t$,{})})]})]})}}]);