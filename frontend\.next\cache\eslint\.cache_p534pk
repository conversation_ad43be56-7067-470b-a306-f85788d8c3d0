[{"E:\\Projects\\AcunetixDesktop\\frontend\\app\\layout.tsx": "1", "E:\\Projects\\AcunetixDesktop\\frontend\\app\\page.tsx": "2", "E:\\Projects\\AcunetixDesktop\\frontend\\lib\\utils.ts": "3", "E:\\Projects\\AcunetixDesktop\\frontend\\app\\dashboard\\page.tsx": "4", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\app-sidebar.tsx": "5", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-main.tsx": "6", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-projects.tsx": "7", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\avatar.tsx": "8", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\breadcrumb.tsx": "9", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\button.tsx": "10", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\collapsible.tsx": "11", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\dropdown-menu.tsx": "12", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\input.tsx": "13", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\separator.tsx": "14", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\sheet.tsx": "15", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\sidebar.tsx": "16", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\skeleton.tsx": "17", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\tooltip.tsx": "18", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\add-backend-dialog.tsx": "19", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\backend-switcher.tsx": "20", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-settings.tsx": "21", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\dialog.tsx": "22", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\label.tsx": "23", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\edit-backend-dialog.tsx": "24", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\connection-test-result.tsx": "25", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\badge.tsx": "26", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\select.tsx": "27", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\table.tsx": "28", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\dashboard-content.tsx": "29", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets-content.tsx": "30", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\columns.tsx": "31", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\data-table.tsx": "32", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\table-skeleton.tsx": "33", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\refresh-icon.tsx": "34", "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\tabs.tsx": "35"}, {"size": 689, "mtime": 1756034891503, "results": "36", "hashOfConfig": "37"}, {"size": 2462, "mtime": 1756136745290, "results": "38", "hashOfConfig": "37"}, {"size": 166, "mtime": 1756035862741, "results": "39", "hashOfConfig": "37"}, {"size": 1944, "mtime": 1756036010915, "results": "40", "hashOfConfig": "37"}, {"size": 3838, "mtime": 1756140244356, "results": "41", "hashOfConfig": "37"}, {"size": 2616, "mtime": 1756137061409, "results": "42", "hashOfConfig": "37"}, {"size": 2501, "mtime": 1756036010945, "results": "43", "hashOfConfig": "37"}, {"size": 1097, "mtime": 1756036011040, "results": "44", "hashOfConfig": "37"}, {"size": 2357, "mtime": 1756036011030, "results": "45", "hashOfConfig": "37"}, {"size": 2123, "mtime": 1756036011001, "results": "46", "hashOfConfig": "37"}, {"size": 800, "mtime": 1756036011032, "results": "47", "hashOfConfig": "37"}, {"size": 8284, "mtime": 1756036011036, "results": "48", "hashOfConfig": "37"}, {"size": 967, "mtime": 1756036011017, "results": "49", "hashOfConfig": "37"}, {"size": 699, "mtime": 1756036011006, "results": "50", "hashOfConfig": "37"}, {"size": 4090, "mtime": 1756036011012, "results": "51", "hashOfConfig": "37"}, {"size": 21633, "mtime": 1756036010998, "results": "52", "hashOfConfig": "37"}, {"size": 276, "mtime": 1756036011027, "results": "53", "hashOfConfig": "37"}, {"size": 1891, "mtime": 1756036011015, "results": "54", "hashOfConfig": "37"}, {"size": 5431, "mtime": 1756135846367, "results": "55", "hashOfConfig": "37"}, {"size": 8497, "mtime": 1756433352612, "results": "56", "hashOfConfig": "37"}, {"size": 2047, "mtime": 1756130481808, "results": "57", "hashOfConfig": "37"}, {"size": 3982, "mtime": 1756131434599, "results": "58", "hashOfConfig": "37"}, {"size": 611, "mtime": 1756131483050, "results": "59", "hashOfConfig": "37"}, {"size": 7613, "mtime": 1756135970699, "results": "60", "hashOfConfig": "37"}, {"size": 3671, "mtime": 1756140244358, "results": "61", "hashOfConfig": "37"}, {"size": 1128, "mtime": 1756136356827, "results": "62", "hashOfConfig": "37"}, {"size": 5629, "mtime": 1756136344243, "results": "63", "hashOfConfig": "37"}, {"size": 2765, "mtime": 1756136321658, "results": "64", "hashOfConfig": "37"}, {"size": 1148, "mtime": 1756136872381, "results": "65", "hashOfConfig": "37"}, {"size": 7605, "mtime": 1756433603718, "results": "66", "hashOfConfig": "37"}, {"size": 10945, "mtime": 1756449027600, "results": "67", "hashOfConfig": "37"}, {"size": 6799, "mtime": 1756138567289, "results": "68", "hashOfConfig": "37"}, {"size": 1679, "mtime": 1756138109645, "results": "69", "hashOfConfig": "37"}, {"size": 745, "mtime": 1756138583322, "results": "70", "hashOfConfig": "37"}, {"size": 1969, "mtime": 1756139410237, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1fvi0ht", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Projects\\AcunetixDesktop\\frontend\\app\\layout.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\app\\page.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\lib\\utils.ts", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\app\\dashboard\\page.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\app-sidebar.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-main.tsx", ["177"], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-projects.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\avatar.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\breadcrumb.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\button.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\collapsible.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\dropdown-menu.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\input.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\separator.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\sheet.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\sidebar.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\skeleton.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\tooltip.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\add-backend-dialog.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\backend-switcher.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\nav-settings.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\dialog.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\label.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\edit-backend-dialog.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\connection-test-result.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\badge.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\select.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\table.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\dashboard-content.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets-content.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\columns.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\data-table.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\targets\\table-skeleton.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\refresh-icon.tsx", [], [], "E:\\Projects\\AcunetixDesktop\\frontend\\components\\ui\\tabs.tsx", [], [], {"ruleId": "178", "severity": 1, "message": "179", "line": 23, "column": 3, "nodeType": null, "messageId": "180", "endLine": 23, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'onNavigate' is defined but never used.", "unusedVar"]