module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},2439,a=>{"use strict";function b(a){return window.go.main.App.AddBackend(a)}function c(a){return window.go.main.App.AddTarget(a)}function d(a){return window.go.main.App.DeleteBackend(a)}function e(a){return window.go.main.App.DeleteTarget(a)}function f(){return window.go.main.App.GetActiveBackend()}function g(){return window.go.main.App.GetBackends()}function h(){return window.go.main.App.GetConfig()}function i(){return window.go.main.App.GetConfigPath()}function j(){return window.go.main.App.GetDefaultOverrides()}function k(){return window.go.main.App.GetLink()}function l(){return window.go.main.App.GetScanAuthorization()}function m(){return window.go.main.App.GetSettings()}function n(){return window.go.main.App.GetSeverityCounts()}function o(){return window.go.main.App.GetTargetAgent()}function p(){return window.go.main.App.GetTargets()}function q(a){return window.go.main.App.Greet(a)}function r(a){return window.go.main.App.SetActiveBackend(a)}function s(a){return window.go.main.App.TestBackendConnection(a)}function t(a){return window.go.main.App.TestBackendConnectionDirect(a)}function u(a,b){return window.go.main.App.UpdateBackend(a,b)}function v(a){return window.go.main.App.UpdateSettings(a)}a.s(["AddBackend",()=>b,"AddTarget",()=>c,"DeleteBackend",()=>d,"DeleteTarget",()=>e,"GetActiveBackend",()=>f,"GetBackends",()=>g,"GetConfig",()=>h,"GetConfigPath",()=>i,"GetDefaultOverrides",()=>j,"GetLink",()=>k,"GetScanAuthorization",()=>l,"GetSettings",()=>m,"GetSeverityCounts",()=>n,"GetTargetAgent",()=>o,"GetTargets",()=>p,"Greet",()=>q,"SetActiveBackend",()=>r,"TestBackendConnection",()=>s,"TestBackendConnectionDirect",()=>t,"UpdateBackend",()=>u,"UpdateSettings",()=>v])},3794,a=>{a.v(a=>Promise.resolve().then(()=>a(2439)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9c598f35._.js.map