package main

import (
	"fmt"
	"testing"
)

func TestBackendManager(t *testing.T) {
	// Create a new backend manager
	bm := NewBackendManager()

	// Test adding a backend
	req := NewBackendRequest{
		Name:   "Test Backend",
		URL:    "https://scanner.example.com:3443",
		APIKey: "test-api-key-123",
	}

	backend, err := bm.AddBackend(req)
	if err != nil {
		t.Fatalf("Failed to add backend: %v", err)
	}

	if backend.Name != req.Name {
		t.<PERSON>("Expected name %s, got %s", req.Name, backend.Name)
	}

	if backend.URL != req.URL {
		t.Errorf("Expected URL %s, got %s", req.URL, backend.URL)
	}

	// Test getting backends
	backends := bm.GetBackends()
	if backends.Count != 1 {
		t.<PERSON>("Expected 1 backend, got %d", backends.Count)
	}

	// Test setting active backend
	err = bm.SetActiveBackend(backend.ID)
	if err != nil {
		t.Fatalf("Failed to set active backend: %v", err)
	}

	activeBackend, err := bm.GetActiveBackend()
	if err != nil {
		t.Fatalf("Failed to get active backend: %v", err)
	}

	if activeBackend.ID != backend.ID {
		t.Errorf("Expected active backend ID %s, got %s", backend.ID, activeBackend.ID)
	}

	// Test updating backend
	updateReq := NewBackendRequest{
		Name:   "Updated Test Backend",
		URL:    "https://updated.example.com:3443",
		APIKey: "updated-api-key-456",
	}

	updatedBackend, err := bm.UpdateBackend(backend.ID, updateReq)
	if err != nil {
		t.Fatalf("Failed to update backend: %v", err)
	}

	if updatedBackend.Name != updateReq.Name {
		t.Errorf("Expected updated name %s, got %s", updateReq.Name, updatedBackend.Name)
	}

	// Test deleting backend
	err = bm.DeleteBackend(backend.ID)
	if err != nil {
		t.Fatalf("Failed to delete backend: %v", err)
	}

	backends = bm.GetBackends()
	if backends.Count != 0 {
		t.Errorf("Expected 0 backends after deletion, got %d", backends.Count)
	}

	fmt.Println("All backend manager tests passed!")
}

func TestBackendValidation(t *testing.T) {
	bm := NewBackendManager()

	// Test adding backend with missing name
	req := NewBackendRequest{
		Name:   "",
		URL:    "https://scanner.example.com:3443",
		APIKey: "test-api-key",
	}

	_, err := bm.AddBackend(req)
	if err == nil {
		t.Error("Expected error for missing name, got nil")
	}

	// Test adding backend with missing URL
	req = NewBackendRequest{
		Name:   "Test Backend",
		URL:    "",
		APIKey: "test-api-key",
	}

	_, err = bm.AddBackend(req)
	if err == nil {
		t.Error("Expected error for missing URL, got nil")
	}

	// Test adding backend with missing API key
	req = NewBackendRequest{
		Name:   "Test Backend",
		URL:    "https://scanner.example.com:3443",
		APIKey: "",
	}

	_, err = bm.AddBackend(req)
	if err == nil {
		t.Error("Expected error for missing API key, got nil")
	}

	fmt.Println("All validation tests passed!")
}

func TestURLNormalization(t *testing.T) {
	bm := NewBackendManager()

	testCases := []struct {
		input    string
		expected string
	}{
		{"scanner.example.com:3443", "https://scanner.example.com:3443"},
		{"http://scanner.example.com:3443", "http://scanner.example.com:3443"},
		{"https://scanner.example.com:3443/", "https://scanner.example.com:3443"},
		{"https://scanner.example.com:3443/api/v1/", "https://scanner.example.com:3443/api/v1"},
	}

	for _, tc := range testCases {
		req := NewBackendRequest{
			Name:   "Test Backend",
			URL:    tc.input,
			APIKey: "test-api-key",
		}

		backend, err := bm.AddBackend(req)
		if err != nil {
			t.Fatalf("Failed to add backend with URL %s: %v", tc.input, err)
		}

		if backend.URL != tc.expected {
			t.Errorf("URL normalization failed. Input: %s, Expected: %s, Got: %s",
				tc.input, tc.expected, backend.URL)
		}

		// Clean up
		bm.DeleteBackend(backend.ID)
	}

	fmt.Println("All URL normalization tests passed!")
}
