package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// Config represents the application configuration
type Config struct {
	Version  string    `json:"version"`
	Backends []Backend `json:"backends"`
	Settings Settings  `json:"settings"`
	LastSave time.Time `json:"last_save"`
}

// Settings represents application settings
type Settings struct {
	AutoConnect       bool   `json:"auto_connect"`
	CheckInterval     int    `json:"check_interval"` // in seconds
	Theme             string `json:"theme"`
	Language          string `json:"language"`
	LogLevel          string `json:"log_level"`
	MaxRetries        int    `json:"max_retries"`
	ConnectionTimeout int    `json:"connection_timeout"` // in seconds
}

// ConfigManager manages application configuration
type ConfigManager struct {
	configPath string
	config     *Config
}

// NewConfigManager creates a new configuration manager
func NewConfigManager() *ConfigManager {
	// Get executable directory
	execPath, err := os.Executable()
	if err != nil {
		// Fallback to current directory
		execPath = "."
	}

	configDir := filepath.Dir(execPath)
	configPath := filepath.Join(configDir, "config.json")

	cm := &ConfigManager{
		configPath: configPath,
		config: &Config{
			Version:  "1.0.0",
			Backends: make([]Backend, 0),
			Settings: Settings{
				AutoConnect:       false,
				CheckInterval:     30,
				Theme:             "system",
				Language:          "en",
				LogLevel:          "info",
				MaxRetries:        3,
				ConnectionTimeout: 10,
			},
			LastSave: time.Now(),
		},
	}

	// Try to load existing config
	cm.LoadConfig()

	return cm
}

// LoadConfig loads configuration from file
func (cm *ConfigManager) LoadConfig() error {
	if _, err := os.Stat(cm.configPath); os.IsNotExist(err) {
		// Config file doesn't exist, use defaults
		return nil
	}

	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %v", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config file: %v", err)
	}

	// Validate and merge with defaults
	if config.Version == "" {
		config.Version = "1.0.0"
	}

	if config.Backends == nil {
		config.Backends = make([]Backend, 0)
	}

	// Merge settings with defaults
	if config.Settings.CheckInterval == 0 {
		config.Settings.CheckInterval = 30
	}
	if config.Settings.Theme == "" {
		config.Settings.Theme = "system"
	}
	if config.Settings.Language == "" {
		config.Settings.Language = "en"
	}
	if config.Settings.LogLevel == "" {
		config.Settings.LogLevel = "info"
	}
	if config.Settings.MaxRetries == 0 {
		config.Settings.MaxRetries = 3
	}
	if config.Settings.ConnectionTimeout == 0 {
		config.Settings.ConnectionTimeout = 10
	}

	cm.config = &config
	return nil
}

// SaveConfig saves configuration to file
func (cm *ConfigManager) SaveConfig() error {
	cm.config.LastSave = time.Now()

	data, err := json.MarshalIndent(cm.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	// Create directory if it doesn't exist
	configDir := filepath.Dir(cm.configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	// Write to temporary file first, then rename (atomic operation)
	tempPath := cm.configPath + ".tmp"
	if err := os.WriteFile(tempPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %v", err)
	}

	if err := os.Rename(tempPath, cm.configPath); err != nil {
		os.Remove(tempPath) // Clean up temp file
		return fmt.Errorf("failed to save config file: %v", err)
	}

	return nil
}

// GetConfig returns the current configuration
func (cm *ConfigManager) GetConfig() *Config {
	return cm.config
}

// GetBackends returns all backends from config
func (cm *ConfigManager) GetBackends() []Backend {
	return cm.config.Backends
}

// AddBackend adds a new backend to config
func (cm *ConfigManager) AddBackend(backend Backend) error {
	cm.config.Backends = append(cm.config.Backends, backend)
	return cm.SaveConfig()
}

// UpdateBackend updates an existing backend in config
func (cm *ConfigManager) UpdateBackend(id string, updatedBackend Backend) error {
	for i, backend := range cm.config.Backends {
		if backend.ID == id {
			// Preserve ID and timestamps
			updatedBackend.ID = id
			updatedBackend.CreatedAt = backend.CreatedAt
			updatedBackend.UpdatedAt = time.Now()

			cm.config.Backends[i] = updatedBackend
			return cm.SaveConfig()
		}
	}
	return fmt.Errorf("backend not found")
}

// DeleteBackend removes a backend from config
func (cm *ConfigManager) DeleteBackend(id string) error {
	for i, backend := range cm.config.Backends {
		if backend.ID == id {
			// Remove the backend
			cm.config.Backends = append(cm.config.Backends[:i], cm.config.Backends[i+1:]...)

			// If we deleted the last backend, clear everything
			// If we deleted an active backend, clear active status from all remaining backends
			if len(cm.config.Backends) == 0 {
				// No backends left, nothing to do
			} else if backend.IsActive {
				// We deleted the active backend, clear active status from all remaining backends
				for j := range cm.config.Backends {
					cm.config.Backends[j].IsActive = false
				}
			}
			// If we deleted a non-active backend, leave other backends unchanged

			return cm.SaveConfig()
		}
	}
	return fmt.Errorf("backend not found")
}

// GetBackend returns a specific backend by ID
func (cm *ConfigManager) GetBackend(id string) (*Backend, error) {
	for i, backend := range cm.config.Backends {
		if backend.ID == id {
			return &cm.config.Backends[i], nil
		}
	}
	return nil, fmt.Errorf("backend not found")
}

// SetActiveBackend sets a backend as active
func (cm *ConfigManager) SetActiveBackend(id string) error {
	found := false
	for i := range cm.config.Backends {
		if cm.config.Backends[i].ID == id {
			cm.config.Backends[i].IsActive = true
			found = true
		} else {
			cm.config.Backends[i].IsActive = false
		}
	}
	if !found {
		return fmt.Errorf("backend not found")
	}
	return cm.SaveConfig()
}

// GetActiveBackend returns the currently active backend
func (cm *ConfigManager) GetActiveBackend() (*Backend, error) {
	for i, backend := range cm.config.Backends {
		if backend.IsActive {
			return &cm.config.Backends[i], nil
		}
	}
	return nil, fmt.Errorf("no active backend")
}

// UpdateBackendStatus updates the status of a backend
func (cm *ConfigManager) UpdateBackendStatus(id string, status string, lastTested time.Time) error {
	for i, backend := range cm.config.Backends {
		if backend.ID == id {
			cm.config.Backends[i].Status = status
			cm.config.Backends[i].LastTested = lastTested
			return cm.SaveConfig()
		}
	}
	return fmt.Errorf("backend not found")
}

// UpdateBackendStatusAndLatency updates the status and latency of a backend
func (cm *ConfigManager) UpdateBackendStatusAndLatency(id string, status string, lastTested time.Time, latency int64) error {
	for i, backend := range cm.config.Backends {
		if backend.ID == id {
			cm.config.Backends[i].Status = status
			cm.config.Backends[i].LastTested = lastTested
			cm.config.Backends[i].Latency = latency
			return cm.SaveConfig()
		}
	}
	return fmt.Errorf("backend not found")
}

// GetSettings returns application settings
func (cm *ConfigManager) GetSettings() Settings {
	return cm.config.Settings
}

// UpdateSettings updates application settings
func (cm *ConfigManager) UpdateSettings(settings Settings) error {
	cm.config.Settings = settings
	return cm.SaveConfig()
}

// GetConfigPath returns the path to the config file
func (cm *ConfigManager) GetConfigPath() string {
	return cm.configPath
}
