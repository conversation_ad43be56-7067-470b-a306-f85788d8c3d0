import * as React from "react"
import { RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"

interface RefreshIconProps {
  isRefreshing?: boolean
  className?: string
}

export function RefreshIcon({ isRefreshing = false, className }: RefreshIconProps) {
  const [isHovered, setIsHovered] = React.useState(false)

  return (
    <RefreshCw
      className={cn(
        "h-4 w-4 transition-all duration-300 ease-in-out",
        isRefreshing && "animate-spin",
        !isRefreshing && isHovered && "rotate-180 scale-110",
        className
      )}
      onMouseEnter={() => !isRefreshing && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transformOrigin: "center",
      }}
    />
  )
}
