module.exports=[60350,a=>{"use strict";a.s(["default",()=>bZ],60350);var b=a.i(87924),c=a.i(72131),d=a.i(39793),e=a.i(11011),f=a.i(50522),g=a.i(17755),h=a.i(97895);function i({...a}){return(0,b.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...a})}function j({className:a,...c}){return(0,b.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,h.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",a),...c})}function k({className:a,...c}){return(0,b.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,h.cn)("inline-flex items-center gap-1.5",a),...c})}function l({asChild:a,className:c,...d}){let f=a?e.Slot:"a";return(0,b.jsx)(f,{"data-slot":"breadcrumb-link",className:(0,h.cn)("hover:text-foreground transition-colors",c),...d})}function m({className:a,...c}){return(0,b.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("text-foreground font-normal",a),...c})}function n({children:a,className:c,...d}){return(0,b.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",c),...d,children:a??(0,b.jsx)(f.ChevronRight,{})})}var o=a.i(7379),p=a.i(14727),q=a.i(40695),r=a.i(5522),s=a.i(17171),t=a.i(65733),u=a.i(35112);function v(a,[b,c]){return Math.min(c,Math.max(b,a))}var w=a.i(7554),x=a.i(37738),y=a.i(70121),z=a.i(50104),A=a.i(7827),B=a.i(96743),C=a.i(86228),D=a.i(22297),E=a.i(92843),F=a.i(4691),G=a.i(92616),H=a.i(30553),I=a.i(46872),J=a.i(25152),K=a.i(72752),L=a.i(28094),M=a.i(41852),N=a.i(52081),O=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],Q="Select",[R,S,T]=(0,x.createCollection)(Q),[U,V]=(0,z.createContextScope)(Q,[T,F.createPopperScope]),W=(0,F.createPopperScope)(),[X,Y]=U(Q),[Z,$]=U(Q),_=a=>{let{__scopeSelect:d,children:e,open:f,defaultOpen:g,onOpenChange:h,value:i,defaultValue:j,onValueChange:k,dir:l,name:m,autoComplete:n,disabled:o,required:p,form:q}=a,r=W(d),[s,t]=c.useState(null),[u,v]=c.useState(null),[w,x]=c.useState(!1),y=(0,A.useDirection)(l),[z,B]=(0,J.useControllableState)({prop:f,defaultProp:g??!1,onChange:h,caller:Q}),[C,D]=(0,J.useControllableState)({prop:i,defaultProp:j,onChange:k,caller:Q}),G=c.useRef(null),H=!s||q||!!s.closest("form"),[I,K]=c.useState(new Set),L=Array.from(I).map(a=>a.props.value).join(";");return(0,b.jsx)(F.Root,{...r,children:(0,b.jsxs)(X,{required:p,scope:d,trigger:s,onTriggerChange:t,valueNode:u,onValueNodeChange:v,valueNodeHasChildren:w,onValueNodeHasChildrenChange:x,contentId:(0,E.useId)(),value:C,onValueChange:D,open:z,onOpenChange:B,dir:y,triggerPointerDownPosRef:G,disabled:o,children:[(0,b.jsx)(R.Provider,{scope:d,children:(0,b.jsx)(Z,{scope:a.__scopeSelect,onNativeOptionAdd:c.useCallback(a=>{K(b=>new Set(b).add(a))},[]),onNativeOptionRemove:c.useCallback(a=>{K(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:e})}),H?(0,b.jsxs)(aM,{"aria-hidden":!0,required:p,tabIndex:-1,name:m,autoComplete:n,value:C,onChange:a=>D(a.target.value),disabled:o,form:q,children:[void 0===C?(0,b.jsx)("option",{value:""}):null,Array.from(I)]},L):null]})})};_.displayName=Q;var aa="SelectTrigger",ab=c.forwardRef((a,d)=>{let{__scopeSelect:e,disabled:f=!1,...g}=a,h=W(e),i=Y(aa,e),j=i.disabled||f,k=(0,y.useComposedRefs)(d,i.onTriggerChange),l=S(e),m=c.useRef("touch"),[n,o,p]=aO(a=>{let b=l().filter(a=>!a.disabled),c=b.find(a=>a.value===i.value),d=aP(b,a,c);void 0!==d&&i.onValueChange(d.value)}),q=a=>{j||(i.onOpenChange(!0),p()),a&&(i.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,b.jsx)(F.Anchor,{asChild:!0,...h,children:(0,b.jsx)(H.Primitive.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:j,"data-disabled":j?"":void 0,"data-placeholder":aN(i.value)?"":void 0,...g,ref:k,onClick:(0,w.composeEventHandlers)(g.onClick,a=>{a.currentTarget.focus(),"mouse"!==m.current&&q(a)}),onPointerDown:(0,w.composeEventHandlers)(g.onPointerDown,a=>{m.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(q(a),a.preventDefault())}),onKeyDown:(0,w.composeEventHandlers)(g.onKeyDown,a=>{let b=""!==n.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||o(a.key),(!b||" "!==a.key)&&O.includes(a.key)&&(q(),a.preventDefault())})})})});ab.displayName=aa;var ac="SelectValue",ad=c.forwardRef((a,c)=>{let{__scopeSelect:d,className:e,style:f,children:g,placeholder:h="",...i}=a,j=Y(ac,d),{onValueNodeHasChildrenChange:k}=j,l=void 0!==g,m=(0,y.useComposedRefs)(c,j.onValueNodeChange);return(0,K.useLayoutEffect)(()=>{k(l)},[k,l]),(0,b.jsx)(H.Primitive.span,{...i,ref:m,style:{pointerEvents:"none"},children:aN(j.value)?(0,b.jsx)(b.Fragment,{children:h}):g})});ad.displayName=ac;var ae=c.forwardRef((a,c)=>{let{__scopeSelect:d,children:e,...f}=a;return(0,b.jsx)(H.Primitive.span,{"aria-hidden":!0,...f,ref:c,children:e||"▼"})});ae.displayName="SelectIcon";var af=a=>(0,b.jsx)(G.Portal,{asChild:!0,...a});af.displayName="SelectPortal";var ag="SelectContent",ah=c.forwardRef((a,d)=>{let e=Y(ag,a.__scopeSelect),[f,g]=c.useState();return((0,K.useLayoutEffect)(()=>{g(new DocumentFragment)},[]),e.open)?(0,b.jsx)(al,{...a,ref:d}):f?u.createPortal((0,b.jsx)(ai,{scope:a.__scopeSelect,children:(0,b.jsx)(R.Slot,{scope:a.__scopeSelect,children:(0,b.jsx)("div",{children:a.children})})}),f):null});ah.displayName=ag;var[ai,aj]=U(ag),ak=(0,e.createSlot)("SelectContent.RemoveScroll"),al=c.forwardRef((a,d)=>{let{__scopeSelect:e,position:f="item-aligned",onCloseAutoFocus:g,onEscapeKeyDown:h,onPointerDownOutside:i,side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:p,sticky:q,hideWhenDetached:r,avoidCollisions:s,...t}=a,u=Y(ag,e),[v,x]=c.useState(null),[z,A]=c.useState(null),E=(0,y.useComposedRefs)(d,a=>x(a)),[F,G]=c.useState(null),[H,I]=c.useState(null),J=S(e),[K,L]=c.useState(!1),O=c.useRef(!1);c.useEffect(()=>{if(v)return(0,M.hideOthers)(v)},[v]),(0,C.useFocusGuards)();let P=c.useCallback(a=>{let[b,...c]=J().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&z&&(z.scrollTop=0),c===d&&z&&(z.scrollTop=z.scrollHeight),c?.focus(),document.activeElement!==e))return},[J,z]),Q=c.useCallback(()=>P([F,v]),[P,F,v]);c.useEffect(()=>{K&&Q()},[K,Q]);let{onOpenChange:R,triggerPointerDownPosRef:T}=u;c.useEffect(()=>{if(v){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(T.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(T.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():v.contains(c.target)||R(!1),document.removeEventListener("pointermove",b),T.current=null};return null!==T.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[v,R,T]),c.useEffect(()=>{let a=()=>R(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[R]);let[U,V]=aO(a=>{let b=J().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=aP(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),W=c.useCallback((a,b,c)=>{let d=!O.current&&!c;(void 0!==u.value&&u.value===b||d)&&(G(a),d&&(O.current=!0))},[u.value]),X=c.useCallback(()=>v?.focus(),[v]),Z=c.useCallback((a,b,c)=>{let d=!O.current&&!c;(void 0!==u.value&&u.value===b||d)&&I(a)},[u.value]),$="popper"===f?an:am,_=$===an?{side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:p,sticky:q,hideWhenDetached:r,avoidCollisions:s}:{};return(0,b.jsx)(ai,{scope:e,content:v,viewport:z,onViewportChange:A,itemRefCallback:W,selectedItem:F,onItemLeave:X,itemTextRefCallback:Z,focusSelectedItem:Q,selectedItemText:H,position:f,isPositioned:K,searchRef:U,children:(0,b.jsx)(N.RemoveScroll,{as:ak,allowPinchZoom:!0,children:(0,b.jsx)(D.FocusScope,{asChild:!0,trapped:u.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,w.composeEventHandlers)(g,a=>{u.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,b.jsx)(B.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>u.onOpenChange(!1),children:(0,b.jsx)($,{role:"listbox",id:u.contentId,"data-state":u.open?"open":"closed",dir:u.dir,onContextMenu:a=>a.preventDefault(),...t,..._,onPlaced:()=>L(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...t.style},onKeyDown:(0,w.composeEventHandlers)(t.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||V(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=J().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>P(b)),a.preventDefault()}})})})})})})});al.displayName="SelectContentImpl";var am=c.forwardRef((a,d)=>{let{__scopeSelect:e,onPlaced:f,...g}=a,h=Y(ag,e),i=aj(ag,e),[j,k]=c.useState(null),[l,m]=c.useState(null),n=(0,y.useComposedRefs)(d,a=>m(a)),o=S(e),p=c.useRef(!1),q=c.useRef(!0),{viewport:r,selectedItem:s,selectedItemText:t,focusSelectedItem:u}=i,w=c.useCallback(()=>{if(h.trigger&&h.valueNode&&j&&l&&r&&s&&t){let a=h.trigger.getBoundingClientRect(),b=l.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=t.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,f=c.left-e,g=a.left-f,h=a.width+g,i=Math.max(h,b.width),k=v(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.left=k+"px"}else{let e=b.right-d.right,f=window.innerWidth-c.right-e,g=window.innerWidth-a.right-f,h=a.width+g,i=Math.max(h,b.width),k=v(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.right=k+"px"}let e=o(),g=window.innerHeight-20,i=r.scrollHeight,k=window.getComputedStyle(l),m=parseInt(k.borderTopWidth,10),n=parseInt(k.paddingTop,10),q=parseInt(k.borderBottomWidth,10),u=m+n+i+parseInt(k.paddingBottom,10)+q,w=Math.min(5*s.offsetHeight,u),x=window.getComputedStyle(r),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=s.offsetHeight/2,C=m+n+(s.offsetTop+B);if(C<=A){let a=e.length>0&&s===e[e.length-1].ref.current;j.style.bottom="0px";let b=Math.max(g-A,B+(a?z:0)+(l.clientHeight-r.offsetTop-r.offsetHeight)+q);j.style.height=C+b+"px"}else{let a=e.length>0&&s===e[0].ref.current;j.style.top="0px";let b=Math.max(A,m+r.offsetTop+(a?y:0)+B);j.style.height=b+(u-C)+"px",r.scrollTop=C-A+r.offsetTop}j.style.margin="10px 0",j.style.minHeight=w+"px",j.style.maxHeight=g+"px",f?.(),requestAnimationFrame(()=>p.current=!0)}},[o,h.trigger,h.valueNode,j,l,r,s,t,h.dir,f]);(0,K.useLayoutEffect)(()=>w(),[w]);let[x,z]=c.useState();(0,K.useLayoutEffect)(()=>{l&&z(window.getComputedStyle(l).zIndex)},[l]);let A=c.useCallback(a=>{a&&!0===q.current&&(w(),u?.(),q.current=!1)},[w,u]);return(0,b.jsx)(ao,{scope:e,contentWrapper:j,shouldExpandOnScrollRef:p,onScrollButtonChange:A,children:(0,b.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:x},children:(0,b.jsx)(H.Primitive.div,{...g,ref:n,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});am.displayName="SelectItemAlignedPosition";var an=c.forwardRef((a,c)=>{let{__scopeSelect:d,align:e="start",collisionPadding:f=10,...g}=a,h=W(d);return(0,b.jsx)(F.Content,{...h,...g,ref:c,align:e,collisionPadding:f,style:{boxSizing:"border-box",...g.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});an.displayName="SelectPopperPosition";var[ao,ap]=U(ag,{}),aq="SelectViewport",ar=c.forwardRef((a,d)=>{let{__scopeSelect:e,nonce:f,...g}=a,h=aj(aq,e),i=ap(aq,e),j=(0,y.useComposedRefs)(d,h.onViewportChange),k=c.useRef(0);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:f}),(0,b.jsx)(R.Slot,{scope:e,children:(0,b.jsx)(H.Primitive.div,{"data-radix-select-viewport":"",role:"presentation",...g,ref:j,style:{position:"relative",flex:1,overflow:"hidden auto",...g.style},onScroll:(0,w.composeEventHandlers)(g.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=i;if(d?.current&&c){let a=Math.abs(k.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}k.current=b.scrollTop})})})]})});ar.displayName=aq;var as="SelectGroup",[at,au]=U(as);c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=(0,E.useId)();return(0,b.jsx)(at,{scope:d,id:f,children:(0,b.jsx)(H.Primitive.div,{role:"group","aria-labelledby":f,...e,ref:c})})}).displayName=as;var av="SelectLabel",aw=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=au(av,d);return(0,b.jsx)(H.Primitive.div,{id:f.id,...e,ref:c})});aw.displayName=av;var ax="SelectItem",[ay,az]=U(ax),aA=c.forwardRef((a,d)=>{let{__scopeSelect:e,value:f,disabled:g=!1,textValue:h,...i}=a,j=Y(ax,e),k=aj(ax,e),l=j.value===f,[m,n]=c.useState(h??""),[o,p]=c.useState(!1),q=(0,y.useComposedRefs)(d,a=>k.itemRefCallback?.(a,f,g)),r=(0,E.useId)(),s=c.useRef("touch"),t=()=>{g||(j.onValueChange(f),j.onOpenChange(!1))};if(""===f)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,b.jsx)(ay,{scope:e,value:f,disabled:g,textId:r,isSelected:l,onItemTextChange:c.useCallback(a=>{n(b=>b||(a?.textContent??"").trim())},[]),children:(0,b.jsx)(R.ItemSlot,{scope:e,value:f,disabled:g,textValue:m,children:(0,b.jsx)(H.Primitive.div,{role:"option","aria-labelledby":r,"data-highlighted":o?"":void 0,"aria-selected":l&&o,"data-state":l?"checked":"unchecked","aria-disabled":g||void 0,"data-disabled":g?"":void 0,tabIndex:g?void 0:-1,...i,ref:q,onFocus:(0,w.composeEventHandlers)(i.onFocus,()=>p(!0)),onBlur:(0,w.composeEventHandlers)(i.onBlur,()=>p(!1)),onClick:(0,w.composeEventHandlers)(i.onClick,()=>{"mouse"!==s.current&&t()}),onPointerUp:(0,w.composeEventHandlers)(i.onPointerUp,()=>{"mouse"===s.current&&t()}),onPointerDown:(0,w.composeEventHandlers)(i.onPointerDown,a=>{s.current=a.pointerType}),onPointerMove:(0,w.composeEventHandlers)(i.onPointerMove,a=>{s.current=a.pointerType,g?k.onItemLeave?.():"mouse"===s.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,w.composeEventHandlers)(i.onPointerLeave,a=>{a.currentTarget===document.activeElement&&k.onItemLeave?.()}),onKeyDown:(0,w.composeEventHandlers)(i.onKeyDown,a=>{(k.searchRef?.current===""||" "!==a.key)&&(P.includes(a.key)&&t()," "===a.key&&a.preventDefault())})})})})});aA.displayName=ax;var aB="SelectItemText",aC=c.forwardRef((a,d)=>{let{__scopeSelect:e,className:f,style:g,...h}=a,i=Y(aB,e),j=aj(aB,e),k=az(aB,e),l=$(aB,e),[m,n]=c.useState(null),o=(0,y.useComposedRefs)(d,a=>n(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),p=m?.textContent,q=c.useMemo(()=>(0,b.jsx)("option",{value:k.value,disabled:k.disabled,children:p},k.value),[k.disabled,k.value,p]),{onNativeOptionAdd:r,onNativeOptionRemove:s}=l;return(0,K.useLayoutEffect)(()=>(r(q),()=>s(q)),[r,s,q]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(H.Primitive.span,{id:k.textId,...h,ref:o}),k.isSelected&&i.valueNode&&!i.valueNodeHasChildren?u.createPortal(h.children,i.valueNode):null]})});aC.displayName=aB;var aD="SelectItemIndicator",aE=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return az(aD,d).isSelected?(0,b.jsx)(H.Primitive.span,{"aria-hidden":!0,...e,ref:c}):null});aE.displayName=aD;var aF="SelectScrollUpButton",aG=c.forwardRef((a,d)=>{let e=aj(aF,a.__scopeSelect),f=ap(aF,a.__scopeSelect),[g,h]=c.useState(!1),i=(0,y.useComposedRefs)(d,f.onScrollButtonChange);return(0,K.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){h(b.scrollTop>0)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(aJ,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});aG.displayName=aF;var aH="SelectScrollDownButton",aI=c.forwardRef((a,d)=>{let e=aj(aH,a.__scopeSelect),f=ap(aH,a.__scopeSelect),[g,h]=c.useState(!1),i=(0,y.useComposedRefs)(d,f.onScrollButtonChange);return(0,K.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;h(Math.ceil(b.scrollTop)<a)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(aJ,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});aI.displayName=aH;var aJ=c.forwardRef((a,d)=>{let{__scopeSelect:e,onAutoScroll:f,...g}=a,h=aj("SelectScrollButton",e),i=c.useRef(null),j=S(e),k=c.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return c.useEffect(()=>()=>k(),[k]),(0,K.useLayoutEffect)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,b.jsx)(H.Primitive.div,{"aria-hidden":!0,...g,ref:d,style:{flexShrink:0,...g.style},onPointerDown:(0,w.composeEventHandlers)(g.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(f,50))}),onPointerMove:(0,w.composeEventHandlers)(g.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(f,50))}),onPointerLeave:(0,w.composeEventHandlers)(g.onPointerLeave,()=>{k()})})}),aK=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return(0,b.jsx)(H.Primitive.div,{"aria-hidden":!0,...e,ref:c})});aK.displayName="SelectSeparator";var aL="SelectArrow";c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=W(d),g=Y(aL,d),h=aj(aL,d);return g.open&&"popper"===h.position?(0,b.jsx)(F.Arrow,{...f,...e,ref:c}):null}).displayName=aL;var aM=c.forwardRef(({__scopeSelect:a,value:d,...e},f)=>{let g=c.useRef(null),h=(0,y.useComposedRefs)(f,g),i=function(a){let b=c.useRef({value:a,previous:a});return c.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(d);return c.useEffect(()=>{let a=g.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==d&&b){let c=new Event("change",{bubbles:!0});b.call(a,d),a.dispatchEvent(c)}},[i,d]),(0,b.jsx)(H.Primitive.select,{...e,style:{...L.VISUALLY_HIDDEN_STYLES,...e.style},ref:h,defaultValue:d})});function aN(a){return""===a||void 0===a}function aO(a){let b=(0,I.useCallbackRef)(a),d=c.useRef(""),e=c.useRef(0),f=c.useCallback(a=>{let c=d.current+a;b(c),function a(b){d.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(c)},[b]),g=c.useCallback(()=>{d.current="",window.clearTimeout(e.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(e.current),[]),[d,f,g]}function aP(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}aM.displayName="SelectBubbleInput";var aQ=a.i(34157),aQ=aQ,aR=a.i(70106);let aS=(0,aR.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),aT=(0,aR.default)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),aU=c.forwardRef(({className:a,children:c,...d},e)=>(0,b.jsxs)(ab,{ref:e,className:(0,h.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...d,children:[c,(0,b.jsx)(ae,{asChild:!0,children:(0,b.jsx)(aS,{className:"h-4 w-4 opacity-50"})})]}));aU.displayName=ab.displayName;let aV=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aG,{ref:d,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aT,{className:"h-4 w-4"})}));aV.displayName=aG.displayName;let aW=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aI,{ref:d,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aS,{className:"h-4 w-4"})}));aW.displayName=aI.displayName;let aX=c.forwardRef(({className:a,children:c,position:d="popper",...e},f)=>(0,b.jsx)(af,{children:(0,b.jsxs)(ah,{ref:f,className:(0,h.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...e,children:[(0,b.jsx)(aV,{}),(0,b.jsx)(ar,{className:(0,h.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:c}),(0,b.jsx)(aW,{})]})}));aX.displayName=ah.displayName,c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aw,{ref:d,className:(0,h.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...c})).displayName=aw.displayName;let aY=c.forwardRef(({className:a,children:c,...d},e)=>(0,b.jsxs)(aA,{ref:e,className:(0,h.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...d,children:[(0,b.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,b.jsx)(aE,{children:(0,b.jsx)(aQ.default,{className:"h-4 w-4"})})}),(0,b.jsx)(aC,{children:c})]}));aY.displayName=aA.displayName,c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aK,{ref:d,className:(0,h.cn)("-mx-1 my-1 h-px bg-muted",a),...c})).displayName=aK.displayName;var aZ=a.i(2439);function a$(a,b){return"function"==typeof a?a(b):a}function a_(a,b){return c=>{b.setState(b=>({...b,[a]:a$(c,b[a])}))}}function a0(a){return a instanceof Function}function a1(a,b,c){let d,e=[];return f=>{let g,h;c.key&&c.debug&&(g=Date.now());let i=a(f);if(!(i.length!==e.length||i.some((a,b)=>e[b]!==a)))return d;if(e=i,c.key&&c.debug&&(h=Date.now()),d=b(...i),null==c||null==c.onChange||c.onChange(d),c.key&&c.debug&&null!=c&&c.debug()){let a=Math.round((Date.now()-g)*100)/100,b=Math.round((Date.now()-h)*100)/100,d=b/16,e=(a,b)=>{for(a=String(a);a.length<b;)a=" "+a;return a};console.info(`%c⏱ ${e(b,5)} /${e(a,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,null==c?void 0:c.key)}return d}}function a2(a,b,c,d){return{debug:()=>{var c;return null!=(c=null==a?void 0:a.debugAll)?c:a[b]},key:!1,onChange:d}}let a3="debugHeaders";function a4(a,b,c){var d;let e={id:null!=(d=c.id)?d:b.id,column:b,index:c.index,isPlaceholder:!!c.isPlaceholder,placeholderId:c.placeholderId,depth:c.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let a=[],b=c=>{c.subHeaders&&c.subHeaders.length&&c.subHeaders.map(b),a.push(c)};return b(e),a},getContext:()=>({table:a,header:e,column:b})};return a._features.forEach(b=>{null==b.createHeader||b.createHeader(e,a)}),e}function a5(a,b,c,d){var e,f;let g=0,h=function(a,b){void 0===b&&(b=1),g=Math.max(g,b),a.filter(a=>a.getIsVisible()).forEach(a=>{var c;null!=(c=a.columns)&&c.length&&h(a.columns,b+1)},0)};h(a);let i=[],j=(a,b)=>{let e={depth:b,id:[d,`${b}`].filter(Boolean).join("_"),headers:[]},f=[];a.forEach(a=>{let g,h=[...f].reverse()[0],i=a.column.depth===e.depth,j=!1;if(i&&a.column.parent?g=a.column.parent:(g=a.column,j=!0),h&&(null==h?void 0:h.column)===g)h.subHeaders.push(a);else{let e=a4(c,g,{id:[d,b,g.id,null==a?void 0:a.id].filter(Boolean).join("_"),isPlaceholder:j,placeholderId:j?`${f.filter(a=>a.column===g).length}`:void 0,depth:b,index:f.length});e.subHeaders.push(a),f.push(e)}e.headers.push(a),a.headerGroup=e}),i.push(e),b>0&&j(f,b-1)};j(b.map((a,b)=>a4(c,a,{depth:g,index:b})),g-1),i.reverse();let k=a=>a.filter(a=>a.column.getIsVisible()).map(a=>{let b=0,c=0,d=[0];return a.subHeaders&&a.subHeaders.length?(d=[],k(a.subHeaders).forEach(a=>{let{colSpan:c,rowSpan:e}=a;b+=c,d.push(e)})):b=1,c+=Math.min(...d),a.colSpan=b,a.rowSpan=c,{colSpan:b,rowSpan:c}});return k(null!=(e=null==(f=i[0])?void 0:f.headers)?e:[]),i}let a6=(a,b,c,d,e,f,g)=>{let h={id:b,index:d,original:c,depth:e,parentId:g,_valuesCache:{},_uniqueValuesCache:{},getValue:b=>{if(h._valuesCache.hasOwnProperty(b))return h._valuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return h._valuesCache[b]=c.accessorFn(h.original,d),h._valuesCache[b]},getUniqueValues:b=>{if(h._uniqueValuesCache.hasOwnProperty(b))return h._uniqueValuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return c.columnDef.getUniqueValues?h._uniqueValuesCache[b]=c.columnDef.getUniqueValues(h.original,d):h._uniqueValuesCache[b]=[h.getValue(b)],h._uniqueValuesCache[b]},renderValue:b=>{var c;return null!=(c=h.getValue(b))?c:a.options.renderFallbackValue},subRows:null!=f?f:[],getLeafRows:()=>(function(a,b){let c=[],d=a=>{a.forEach(a=>{c.push(a);let e=b(a);null!=e&&e.length&&d(e)})};return d(a),c})(h.subRows,a=>a.subRows),getParentRow:()=>h.parentId?a.getRow(h.parentId,!0):void 0,getParentRows:()=>{let a=[],b=h;for(;;){let c=b.getParentRow();if(!c)break;a.push(c),b=c}return a.reverse()},getAllCells:a1(()=>[a.getAllLeafColumns()],b=>b.map(b=>(function(a,b,c,d){let e={id:`${b.id}_${c.id}`,row:b,column:c,getValue:()=>b.getValue(d),renderValue:()=>{var b;return null!=(b=e.getValue())?b:a.options.renderFallbackValue},getContext:a1(()=>[a,c,b,e],(a,b,c,d)=>({table:a,column:b,row:c,cell:d,getValue:d.getValue,renderValue:d.renderValue}),a2(a.options,"debugCells","cell.getContext"))};return a._features.forEach(d=>{null==d.createCell||d.createCell(e,c,b,a)},{}),e})(a,h,b,b.id)),a2(a.options,"debugRows","getAllCells")),_getAllCellsByColumnId:a1(()=>[h.getAllCells()],a=>a.reduce((a,b)=>(a[b.column.id]=b,a),{}),a2(a.options,"debugRows","getAllCellsByColumnId"))};for(let b=0;b<a._features.length;b++){let c=a._features[b];null==c||null==c.createRow||c.createRow(h,a)}return h},a7=(a,b,c)=>{var d,e;let f=null==c||null==(d=c.toString())?void 0:d.toLowerCase();return!!(null==(e=a.getValue(b))||null==(e=e.toString())||null==(e=e.toLowerCase())?void 0:e.includes(f))};a7.autoRemove=a=>bh(a);let a8=(a,b,c)=>{var d;return!!(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.includes(c))};a8.autoRemove=a=>bh(a);let a9=(a,b,c)=>{var d;return(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.toLowerCase())===(null==c?void 0:c.toLowerCase())};a9.autoRemove=a=>bh(a);let ba=(a,b,c)=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)};ba.autoRemove=a=>bh(a);let bb=(a,b,c)=>!c.some(c=>{var d;return!(null!=(d=a.getValue(b))&&d.includes(c))});bb.autoRemove=a=>bh(a)||!(null!=a&&a.length);let bc=(a,b,c)=>c.some(c=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)});bc.autoRemove=a=>bh(a)||!(null!=a&&a.length);let bd=(a,b,c)=>a.getValue(b)===c;bd.autoRemove=a=>bh(a);let be=(a,b,c)=>a.getValue(b)==c;be.autoRemove=a=>bh(a);let bf=(a,b,c)=>{let[d,e]=c,f=a.getValue(b);return f>=d&&f<=e};bf.resolveFilterValue=a=>{let[b,c]=a,d="number"!=typeof b?parseFloat(b):b,e="number"!=typeof c?parseFloat(c):c,f=null===b||Number.isNaN(d)?-1/0:d,g=null===c||Number.isNaN(e)?1/0:e;if(f>g){let a=f;f=g,g=a}return[f,g]},bf.autoRemove=a=>bh(a)||bh(a[0])&&bh(a[1]);let bg={includesString:a7,includesStringSensitive:a8,equalsString:a9,arrIncludes:ba,arrIncludesAll:bb,arrIncludesSome:bc,equals:bd,weakEquals:be,inNumberRange:bf};function bh(a){return null==a||""===a}function bi(a,b,c){return!!a&&!!a.autoRemove&&a.autoRemove(b,c)||void 0===b||"string"==typeof b&&!b}let bj={sum:(a,b,c)=>c.reduce((b,c)=>{let d=c.getValue(a);return b+("number"==typeof d?d:0)},0),min:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d>c||void 0===d&&c>=c)&&(d=c)}),d},max:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d<c||void 0===d&&c>=c)&&(d=c)}),d},extent:(a,b,c)=>{let d,e;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(void 0===d?c>=c&&(d=e=c):(d>c&&(d=c),e<c&&(e=c)))}),[d,e]},mean:(a,b)=>{let c=0,d=0;if(b.forEach(b=>{let e=b.getValue(a);null!=e&&(e*=1)>=e&&(++c,d+=e)}),c)return d/c},median:(a,b)=>{if(!b.length)return;let c=b.map(b=>b.getValue(a));if(!function(a){return Array.isArray(a)&&a.every(a=>"number"==typeof a)}(c))return;if(1===c.length)return c[0];let d=Math.floor(c.length/2),e=c.sort((a,b)=>a-b);return c.length%2!=0?e[d]:(e[d-1]+e[d])/2},unique:(a,b)=>Array.from(new Set(b.map(b=>b.getValue(a))).values()),uniqueCount:(a,b)=>new Set(b.map(b=>b.getValue(a))).size,count:(a,b)=>b.length},bk=()=>({left:[],right:[]}),bl={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},bm=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),bn=null;function bo(a){return"touchstart"===a.type}function bp(a,b){return b?"center"===b?a.getCenterVisibleLeafColumns():"left"===b?a.getLeftVisibleLeafColumns():a.getRightVisibleLeafColumns():a.getVisibleLeafColumns()}let bq=()=>({pageIndex:0,pageSize:10}),br=()=>({top:[],bottom:[]}),bs=(a,b,c,d,e)=>{var f;let g=e.getRow(b,!0);c?(g.getCanMultiSelect()||Object.keys(a).forEach(b=>delete a[b]),g.getCanSelect()&&(a[b]=!0)):delete a[b],d&&null!=(f=g.subRows)&&f.length&&g.getCanSelectSubRows()&&g.subRows.forEach(b=>bs(a,b.id,c,d,e))};function bt(a,b){let c=a.getState().rowSelection,d=[],e={},f=function(a,b){return a.map(a=>{var b;let g=bu(a,c);if(g&&(d.push(a),e[a.id]=a),null!=(b=a.subRows)&&b.length&&(a={...a,subRows:f(a.subRows)}),g)return a}).filter(Boolean)};return{rows:f(b.rows),flatRows:d,rowsById:e}}function bu(a,b){var c;return null!=(c=b[a.id])&&c}function bv(a,b,c){var d;if(!(null!=(d=a.subRows)&&d.length))return!1;let e=!0,f=!1;return a.subRows.forEach(a=>{if((!f||e)&&(a.getCanSelect()&&(bu(a,b)?f=!0:e=!1),a.subRows&&a.subRows.length)){let c=bv(a,b);"all"===c?f=!0:("some"===c&&(f=!0),e=!1)}}),e?"all":!!f&&"some"}let bw=/([0-9]+)/gm;function bx(a,b){return a===b?0:a>b?1:-1}function by(a){return"number"==typeof a?isNaN(a)||a===1/0||a===-1/0?"":String(a):"string"==typeof a?a:""}function bz(a,b){let c=a.split(bw).filter(Boolean),d=b.split(bw).filter(Boolean);for(;c.length&&d.length;){let a=c.shift(),b=d.shift(),e=parseInt(a,10),f=parseInt(b,10),g=[e,f].sort();if(isNaN(g[0])){if(a>b)return 1;if(b>a)return -1;continue}if(isNaN(g[1]))return isNaN(e)?-1:1;if(e>f)return 1;if(f>e)return -1}return c.length-d.length}let bA={alphanumeric:(a,b,c)=>bz(by(a.getValue(c)).toLowerCase(),by(b.getValue(c)).toLowerCase()),alphanumericCaseSensitive:(a,b,c)=>bz(by(a.getValue(c)),by(b.getValue(c))),text:(a,b,c)=>bx(by(a.getValue(c)).toLowerCase(),by(b.getValue(c)).toLowerCase()),textCaseSensitive:(a,b,c)=>bx(by(a.getValue(c)),by(b.getValue(c))),datetime:(a,b,c)=>{let d=a.getValue(c),e=b.getValue(c);return d>e?1:d<e?-1:0},basic:(a,b,c)=>bx(a.getValue(c),b.getValue(c))},bB=[{createTable:a=>{a.getHeaderGroups=a1(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>{var f,g;let h=null!=(f=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?f:[],i=null!=(g=null==e?void 0:e.map(a=>c.find(b=>b.id===a)).filter(Boolean))?g:[];return a5(b,[...h,...c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),...i],a)},a2(a.options,a3,"getHeaderGroups")),a.getCenterHeaderGroups=a1(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>a5(b,c=c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),a,"center"),a2(a.options,a3,"getCenterHeaderGroups")),a.getLeftHeaderGroups=a1(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left],(b,c,d)=>{var e;return a5(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"left")},a2(a.options,a3,"getLeftHeaderGroups")),a.getRightHeaderGroups=a1(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.right],(b,c,d)=>{var e;return a5(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"right")},a2(a.options,a3,"getRightHeaderGroups")),a.getFooterGroups=a1(()=>[a.getHeaderGroups()],a=>[...a].reverse(),a2(a.options,a3,"getFooterGroups")),a.getLeftFooterGroups=a1(()=>[a.getLeftHeaderGroups()],a=>[...a].reverse(),a2(a.options,a3,"getLeftFooterGroups")),a.getCenterFooterGroups=a1(()=>[a.getCenterHeaderGroups()],a=>[...a].reverse(),a2(a.options,a3,"getCenterFooterGroups")),a.getRightFooterGroups=a1(()=>[a.getRightHeaderGroups()],a=>[...a].reverse(),a2(a.options,a3,"getRightFooterGroups")),a.getFlatHeaders=a1(()=>[a.getHeaderGroups()],a=>a.map(a=>a.headers).flat(),a2(a.options,a3,"getFlatHeaders")),a.getLeftFlatHeaders=a1(()=>[a.getLeftHeaderGroups()],a=>a.map(a=>a.headers).flat(),a2(a.options,a3,"getLeftFlatHeaders")),a.getCenterFlatHeaders=a1(()=>[a.getCenterHeaderGroups()],a=>a.map(a=>a.headers).flat(),a2(a.options,a3,"getCenterFlatHeaders")),a.getRightFlatHeaders=a1(()=>[a.getRightHeaderGroups()],a=>a.map(a=>a.headers).flat(),a2(a.options,a3,"getRightFlatHeaders")),a.getCenterLeafHeaders=a1(()=>[a.getCenterFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),a2(a.options,a3,"getCenterLeafHeaders")),a.getLeftLeafHeaders=a1(()=>[a.getLeftFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),a2(a.options,a3,"getLeftLeafHeaders")),a.getRightLeafHeaders=a1(()=>[a.getRightFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),a2(a.options,a3,"getRightLeafHeaders")),a.getLeafHeaders=a1(()=>[a.getLeftHeaderGroups(),a.getCenterHeaderGroups(),a.getRightHeaderGroups()],(a,b,c)=>{var d,e,f,g,h,i;return[...null!=(d=null==(e=a[0])?void 0:e.headers)?d:[],...null!=(f=null==(g=b[0])?void 0:g.headers)?f:[],...null!=(h=null==(i=c[0])?void 0:i.headers)?h:[]].map(a=>a.getLeafHeaders()).flat()},a2(a.options,a3,"getLeafHeaders"))}},{getInitialState:a=>({columnVisibility:{},...a}),getDefaultOptions:a=>({onColumnVisibilityChange:a_("columnVisibility",a)}),createColumn:(a,b)=>{a.toggleVisibility=c=>{a.getCanHide()&&b.setColumnVisibility(b=>({...b,[a.id]:null!=c?c:!a.getIsVisible()}))},a.getIsVisible=()=>{var c,d;let e=a.columns;return null==(c=e.length?e.some(a=>a.getIsVisible()):null==(d=b.getState().columnVisibility)?void 0:d[a.id])||c},a.getCanHide=()=>{var c,d;return(null==(c=a.columnDef.enableHiding)||c)&&(null==(d=b.options.enableHiding)||d)},a.getToggleVisibilityHandler=()=>b=>{null==a.toggleVisibility||a.toggleVisibility(b.target.checked)}},createRow:(a,b)=>{a._getAllVisibleCells=a1(()=>[a.getAllCells(),b.getState().columnVisibility],a=>a.filter(a=>a.column.getIsVisible()),a2(b.options,"debugRows","_getAllVisibleCells")),a.getVisibleCells=a1(()=>[a.getLeftVisibleCells(),a.getCenterVisibleCells(),a.getRightVisibleCells()],(a,b,c)=>[...a,...b,...c],a2(b.options,"debugRows","getVisibleCells"))},createTable:a=>{let b=(b,c)=>a1(()=>[c(),c().filter(a=>a.getIsVisible()).map(a=>a.id).join("_")],a=>a.filter(a=>null==a.getIsVisible?void 0:a.getIsVisible()),a2(a.options,"debugColumns",b));a.getVisibleFlatColumns=b("getVisibleFlatColumns",()=>a.getAllFlatColumns()),a.getVisibleLeafColumns=b("getVisibleLeafColumns",()=>a.getAllLeafColumns()),a.getLeftVisibleLeafColumns=b("getLeftVisibleLeafColumns",()=>a.getLeftLeafColumns()),a.getRightVisibleLeafColumns=b("getRightVisibleLeafColumns",()=>a.getRightLeafColumns()),a.getCenterVisibleLeafColumns=b("getCenterVisibleLeafColumns",()=>a.getCenterLeafColumns()),a.setColumnVisibility=b=>null==a.options.onColumnVisibilityChange?void 0:a.options.onColumnVisibilityChange(b),a.resetColumnVisibility=b=>{var c;a.setColumnVisibility(b?{}:null!=(c=a.initialState.columnVisibility)?c:{})},a.toggleAllColumnsVisible=b=>{var c;b=null!=(c=b)?c:!a.getIsAllColumnsVisible(),a.setColumnVisibility(a.getAllLeafColumns().reduce((a,c)=>({...a,[c.id]:b||!(null!=c.getCanHide&&c.getCanHide())}),{}))},a.getIsAllColumnsVisible=()=>!a.getAllLeafColumns().some(a=>!(null!=a.getIsVisible&&a.getIsVisible())),a.getIsSomeColumnsVisible=()=>a.getAllLeafColumns().some(a=>null==a.getIsVisible?void 0:a.getIsVisible()),a.getToggleAllColumnsVisibilityHandler=()=>b=>{var c;a.toggleAllColumnsVisible(null==(c=b.target)?void 0:c.checked)}}},{getInitialState:a=>({columnOrder:[],...a}),getDefaultOptions:a=>({onColumnOrderChange:a_("columnOrder",a)}),createColumn:(a,b)=>{a.getIndex=a1(a=>[bp(b,a)],b=>b.findIndex(b=>b.id===a.id),a2(b.options,"debugColumns","getIndex")),a.getIsFirstColumn=c=>{var d;return(null==(d=bp(b,c)[0])?void 0:d.id)===a.id},a.getIsLastColumn=c=>{var d;let e=bp(b,c);return(null==(d=e[e.length-1])?void 0:d.id)===a.id}},createTable:a=>{a.setColumnOrder=b=>null==a.options.onColumnOrderChange?void 0:a.options.onColumnOrderChange(b),a.resetColumnOrder=b=>{var c;a.setColumnOrder(b?[]:null!=(c=a.initialState.columnOrder)?c:[])},a._getOrderColumnsFn=a1(()=>[a.getState().columnOrder,a.getState().grouping,a.options.groupedColumnMode],(a,b,c)=>d=>{let e=[];if(null!=a&&a.length){let b=[...a],c=[...d];for(;c.length&&b.length;){let a=b.shift(),d=c.findIndex(b=>b.id===a);d>-1&&e.push(c.splice(d,1)[0])}e=[...e,...c]}else e=d;return function(a,b,c){if(!(null!=b&&b.length)||!c)return a;let d=a.filter(a=>!b.includes(a.id));return"remove"===c?d:[...b.map(b=>a.find(a=>a.id===b)).filter(Boolean),...d]}(e,b,c)},a2(a.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:a=>({columnPinning:bk(),...a}),getDefaultOptions:a=>({onColumnPinningChange:a_("columnPinning",a)}),createColumn:(a,b)=>{a.pin=c=>{let d=a.getLeafColumns().map(a=>a.id).filter(Boolean);b.setColumnPinning(a=>{var b,e,f,g,h,i;return"right"===c?{left:(null!=(f=null==a?void 0:a.left)?f:[]).filter(a=>!(null!=d&&d.includes(a))),right:[...(null!=(g=null==a?void 0:a.right)?g:[]).filter(a=>!(null!=d&&d.includes(a))),...d]}:"left"===c?{left:[...(null!=(h=null==a?void 0:a.left)?h:[]).filter(a=>!(null!=d&&d.includes(a))),...d],right:(null!=(i=null==a?void 0:a.right)?i:[]).filter(a=>!(null!=d&&d.includes(a)))}:{left:(null!=(b=null==a?void 0:a.left)?b:[]).filter(a=>!(null!=d&&d.includes(a))),right:(null!=(e=null==a?void 0:a.right)?e:[]).filter(a=>!(null!=d&&d.includes(a)))}})},a.getCanPin=()=>a.getLeafColumns().some(a=>{var c,d,e;return(null==(c=a.columnDef.enablePinning)||c)&&(null==(d=null!=(e=b.options.enableColumnPinning)?e:b.options.enablePinning)||d)}),a.getIsPinned=()=>{let c=a.getLeafColumns().map(a=>a.id),{left:d,right:e}=b.getState().columnPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"left":!!g&&"right"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();return e?null!=(c=null==(d=b.getState().columnPinning)||null==(d=d[e])?void 0:d.indexOf(a.id))?c:-1:0}},createRow:(a,b)=>{a.getCenterVisibleCells=a1(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left,b.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.column.id))},a2(b.options,"debugRows","getCenterVisibleCells")),a.getLeftVisibleCells=a1(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"left"})),a2(b.options,"debugRows","getLeftVisibleCells")),a.getRightVisibleCells=a1(()=>[a._getAllVisibleCells(),b.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"right"})),a2(b.options,"debugRows","getRightVisibleCells"))},createTable:a=>{a.setColumnPinning=b=>null==a.options.onColumnPinningChange?void 0:a.options.onColumnPinningChange(b),a.resetColumnPinning=b=>{var c,d;return a.setColumnPinning(b?bk():null!=(c=null==(d=a.initialState)?void 0:d.columnPinning)?c:bk())},a.getIsSomeColumnsPinned=b=>{var c,d,e;let f=a.getState().columnPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.left)?void 0:d.length)||(null==(e=f.right)?void 0:e.length))},a.getLeftLeafColumns=a1(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),a2(a.options,"debugColumns","getLeftLeafColumns")),a.getRightLeafColumns=a1(()=>[a.getAllLeafColumns(),a.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),a2(a.options,"debugColumns","getRightLeafColumns")),a.getCenterLeafColumns=a1(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.id))},a2(a.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(a,b)=>{a._getFacetedRowModel=b.options.getFacetedRowModel&&b.options.getFacetedRowModel(b,a.id),a.getFacetedRowModel=()=>a._getFacetedRowModel?a._getFacetedRowModel():b.getPreFilteredRowModel(),a._getFacetedUniqueValues=b.options.getFacetedUniqueValues&&b.options.getFacetedUniqueValues(b,a.id),a.getFacetedUniqueValues=()=>a._getFacetedUniqueValues?a._getFacetedUniqueValues():new Map,a._getFacetedMinMaxValues=b.options.getFacetedMinMaxValues&&b.options.getFacetedMinMaxValues(b,a.id),a.getFacetedMinMaxValues=()=>{if(a._getFacetedMinMaxValues)return a._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:a=>({columnFilters:[],...a}),getDefaultOptions:a=>({onColumnFiltersChange:a_("columnFilters",a),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(a,b)=>{a.getAutoFilterFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"string"==typeof d?bg.includesString:"number"==typeof d?bg.inNumberRange:"boolean"==typeof d||null!==d&&"object"==typeof d?bg.equals:Array.isArray(d)?bg.arrIncludes:bg.weakEquals},a.getFilterFn=()=>{var c,d;return a0(a.columnDef.filterFn)?a.columnDef.filterFn:"auto"===a.columnDef.filterFn?a.getAutoFilterFn():null!=(c=null==(d=b.options.filterFns)?void 0:d[a.columnDef.filterFn])?c:bg[a.columnDef.filterFn]},a.getCanFilter=()=>{var c,d,e;return(null==(c=a.columnDef.enableColumnFilter)||c)&&(null==(d=b.options.enableColumnFilters)||d)&&(null==(e=b.options.enableFilters)||e)&&!!a.accessorFn},a.getIsFiltered=()=>a.getFilterIndex()>-1,a.getFilterValue=()=>{var c;return null==(c=b.getState().columnFilters)||null==(c=c.find(b=>b.id===a.id))?void 0:c.value},a.getFilterIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().columnFilters)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.setFilterValue=c=>{b.setColumnFilters(b=>{var d,e;let f=a.getFilterFn(),g=null==b?void 0:b.find(b=>b.id===a.id),h=a$(c,g?g.value:void 0);if(bi(f,h,a))return null!=(d=null==b?void 0:b.filter(b=>b.id!==a.id))?d:[];let i={id:a.id,value:h};return g?null!=(e=null==b?void 0:b.map(b=>b.id===a.id?i:b))?e:[]:null!=b&&b.length?[...b,i]:[i]})}},createRow:(a,b)=>{a.columnFilters={},a.columnFiltersMeta={}},createTable:a=>{a.setColumnFilters=b=>{let c=a.getAllLeafColumns();null==a.options.onColumnFiltersChange||a.options.onColumnFiltersChange(a=>{var d;return null==(d=a$(b,a))?void 0:d.filter(a=>{let b=c.find(b=>b.id===a.id);return!(b&&bi(b.getFilterFn(),a.value,b))&&!0})})},a.resetColumnFilters=b=>{var c,d;a.setColumnFilters(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.columnFilters)?c:[])},a.getPreFilteredRowModel=()=>a.getCoreRowModel(),a.getFilteredRowModel=()=>(!a._getFilteredRowModel&&a.options.getFilteredRowModel&&(a._getFilteredRowModel=a.options.getFilteredRowModel(a)),a.options.manualFiltering||!a._getFilteredRowModel)?a.getPreFilteredRowModel():a._getFilteredRowModel()}},{createTable:a=>{a._getGlobalFacetedRowModel=a.options.getFacetedRowModel&&a.options.getFacetedRowModel(a,"__global__"),a.getGlobalFacetedRowModel=()=>a.options.manualFiltering||!a._getGlobalFacetedRowModel?a.getPreFilteredRowModel():a._getGlobalFacetedRowModel(),a._getGlobalFacetedUniqueValues=a.options.getFacetedUniqueValues&&a.options.getFacetedUniqueValues(a,"__global__"),a.getGlobalFacetedUniqueValues=()=>a._getGlobalFacetedUniqueValues?a._getGlobalFacetedUniqueValues():new Map,a._getGlobalFacetedMinMaxValues=a.options.getFacetedMinMaxValues&&a.options.getFacetedMinMaxValues(a,"__global__"),a.getGlobalFacetedMinMaxValues=()=>{if(a._getGlobalFacetedMinMaxValues)return a._getGlobalFacetedMinMaxValues()}}},{getInitialState:a=>({globalFilter:void 0,...a}),getDefaultOptions:a=>({onGlobalFilterChange:a_("globalFilter",a),globalFilterFn:"auto",getColumnCanGlobalFilter:b=>{var c;let d=null==(c=a.getCoreRowModel().flatRows[0])||null==(c=c._getAllCellsByColumnId()[b.id])?void 0:c.getValue();return"string"==typeof d||"number"==typeof d}}),createColumn:(a,b)=>{a.getCanGlobalFilter=()=>{var c,d,e,f;return(null==(c=a.columnDef.enableGlobalFilter)||c)&&(null==(d=b.options.enableGlobalFilter)||d)&&(null==(e=b.options.enableFilters)||e)&&(null==(f=null==b.options.getColumnCanGlobalFilter?void 0:b.options.getColumnCanGlobalFilter(a))||f)&&!!a.accessorFn}},createTable:a=>{a.getGlobalAutoFilterFn=()=>bg.includesString,a.getGlobalFilterFn=()=>{var b,c;let{globalFilterFn:d}=a.options;return a0(d)?d:"auto"===d?a.getGlobalAutoFilterFn():null!=(b=null==(c=a.options.filterFns)?void 0:c[d])?b:bg[d]},a.setGlobalFilter=b=>{null==a.options.onGlobalFilterChange||a.options.onGlobalFilterChange(b)},a.resetGlobalFilter=b=>{a.setGlobalFilter(b?void 0:a.initialState.globalFilter)}}},{getInitialState:a=>({sorting:[],...a}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:a=>({onSortingChange:a_("sorting",a),isMultiSortEvent:a=>a.shiftKey}),createColumn:(a,b)=>{a.getAutoSortingFn=()=>{let c=b.getFilteredRowModel().flatRows.slice(10),d=!1;for(let b of c){let c=null==b?void 0:b.getValue(a.id);if("[object Date]"===Object.prototype.toString.call(c))return bA.datetime;if("string"==typeof c&&(d=!0,c.split(bw).length>1))return bA.alphanumeric}return d?bA.text:bA.basic},a.getAutoSortDir=()=>{let c=b.getFilteredRowModel().flatRows[0];return"string"==typeof(null==c?void 0:c.getValue(a.id))?"asc":"desc"},a.getSortingFn=()=>{var c,d;if(!a)throw Error();return a0(a.columnDef.sortingFn)?a.columnDef.sortingFn:"auto"===a.columnDef.sortingFn?a.getAutoSortingFn():null!=(c=null==(d=b.options.sortingFns)?void 0:d[a.columnDef.sortingFn])?c:bA[a.columnDef.sortingFn]},a.toggleSorting=(c,d)=>{let e=a.getNextSortingOrder(),f=null!=c;b.setSorting(g=>{let h,i=null==g?void 0:g.find(b=>b.id===a.id),j=null==g?void 0:g.findIndex(b=>b.id===a.id),k=[],l=f?c:"desc"===e;if("toggle"!=(h=null!=g&&g.length&&a.getCanMultiSort()&&d?i?"toggle":"add":null!=g&&g.length&&j!==g.length-1?"replace":i?"toggle":"replace")||f||e||(h="remove"),"add"===h){var m;(k=[...g,{id:a.id,desc:l}]).splice(0,k.length-(null!=(m=b.options.maxMultiSortColCount)?m:Number.MAX_SAFE_INTEGER))}else k="toggle"===h?g.map(b=>b.id===a.id?{...b,desc:l}:b):"remove"===h?g.filter(b=>b.id!==a.id):[{id:a.id,desc:l}];return k})},a.getFirstSortDir=()=>{var c,d;return(null!=(c=null!=(d=a.columnDef.sortDescFirst)?d:b.options.sortDescFirst)?c:"desc"===a.getAutoSortDir())?"desc":"asc"},a.getNextSortingOrder=c=>{var d,e;let f=a.getFirstSortDir(),g=a.getIsSorted();return g?(g===f||null!=(d=b.options.enableSortingRemoval)&&!d||!!c&&null!=(e=b.options.enableMultiRemove)&&!e)&&("desc"===g?"asc":"desc"):f},a.getCanSort=()=>{var c,d;return(null==(c=a.columnDef.enableSorting)||c)&&(null==(d=b.options.enableSorting)||d)&&!!a.accessorFn},a.getCanMultiSort=()=>{var c,d;return null!=(c=null!=(d=a.columnDef.enableMultiSort)?d:b.options.enableMultiSort)?c:!!a.accessorFn},a.getIsSorted=()=>{var c;let d=null==(c=b.getState().sorting)?void 0:c.find(b=>b.id===a.id);return!!d&&(d.desc?"desc":"asc")},a.getSortIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().sorting)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.clearSorting=()=>{b.setSorting(b=>null!=b&&b.length?b.filter(b=>b.id!==a.id):[])},a.getToggleSortingHandler=()=>{let c=a.getCanSort();return d=>{c&&(null==d.persist||d.persist(),null==a.toggleSorting||a.toggleSorting(void 0,!!a.getCanMultiSort()&&(null==b.options.isMultiSortEvent?void 0:b.options.isMultiSortEvent(d))))}}},createTable:a=>{a.setSorting=b=>null==a.options.onSortingChange?void 0:a.options.onSortingChange(b),a.resetSorting=b=>{var c,d;a.setSorting(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.sorting)?c:[])},a.getPreSortedRowModel=()=>a.getGroupedRowModel(),a.getSortedRowModel=()=>(!a._getSortedRowModel&&a.options.getSortedRowModel&&(a._getSortedRowModel=a.options.getSortedRowModel(a)),a.options.manualSorting||!a._getSortedRowModel)?a.getPreSortedRowModel():a._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:a=>{var b,c;return null!=(b=null==(c=a.getValue())||null==c.toString?void 0:c.toString())?b:null},aggregationFn:"auto"}),getInitialState:a=>({grouping:[],...a}),getDefaultOptions:a=>({onGroupingChange:a_("grouping",a),groupedColumnMode:"reorder"}),createColumn:(a,b)=>{a.toggleGrouping=()=>{b.setGrouping(b=>null!=b&&b.includes(a.id)?b.filter(b=>b!==a.id):[...null!=b?b:[],a.id])},a.getCanGroup=()=>{var c,d;return(null==(c=a.columnDef.enableGrouping)||c)&&(null==(d=b.options.enableGrouping)||d)&&(!!a.accessorFn||!!a.columnDef.getGroupingValue)},a.getIsGrouped=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.includes(a.id)},a.getGroupedIndex=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.indexOf(a.id)},a.getToggleGroupingHandler=()=>{let b=a.getCanGroup();return()=>{b&&a.toggleGrouping()}},a.getAutoAggregationFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"number"==typeof d?bj.sum:"[object Date]"===Object.prototype.toString.call(d)?bj.extent:void 0},a.getAggregationFn=()=>{var c,d;if(!a)throw Error();return a0(a.columnDef.aggregationFn)?a.columnDef.aggregationFn:"auto"===a.columnDef.aggregationFn?a.getAutoAggregationFn():null!=(c=null==(d=b.options.aggregationFns)?void 0:d[a.columnDef.aggregationFn])?c:bj[a.columnDef.aggregationFn]}},createTable:a=>{a.setGrouping=b=>null==a.options.onGroupingChange?void 0:a.options.onGroupingChange(b),a.resetGrouping=b=>{var c,d;a.setGrouping(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.grouping)?c:[])},a.getPreGroupedRowModel=()=>a.getFilteredRowModel(),a.getGroupedRowModel=()=>(!a._getGroupedRowModel&&a.options.getGroupedRowModel&&(a._getGroupedRowModel=a.options.getGroupedRowModel(a)),a.options.manualGrouping||!a._getGroupedRowModel)?a.getPreGroupedRowModel():a._getGroupedRowModel()},createRow:(a,b)=>{a.getIsGrouped=()=>!!a.groupingColumnId,a.getGroupingValue=c=>{if(a._groupingValuesCache.hasOwnProperty(c))return a._groupingValuesCache[c];let d=b.getColumn(c);return null!=d&&d.columnDef.getGroupingValue?(a._groupingValuesCache[c]=d.columnDef.getGroupingValue(a.original),a._groupingValuesCache[c]):a.getValue(c)},a._groupingValuesCache={}},createCell:(a,b,c,d)=>{a.getIsGrouped=()=>b.getIsGrouped()&&b.id===c.groupingColumnId,a.getIsPlaceholder=()=>!a.getIsGrouped()&&b.getIsGrouped(),a.getIsAggregated=()=>{var b;return!a.getIsGrouped()&&!a.getIsPlaceholder()&&!!(null!=(b=c.subRows)&&b.length)}}},{getInitialState:a=>({expanded:{},...a}),getDefaultOptions:a=>({onExpandedChange:a_("expanded",a),paginateExpandedRows:!0}),createTable:a=>{let b=!1,c=!1;a._autoResetExpanded=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetExpanded)?d:!a.options.manualExpanding){if(c)return;c=!0,a._queue(()=>{a.resetExpanded(),c=!1})}},a.setExpanded=b=>null==a.options.onExpandedChange?void 0:a.options.onExpandedChange(b),a.toggleAllRowsExpanded=b=>{(null!=b?b:!a.getIsAllRowsExpanded())?a.setExpanded(!0):a.setExpanded({})},a.resetExpanded=b=>{var c,d;a.setExpanded(b?{}:null!=(c=null==(d=a.initialState)?void 0:d.expanded)?c:{})},a.getCanSomeRowsExpand=()=>a.getPrePaginationRowModel().flatRows.some(a=>a.getCanExpand()),a.getToggleAllRowsExpandedHandler=()=>b=>{null==b.persist||b.persist(),a.toggleAllRowsExpanded()},a.getIsSomeRowsExpanded=()=>{let b=a.getState().expanded;return!0===b||Object.values(b).some(Boolean)},a.getIsAllRowsExpanded=()=>{let b=a.getState().expanded;return"boolean"==typeof b?!0===b:!(!Object.keys(b).length||a.getRowModel().flatRows.some(a=>!a.getIsExpanded()))},a.getExpandedDepth=()=>{let b=0;return(!0===a.getState().expanded?Object.keys(a.getRowModel().rowsById):Object.keys(a.getState().expanded)).forEach(a=>{let c=a.split(".");b=Math.max(b,c.length)}),b},a.getPreExpandedRowModel=()=>a.getSortedRowModel(),a.getExpandedRowModel=()=>(!a._getExpandedRowModel&&a.options.getExpandedRowModel&&(a._getExpandedRowModel=a.options.getExpandedRowModel(a)),a.options.manualExpanding||!a._getExpandedRowModel)?a.getPreExpandedRowModel():a._getExpandedRowModel()},createRow:(a,b)=>{a.toggleExpanded=c=>{b.setExpanded(d=>{var e;let f=!0===d||!!(null!=d&&d[a.id]),g={};if(!0===d?Object.keys(b.getRowModel().rowsById).forEach(a=>{g[a]=!0}):g=d,c=null!=(e=c)?e:!f,!f&&c)return{...g,[a.id]:!0};if(f&&!c){let{[a.id]:b,...c}=g;return c}return d})},a.getIsExpanded=()=>{var c;let d=b.getState().expanded;return!!(null!=(c=null==b.options.getIsRowExpanded?void 0:b.options.getIsRowExpanded(a))?c:!0===d||(null==d?void 0:d[a.id]))},a.getCanExpand=()=>{var c,d,e;return null!=(c=null==b.options.getRowCanExpand?void 0:b.options.getRowCanExpand(a))?c:(null==(d=b.options.enableExpanding)||d)&&!!(null!=(e=a.subRows)&&e.length)},a.getIsAllParentsExpanded=()=>{let c=!0,d=a;for(;c&&d.parentId;)c=(d=b.getRow(d.parentId,!0)).getIsExpanded();return c},a.getToggleExpandedHandler=()=>{let b=a.getCanExpand();return()=>{b&&a.toggleExpanded()}}}},{getInitialState:a=>({...a,pagination:{...bq(),...null==a?void 0:a.pagination}}),getDefaultOptions:a=>({onPaginationChange:a_("pagination",a)}),createTable:a=>{let b=!1,c=!1;a._autoResetPageIndex=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetPageIndex)?d:!a.options.manualPagination){if(c)return;c=!0,a._queue(()=>{a.resetPageIndex(),c=!1})}},a.setPagination=b=>null==a.options.onPaginationChange?void 0:a.options.onPaginationChange(a=>a$(b,a)),a.resetPagination=b=>{var c;a.setPagination(b?bq():null!=(c=a.initialState.pagination)?c:bq())},a.setPageIndex=b=>{a.setPagination(c=>{let d=a$(b,c.pageIndex);return d=Math.max(0,Math.min(d,void 0===a.options.pageCount||-1===a.options.pageCount?Number.MAX_SAFE_INTEGER:a.options.pageCount-1)),{...c,pageIndex:d}})},a.resetPageIndex=b=>{var c,d;a.setPageIndex(b?0:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageIndex)?c:0)},a.resetPageSize=b=>{var c,d;a.setPageSize(b?10:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageSize)?c:10)},a.setPageSize=b=>{a.setPagination(a=>{let c=Math.max(1,a$(b,a.pageSize)),d=Math.floor(a.pageSize*a.pageIndex/c);return{...a,pageIndex:d,pageSize:c}})},a.setPageCount=b=>a.setPagination(c=>{var d;let e=a$(b,null!=(d=a.options.pageCount)?d:-1);return"number"==typeof e&&(e=Math.max(-1,e)),{...c,pageCount:e}}),a.getPageOptions=a1(()=>[a.getPageCount()],a=>{let b=[];return a&&a>0&&(b=[...Array(a)].fill(null).map((a,b)=>b)),b},a2(a.options,"debugTable","getPageOptions")),a.getCanPreviousPage=()=>a.getState().pagination.pageIndex>0,a.getCanNextPage=()=>{let{pageIndex:b}=a.getState().pagination,c=a.getPageCount();return -1===c||0!==c&&b<c-1},a.previousPage=()=>a.setPageIndex(a=>a-1),a.nextPage=()=>a.setPageIndex(a=>a+1),a.firstPage=()=>a.setPageIndex(0),a.lastPage=()=>a.setPageIndex(a.getPageCount()-1),a.getPrePaginationRowModel=()=>a.getExpandedRowModel(),a.getPaginationRowModel=()=>(!a._getPaginationRowModel&&a.options.getPaginationRowModel&&(a._getPaginationRowModel=a.options.getPaginationRowModel(a)),a.options.manualPagination||!a._getPaginationRowModel)?a.getPrePaginationRowModel():a._getPaginationRowModel(),a.getPageCount=()=>{var b;return null!=(b=a.options.pageCount)?b:Math.ceil(a.getRowCount()/a.getState().pagination.pageSize)},a.getRowCount=()=>{var b;return null!=(b=a.options.rowCount)?b:a.getPrePaginationRowModel().rows.length}}},{getInitialState:a=>({rowPinning:br(),...a}),getDefaultOptions:a=>({onRowPinningChange:a_("rowPinning",a)}),createRow:(a,b)=>{a.pin=(c,d,e)=>{let f=d?a.getLeafRows().map(a=>{let{id:b}=a;return b}):[],g=new Set([...e?a.getParentRows().map(a=>{let{id:b}=a;return b}):[],a.id,...f]);b.setRowPinning(a=>{var b,d,e,f,h,i;return"bottom"===c?{top:(null!=(e=null==a?void 0:a.top)?e:[]).filter(a=>!(null!=g&&g.has(a))),bottom:[...(null!=(f=null==a?void 0:a.bottom)?f:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)]}:"top"===c?{top:[...(null!=(h=null==a?void 0:a.top)?h:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)],bottom:(null!=(i=null==a?void 0:a.bottom)?i:[]).filter(a=>!(null!=g&&g.has(a)))}:{top:(null!=(b=null==a?void 0:a.top)?b:[]).filter(a=>!(null!=g&&g.has(a))),bottom:(null!=(d=null==a?void 0:a.bottom)?d:[]).filter(a=>!(null!=g&&g.has(a)))}})},a.getCanPin=()=>{var c;let{enableRowPinning:d,enablePinning:e}=b.options;return"function"==typeof d?d(a):null==(c=null!=d?d:e)||c},a.getIsPinned=()=>{let c=[a.id],{top:d,bottom:e}=b.getState().rowPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"top":!!g&&"bottom"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();if(!e)return -1;let f=null==(c="top"===e?b.getTopRows():b.getBottomRows())?void 0:c.map(a=>{let{id:b}=a;return b});return null!=(d=null==f?void 0:f.indexOf(a.id))?d:-1}},createTable:a=>{a.setRowPinning=b=>null==a.options.onRowPinningChange?void 0:a.options.onRowPinningChange(b),a.resetRowPinning=b=>{var c,d;return a.setRowPinning(b?br():null!=(c=null==(d=a.initialState)?void 0:d.rowPinning)?c:br())},a.getIsSomeRowsPinned=b=>{var c,d,e;let f=a.getState().rowPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.top)?void 0:d.length)||(null==(e=f.bottom)?void 0:e.length))},a._getPinnedRows=(b,c,d)=>{var e;return(null==(e=a.options.keepPinnedRows)||e?(null!=c?c:[]).map(b=>{let c=a.getRow(b,!0);return c.getIsAllParentsExpanded()?c:null}):(null!=c?c:[]).map(a=>b.find(b=>b.id===a))).filter(Boolean).map(a=>({...a,position:d}))},a.getTopRows=a1(()=>[a.getRowModel().rows,a.getState().rowPinning.top],(b,c)=>a._getPinnedRows(b,c,"top"),a2(a.options,"debugRows","getTopRows")),a.getBottomRows=a1(()=>[a.getRowModel().rows,a.getState().rowPinning.bottom],(b,c)=>a._getPinnedRows(b,c,"bottom"),a2(a.options,"debugRows","getBottomRows")),a.getCenterRows=a1(()=>[a.getRowModel().rows,a.getState().rowPinning.top,a.getState().rowPinning.bottom],(a,b,c)=>{let d=new Set([...null!=b?b:[],...null!=c?c:[]]);return a.filter(a=>!d.has(a.id))},a2(a.options,"debugRows","getCenterRows"))}},{getInitialState:a=>({rowSelection:{},...a}),getDefaultOptions:a=>({onRowSelectionChange:a_("rowSelection",a),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:a=>{a.setRowSelection=b=>null==a.options.onRowSelectionChange?void 0:a.options.onRowSelectionChange(b),a.resetRowSelection=b=>{var c;return a.setRowSelection(b?{}:null!=(c=a.initialState.rowSelection)?c:{})},a.toggleAllRowsSelected=b=>{a.setRowSelection(c=>{b=void 0!==b?b:!a.getIsAllRowsSelected();let d={...c},e=a.getPreGroupedRowModel().flatRows;return b?e.forEach(a=>{a.getCanSelect()&&(d[a.id]=!0)}):e.forEach(a=>{delete d[a.id]}),d})},a.toggleAllPageRowsSelected=b=>a.setRowSelection(c=>{let d=void 0!==b?b:!a.getIsAllPageRowsSelected(),e={...c};return a.getRowModel().rows.forEach(b=>{bs(e,b.id,d,!0,a)}),e}),a.getPreSelectedRowModel=()=>a.getCoreRowModel(),a.getSelectedRowModel=a1(()=>[a.getState().rowSelection,a.getCoreRowModel()],(b,c)=>Object.keys(b).length?bt(a,c):{rows:[],flatRows:[],rowsById:{}},a2(a.options,"debugTable","getSelectedRowModel")),a.getFilteredSelectedRowModel=a1(()=>[a.getState().rowSelection,a.getFilteredRowModel()],(b,c)=>Object.keys(b).length?bt(a,c):{rows:[],flatRows:[],rowsById:{}},a2(a.options,"debugTable","getFilteredSelectedRowModel")),a.getGroupedSelectedRowModel=a1(()=>[a.getState().rowSelection,a.getSortedRowModel()],(b,c)=>Object.keys(b).length?bt(a,c):{rows:[],flatRows:[],rowsById:{}},a2(a.options,"debugTable","getGroupedSelectedRowModel")),a.getIsAllRowsSelected=()=>{let b=a.getFilteredRowModel().flatRows,{rowSelection:c}=a.getState(),d=!!(b.length&&Object.keys(c).length);return d&&b.some(a=>a.getCanSelect()&&!c[a.id])&&(d=!1),d},a.getIsAllPageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows.filter(a=>a.getCanSelect()),{rowSelection:c}=a.getState(),d=!!b.length;return d&&b.some(a=>!c[a.id])&&(d=!1),d},a.getIsSomeRowsSelected=()=>{var b;let c=Object.keys(null!=(b=a.getState().rowSelection)?b:{}).length;return c>0&&c<a.getFilteredRowModel().flatRows.length},a.getIsSomePageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows;return!a.getIsAllPageRowsSelected()&&b.filter(a=>a.getCanSelect()).some(a=>a.getIsSelected()||a.getIsSomeSelected())},a.getToggleAllRowsSelectedHandler=()=>b=>{a.toggleAllRowsSelected(b.target.checked)},a.getToggleAllPageRowsSelectedHandler=()=>b=>{a.toggleAllPageRowsSelected(b.target.checked)}},createRow:(a,b)=>{a.toggleSelected=(c,d)=>{let e=a.getIsSelected();b.setRowSelection(f=>{var g;if(c=void 0!==c?c:!e,a.getCanSelect()&&e===c)return f;let h={...f};return bs(h,a.id,c,null==(g=null==d?void 0:d.selectChildren)||g,b),h})},a.getIsSelected=()=>{let{rowSelection:c}=b.getState();return bu(a,c)},a.getIsSomeSelected=()=>{let{rowSelection:c}=b.getState();return"some"===bv(a,c)},a.getIsAllSubRowsSelected=()=>{let{rowSelection:c}=b.getState();return"all"===bv(a,c)},a.getCanSelect=()=>{var c;return"function"==typeof b.options.enableRowSelection?b.options.enableRowSelection(a):null==(c=b.options.enableRowSelection)||c},a.getCanSelectSubRows=()=>{var c;return"function"==typeof b.options.enableSubRowSelection?b.options.enableSubRowSelection(a):null==(c=b.options.enableSubRowSelection)||c},a.getCanMultiSelect=()=>{var c;return"function"==typeof b.options.enableMultiRowSelection?b.options.enableMultiRowSelection(a):null==(c=b.options.enableMultiRowSelection)||c},a.getToggleSelectedHandler=()=>{let b=a.getCanSelect();return c=>{var d;b&&a.toggleSelected(null==(d=c.target)?void 0:d.checked)}}}},{getDefaultColumnDef:()=>bl,getInitialState:a=>({columnSizing:{},columnSizingInfo:bm(),...a}),getDefaultOptions:a=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:a_("columnSizing",a),onColumnSizingInfoChange:a_("columnSizingInfo",a)}),createColumn:(a,b)=>{a.getSize=()=>{var c,d,e;let f=b.getState().columnSizing[a.id];return Math.min(Math.max(null!=(c=a.columnDef.minSize)?c:bl.minSize,null!=(d=null!=f?f:a.columnDef.size)?d:bl.size),null!=(e=a.columnDef.maxSize)?e:bl.maxSize)},a.getStart=a1(a=>[a,bp(b,a),b.getState().columnSizing],(b,c)=>c.slice(0,a.getIndex(b)).reduce((a,b)=>a+b.getSize(),0),a2(b.options,"debugColumns","getStart")),a.getAfter=a1(a=>[a,bp(b,a),b.getState().columnSizing],(b,c)=>c.slice(a.getIndex(b)+1).reduce((a,b)=>a+b.getSize(),0),a2(b.options,"debugColumns","getAfter")),a.resetSize=()=>{b.setColumnSizing(b=>{let{[a.id]:c,...d}=b;return d})},a.getCanResize=()=>{var c,d;return(null==(c=a.columnDef.enableResizing)||c)&&(null==(d=b.options.enableColumnResizing)||d)},a.getIsResizing=()=>b.getState().columnSizingInfo.isResizingColumn===a.id},createHeader:(a,b)=>{a.getSize=()=>{let b=0,c=a=>{if(a.subHeaders.length)a.subHeaders.forEach(c);else{var d;b+=null!=(d=a.column.getSize())?d:0}};return c(a),b},a.getStart=()=>{if(a.index>0){let b=a.headerGroup.headers[a.index-1];return b.getStart()+b.getSize()}return 0},a.getResizeHandler=c=>{let d=b.getColumn(a.column.id),e=null==d?void 0:d.getCanResize();return f=>{if(!d||!e||(null==f.persist||f.persist(),bo(f)&&f.touches&&f.touches.length>1))return;let g=a.getSize(),h=a?a.getLeafHeaders().map(a=>[a.column.id,a.column.getSize()]):[[d.id,d.getSize()]],i=bo(f)?Math.round(f.touches[0].clientX):f.clientX,j={},k=(a,c)=>{"number"==typeof c&&(b.setColumnSizingInfo(a=>{var d,e;let f="rtl"===b.options.columnResizeDirection?-1:1,g=(c-(null!=(d=null==a?void 0:a.startOffset)?d:0))*f,h=Math.max(g/(null!=(e=null==a?void 0:a.startSize)?e:0),-.999999);return a.columnSizingStart.forEach(a=>{let[b,c]=a;j[b]=Math.round(100*Math.max(c+c*h,0))/100}),{...a,deltaOffset:g,deltaPercentage:h}}),("onChange"===b.options.columnResizeMode||"end"===a)&&b.setColumnSizing(a=>({...a,...j})))},l=a=>k("move",a),m=a=>{k("end",a),b.setColumnSizingInfo(a=>({...a,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},n=c||("undefined"!=typeof document?document:null),o={moveHandler:a=>l(a.clientX),upHandler:a=>{null==n||n.removeEventListener("mousemove",o.moveHandler),null==n||n.removeEventListener("mouseup",o.upHandler),m(a.clientX)}},p={moveHandler:a=>(a.cancelable&&(a.preventDefault(),a.stopPropagation()),l(a.touches[0].clientX),!1),upHandler:a=>{var b;null==n||n.removeEventListener("touchmove",p.moveHandler),null==n||n.removeEventListener("touchend",p.upHandler),a.cancelable&&(a.preventDefault(),a.stopPropagation()),m(null==(b=a.touches[0])?void 0:b.clientX)}},q=!!function(){if("boolean"==typeof bn)return bn;let a=!1;try{let b=()=>{};window.addEventListener("test",b,{get passive(){return a=!0,!1}}),window.removeEventListener("test",b)}catch(b){a=!1}return bn=a}()&&{passive:!1};bo(f)?(null==n||n.addEventListener("touchmove",p.moveHandler,q),null==n||n.addEventListener("touchend",p.upHandler,q)):(null==n||n.addEventListener("mousemove",o.moveHandler,q),null==n||n.addEventListener("mouseup",o.upHandler,q)),b.setColumnSizingInfo(a=>({...a,startOffset:i,startSize:g,deltaOffset:0,deltaPercentage:0,columnSizingStart:h,isResizingColumn:d.id}))}}},createTable:a=>{a.setColumnSizing=b=>null==a.options.onColumnSizingChange?void 0:a.options.onColumnSizingChange(b),a.setColumnSizingInfo=b=>null==a.options.onColumnSizingInfoChange?void 0:a.options.onColumnSizingInfoChange(b),a.resetColumnSizing=b=>{var c;a.setColumnSizing(b?{}:null!=(c=a.initialState.columnSizing)?c:{})},a.resetHeaderSizeInfo=b=>{var c;a.setColumnSizingInfo(b?bm():null!=(c=a.initialState.columnSizingInfo)?c:bm())},a.getTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getLeftTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getLeftHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getCenterTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getCenterHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getRightTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getRightHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0}}}];function bC(a,b){var d,e,f;return a?"function"==typeof(e=d=a)&&(()=>{let a=Object.getPrototypeOf(e);return a.prototype&&a.prototype.isReactComponent})()||"function"==typeof d||"object"==typeof(f=d)&&"symbol"==typeof f.$$typeof&&["react.memo","react.forward_ref"].includes(f.$$typeof.description)?c.createElement(a,b):a:null}var bD=a.i(76504);let bE=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{className:"relative w-full overflow-auto",children:(0,b.jsx)("table",{ref:d,className:(0,h.cn)("w-full caption-bottom text-sm",a),...c})}));bE.displayName="Table";let bF=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("thead",{ref:d,className:(0,h.cn)("[&_tr]:border-b",a),...c}));bF.displayName="TableHeader";let bG=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("tbody",{ref:d,className:(0,h.cn)("[&_tr:last-child]:border-0",a),...c}));bG.displayName="TableBody",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("tfoot",{ref:d,className:(0,h.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...c})).displayName="TableFooter";let bH=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("tr",{ref:d,className:(0,h.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...c}));bH.displayName="TableRow";let bI=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("th",{ref:d,className:(0,h.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...c}));bI.displayName="TableHead";let bJ=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("td",{ref:d,className:(0,h.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...c}));bJ.displayName="TableCell",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("caption",{ref:d,className:(0,h.cn)("mt-4 text-sm text-muted-foreground",a),...c})).displayName="TableCaption";var bK=a.i(15618),bL=a.i(50463);function bM({rows:a=5,columns:c=5}){return(0,b.jsx)("div",{className:"rounded-md border",children:(0,b.jsxs)(bE,{children:[(0,b.jsx)(bF,{children:(0,b.jsx)(bH,{children:Array.from({length:c}).map((a,c)=>(0,b.jsx)(bI,{children:(0,b.jsx)(bL.Skeleton,{className:"h-4 w-20"})},c))})}),(0,b.jsx)(bG,{children:Array.from({length:a}).map((a,d)=>(0,b.jsx)(bH,{children:Array.from({length:c}).map((a,c)=>(0,b.jsx)(bJ,{children:0===c?(0,b.jsx)(bL.Skeleton,{className:"h-4 w-32"}):1===c?(0,b.jsx)(bL.Skeleton,{className:"h-4 w-24"}):2===c?(0,b.jsxs)("div",{className:"flex items-center gap-1",children:[(0,b.jsx)(bL.Skeleton,{className:"h-3 w-3 rounded-full"}),(0,b.jsx)(bL.Skeleton,{className:"h-4 w-16"})]}):3===c?(0,b.jsx)(bL.Skeleton,{className:"h-4 w-12"}):(0,b.jsx)(bL.Skeleton,{className:"h-8 w-8 rounded"})},c))},d))})]})})}let bN=(0,aR.default)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function bO({isRefreshing:a=!1,className:d}){let[e,f]=c.useState(!1);return(0,b.jsx)(bN,{className:(0,h.cn)("h-4 w-4 transition-all duration-300 ease-in-out",a&&"animate-spin",!a&&e&&"rotate-180 scale-110",d),onMouseEnter:()=>!a&&f(!0),onMouseLeave:()=>f(!1),style:{transformOrigin:"center"}})}function bP({columns:a,data:d,loading:e=!1,refreshing:f=!1,onAddTarget:g,onRefresh:h}){let[i,j]=c.useState([]),[k,l]=c.useState([]),[m,n]=c.useState({}),[o,p]=c.useState({}),s=function(a){let b={state:{},onStateChange:()=>{},renderFallbackValue:null,...a},[d]=c.useState(()=>({current:function(a){var b,c;let d=[...bB,...null!=(b=a._features)?b:[]],e={_features:d},f=e._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultOptions?void 0:b.getDefaultOptions(e)),{}),g={...null!=(c=a.initialState)?c:{}};e._features.forEach(a=>{var b;g=null!=(b=null==a.getInitialState?void 0:a.getInitialState(g))?b:g});let h=[],i=!1,j={_features:d,options:{...f,...a},initialState:g,_queue:a=>{h.push(a),i||(i=!0,Promise.resolve().then(()=>{for(;h.length;)h.shift()();i=!1}).catch(a=>setTimeout(()=>{throw a})))},reset:()=>{e.setState(e.initialState)},setOptions:a=>{var b;b=a$(a,e.options),e.options=e.options.mergeOptions?e.options.mergeOptions(f,b):{...f,...b}},getState:()=>e.options.state,setState:a=>{null==e.options.onStateChange||e.options.onStateChange(a)},_getRowId:(a,b,c)=>{var d;return null!=(d=null==e.options.getRowId?void 0:e.options.getRowId(a,b,c))?d:`${c?[c.id,b].join("."):b}`},getCoreRowModel:()=>(e._getCoreRowModel||(e._getCoreRowModel=e.options.getCoreRowModel(e)),e._getCoreRowModel()),getRowModel:()=>e.getPaginationRowModel(),getRow:(a,b)=>{let c=(b?e.getPrePaginationRowModel():e.getRowModel()).rowsById[a];if(!c&&!(c=e.getCoreRowModel().rowsById[a]))throw Error();return c},_getDefaultColumnDef:a1(()=>[e.options.defaultColumn],a=>{var b;return a=null!=(b=a)?b:{},{header:a=>{let b=a.header.column.columnDef;return b.accessorKey?b.accessorKey:b.accessorFn?b.id:null},cell:a=>{var b,c;return null!=(b=null==(c=a.renderValue())||null==c.toString?void 0:c.toString())?b:null},...e._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultColumnDef?void 0:b.getDefaultColumnDef()),{}),...a}},a2(a,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>e.options.columns,getAllColumns:a1(()=>[e._getColumnDefs()],a=>{let b=function(a,c,d){return void 0===d&&(d=0),a.map(a=>{let f=function(a,b,c,d){var e,f;let g,h={...a._getDefaultColumnDef(),...b},i=h.accessorKey,j=null!=(e=null!=(f=h.id)?f:i?"function"==typeof String.prototype.replaceAll?i.replaceAll(".","_"):i.replace(/\./g,"_"):void 0)?e:"string"==typeof h.header?h.header:void 0;if(h.accessorFn?g=h.accessorFn:i&&(g=i.includes(".")?a=>{let b=a;for(let a of i.split(".")){var c;b=null==(c=b)?void 0:c[a]}return b}:a=>a[h.accessorKey]),!j)throw Error();let k={id:`${String(j)}`,accessorFn:g,parent:d,depth:c,columnDef:h,columns:[],getFlatColumns:a1(()=>[!0],()=>{var a;return[k,...null==(a=k.columns)?void 0:a.flatMap(a=>a.getFlatColumns())]},a2(a.options,"debugColumns","column.getFlatColumns")),getLeafColumns:a1(()=>[a._getOrderColumnsFn()],a=>{var b;return null!=(b=k.columns)&&b.length?a(k.columns.flatMap(a=>a.getLeafColumns())):[k]},a2(a.options,"debugColumns","column.getLeafColumns"))};for(let b of a._features)null==b.createColumn||b.createColumn(k,a);return k}(e,a,d,c);return f.columns=a.columns?b(a.columns,f,d+1):[],f})};return b(a)},a2(a,"debugColumns","getAllColumns")),getAllFlatColumns:a1(()=>[e.getAllColumns()],a=>a.flatMap(a=>a.getFlatColumns()),a2(a,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:a1(()=>[e.getAllFlatColumns()],a=>a.reduce((a,b)=>(a[b.id]=b,a),{}),a2(a,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:a1(()=>[e.getAllColumns(),e._getOrderColumnsFn()],(a,b)=>b(a.flatMap(a=>a.getLeafColumns())),a2(a,"debugColumns","getAllLeafColumns")),getColumn:a=>e._getAllFlatColumnsById()[a]};Object.assign(e,j);for(let a=0;a<e._features.length;a++){let b=e._features[a];null==b||null==b.createTable||b.createTable(e)}return e}(b)})),[e,f]=c.useState(()=>d.current.initialState);return d.current.setOptions(b=>({...b,...a,state:{...e,...a.state},onStateChange:b=>{f(b),null==a.onStateChange||a.onStateChange(b)}})),d.current}({data:d,columns:a,onSortingChange:j,onColumnFiltersChange:l,getCoreRowModel:a=>a1(()=>[a.options.data],b=>{let c={rows:[],flatRows:[],rowsById:{}},d=function(b,e,f){void 0===e&&(e=0);let g=[];for(let i=0;i<b.length;i++){let j=a6(a,a._getRowId(b[i],i,f),b[i],i,e,void 0,null==f?void 0:f.id);if(c.flatRows.push(j),c.rowsById[j.id]=j,g.push(j),a.options.getSubRows){var h;j.originalSubRows=a.options.getSubRows(b[i],i),null!=(h=j.originalSubRows)&&h.length&&(j.subRows=d(j.originalSubRows,e+1,j))}}return g};return c.rows=d(b),c},a2(a.options,"debugTable","getRowModel",()=>a._autoResetPageIndex())),getPaginationRowModel:a=>a1(()=>[a.getState().pagination,a.getPrePaginationRowModel(),a.options.paginateExpandedRows?void 0:a.getState().expanded],(b,c)=>{let d;if(!c.rows.length)return c;let{pageSize:e,pageIndex:f}=b,{rows:g,flatRows:h,rowsById:i}=c,j=e*f;g=g.slice(j,j+e),(d=a.options.paginateExpandedRows?{rows:g,flatRows:h,rowsById:i}:function(a){let b=[],c=a=>{var d;b.push(a),null!=(d=a.subRows)&&d.length&&a.getIsExpanded()&&a.subRows.forEach(c)};return a.rows.forEach(c),{rows:b,flatRows:a.flatRows,rowsById:a.rowsById}}({rows:g,flatRows:h,rowsById:i})).flatRows=[];let k=a=>{d.flatRows.push(a),a.subRows.length&&a.subRows.forEach(k)};return d.rows.forEach(k),d},a2(a.options,"debugTable","getPaginationRowModel")),getSortedRowModel:a=>a1(()=>[a.getState().sorting,a.getPreSortedRowModel()],(b,c)=>{if(!c.rows.length||!(null!=b&&b.length))return c;let d=a.getState().sorting,e=[],f=d.filter(b=>{var c;return null==(c=a.getColumn(b.id))?void 0:c.getCanSort()}),g={};f.forEach(b=>{let c=a.getColumn(b.id);c&&(g[b.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});let h=a=>{let b=a.map(a=>({...a}));return b.sort((a,b)=>{for(let d=0;d<f.length;d+=1){var c;let e=f[d],h=g[e.id],i=h.sortUndefined,j=null!=(c=null==e?void 0:e.desc)&&c,k=0;if(i){let c=a.getValue(e.id),d=b.getValue(e.id),f=void 0===c,g=void 0===d;if(f||g){if("first"===i)return f?-1:1;if("last"===i)return f?1:-1;k=f&&g?0:f?i:-i}}if(0===k&&(k=h.sortingFn(a,b,e.id)),0!==k)return j&&(k*=-1),h.invertSorting&&(k*=-1),k}return a.index-b.index}),b.forEach(a=>{var b;e.push(a),null!=(b=a.subRows)&&b.length&&(a.subRows=h(a.subRows))}),b};return{rows:h(c.rows),flatRows:e,rowsById:c.rowsById}},a2(a.options,"debugTable","getSortedRowModel",()=>a._autoResetPageIndex())),getFilteredRowModel:a=>a1(()=>[a.getPreFilteredRowModel(),a.getState().columnFilters,a.getState().globalFilter],(b,c,d)=>{var e,f;let g,h;if(!b.rows.length||!(null!=c&&c.length)&&!d){for(let a=0;a<b.flatRows.length;a++)b.flatRows[a].columnFilters={},b.flatRows[a].columnFiltersMeta={};return b}let i=[],j=[];(null!=c?c:[]).forEach(b=>{var c;let d=a.getColumn(b.id);if(!d)return;let e=d.getFilterFn();e&&i.push({id:b.id,filterFn:e,resolvedValue:null!=(c=null==e.resolveFilterValue?void 0:e.resolveFilterValue(b.value))?c:b.value})});let k=(null!=c?c:[]).map(a=>a.id),l=a.getGlobalFilterFn(),m=a.getAllLeafColumns().filter(a=>a.getCanGlobalFilter());d&&l&&m.length&&(k.push("__global__"),m.forEach(a=>{var b;j.push({id:a.id,filterFn:l,resolvedValue:null!=(b=null==l.resolveFilterValue?void 0:l.resolveFilterValue(d))?b:d})}));for(let a=0;a<b.flatRows.length;a++){let c=b.flatRows[a];if(c.columnFilters={},i.length)for(let a=0;a<i.length;a++){let b=(g=i[a]).id;c.columnFilters[b]=g.filterFn(c,b,g.resolvedValue,a=>{c.columnFiltersMeta[b]=a})}if(j.length){for(let a=0;a<j.length;a++){let b=(h=j[a]).id;if(h.filterFn(c,b,h.resolvedValue,a=>{c.columnFiltersMeta[b]=a})){c.columnFilters.__global__=!0;break}}!0!==c.columnFilters.__global__&&(c.columnFilters.__global__=!1)}}return e=b.rows,f=a=>{for(let b=0;b<k.length;b++)if(!1===a.columnFilters[k[b]])return!1;return!0},a.options.filterFromLeafRows?function(a,b,c){var d;let e=[],f={},g=null!=(d=c.options.maxLeafRowFilterDepth)?d:100,h=function(a,d){void 0===d&&(d=0);let i=[];for(let k=0;k<a.length;k++){var j;let l=a[k],m=a6(c,l.id,l.original,l.index,l.depth,void 0,l.parentId);if(m.columnFilters=l.columnFilters,null!=(j=l.subRows)&&j.length&&d<g){if(m.subRows=h(l.subRows,d+1),b(l=m)&&!m.subRows.length||b(l)||m.subRows.length){i.push(l),f[l.id]=l,e.push(l);continue}}else b(l=m)&&(i.push(l),f[l.id]=l,e.push(l))}return i};return{rows:h(a),flatRows:e,rowsById:f}}(e,f,a):function(a,b,c){var d;let e=[],f={},g=null!=(d=c.options.maxLeafRowFilterDepth)?d:100,h=function(a,d){void 0===d&&(d=0);let i=[];for(let k=0;k<a.length;k++){let l=a[k];if(b(l)){var j;if(null!=(j=l.subRows)&&j.length&&d<g){let a=a6(c,l.id,l.original,l.index,l.depth,void 0,l.parentId);a.subRows=h(l.subRows,d+1),l=a}i.push(l),e.push(l),f[l.id]=l}}return i};return{rows:h(a),flatRows:e,rowsById:f}}(e,f,a)},a2(a.options,"debugTable","getFilteredRowModel",()=>a._autoResetPageIndex())),onColumnVisibilityChange:n,onRowSelectionChange:p,state:{sorting:i,columnFilters:k,columnVisibility:m,rowSelection:o}});return(0,b.jsxs)("div",{className:"w-full",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)(r.Input,{placeholder:"Filter addresses...",value:s.getColumn("address")?.getFilterValue()??"",onChange:a=>s.getColumn("address")?.setFilterValue(a.target.value),className:"max-w-sm"}),(0,b.jsxs)(bD.DropdownMenu,{children:[(0,b.jsx)(bD.DropdownMenuTrigger,{asChild:!0,children:(0,b.jsxs)(q.Button,{variant:"outline",className:"ml-auto",children:["Columns ",(0,b.jsx)(aS,{className:"ml-2 h-4 w-4"})]})}),(0,b.jsx)(bD.DropdownMenuContent,{align:"end",children:s.getAllColumns().filter(a=>a.getCanHide()).map(a=>(0,b.jsx)(bD.DropdownMenuCheckboxItem,{className:"capitalize",checked:a.getIsVisible(),onCheckedChange:b=>a.toggleVisibility(!!b),children:a.id},a.id))})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)(q.Button,{variant:"outline",onClick:h,disabled:e||f,className:"group transition-all duration-200 hover:bg-muted/50",children:[(0,b.jsx)(bO,{isRefreshing:f,className:"mr-2"}),"Refresh"]}),(0,b.jsxs)(q.Button,{onClick:g,children:[(0,b.jsx)(bK.Plus,{className:"mr-2 h-4 w-4"}),"Add Target"]})]})]}),e?(0,b.jsx)(bM,{rows:5,columns:5}):(0,b.jsx)("div",{className:`rounded-md border transition-opacity duration-300 ${f?"opacity-60":"opacity-100"}`,children:(0,b.jsxs)(bE,{children:[(0,b.jsx)(bF,{children:s.getHeaderGroups().map(a=>(0,b.jsx)(bH,{children:a.headers.map(a=>(0,b.jsx)(bI,{children:a.isPlaceholder?null:bC(a.column.columnDef.header,a.getContext())},a.id))},a.id))}),(0,b.jsx)(bG,{children:s.getRowModel().rows?.length?s.getRowModel().rows.map(a=>(0,b.jsx)(bH,{"data-state":a.getIsSelected()&&"selected",className:"transition-all duration-200 hover:bg-muted/50 animate-in fade-in-0 slide-in-from-left-1",children:a.getVisibleCells().map(a=>(0,b.jsx)(bJ,{children:bC(a.column.columnDef.cell,a.getContext())},a.id))},a.id)):(0,b.jsx)(bH,{children:(0,b.jsx)(bJ,{colSpan:a.length,className:"h-24 text-center",children:"No targets found. Add your first target to get started."})})})]})}),(0,b.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,b.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[s.getFilteredSelectedRowModel().rows.length," of"," ",s.getFilteredRowModel().rows.length," row(s) selected."]}),(0,b.jsxs)("div",{className:"space-x-2",children:[(0,b.jsx)(q.Button,{variant:"outline",size:"sm",onClick:()=>s.previousPage(),disabled:!s.getCanPreviousPage(),children:"Previous"}),(0,b.jsx)(q.Button,{variant:"outline",size:"sm",onClick:()=>s.nextPage(),disabled:!s.getCanNextPage(),children:"Next"})]})]})]})}let bQ=(0,a.i(187).cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function bR({className:a,variant:c,...d}){return(0,b.jsx)("div",{className:(0,h.cn)(bQ({variant:c}),a),...d})}let bS=(0,aR.default)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var bT=a.i(16201),bU=a.i(62722),bV=a.i(41710);let bW=(0,aR.default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function bX(){let[a,d]=c.useState([]),[e,f]=c.useState(!1),[h,i]=c.useState(!1),[j,k]=c.useState(!1),[l,m]=c.useState(!1),[n,o]=c.useState(null),[p,u]=c.useState({address:"",description:"",criticality:10}),[v,w]=c.useState({}),x=async(a=!1)=>{try{a?i(!0):f(!0);let b=await (0,aZ.GetTargets)();d(b.targets||[])}catch(a){console.error("Failed to load targets:",a),d([])}finally{a?i(!1):f(!1)}};c.useEffect(()=>{x()},[]);let y=c.useCallback(()=>{x(!0)},[]),z=async()=>{try{let a={};if(p.address.trim()||(a.address="Target address is required"),Object.keys(a).length>0)return void w(a);f(!0),await (0,aZ.AddTarget)({address:p.address.trim(),description:p.description.trim(),criticality:p.criticality}),u({address:"",description:"",criticality:10}),w({}),k(!1),await x()}catch(a){w({submit:`Failed to add target: ${a}`})}finally{f(!1)}},A=async()=>{if(n)try{f(!0),await (0,aZ.DeleteTarget)(n.target_id),m(!1),o(null),await x()}catch(a){console.error("Failed to delete target:",a)}finally{f(!1)}},B=c.useCallback(a=>{o(a),m(!0)},[]),C=c.useMemo(()=>(({onDelete:a})=>[{accessorKey:"address",header:({column:a})=>(0,b.jsxs)(q.Button,{variant:"ghost",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Address",(0,b.jsx)(bS,{className:"ml-2 h-4 w-4"})]}),cell:({row:a})=>{let c=a.getValue("address");return(0,b.jsx)("div",{className:"font-medium",children:c})}},{accessorKey:"description",header:({column:a})=>(0,b.jsxs)(q.Button,{variant:"ghost",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Description",(0,b.jsx)(bS,{className:"ml-2 h-4 w-4"})]}),cell:({row:a})=>{let c=a.getValue("description");return(0,b.jsx)("div",{className:"max-w-[200px] truncate",children:c||"-"})}},{id:"vulnerabilities",header:({column:a})=>(0,b.jsxs)(q.Button,{variant:"ghost",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Vulnerabilities",(0,b.jsx)(bS,{className:"ml-2 h-4 w-4"})]}),cell:({row:a})=>{let c=a.original.severity_counts;if(!c)return(0,b.jsx)("div",{className:"text-muted-foreground",children:"No scan data"});let d=c.critical+c.high+c.medium+c.low+c.info;return 0===d?(0,b.jsxs)(bR,{variant:"outline",className:"flex items-center gap-1 w-fit",children:[(0,b.jsx)(bT.CheckCircle,{className:"h-3 w-3 text-green-500"}),"Clean"]}):(0,b.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,b.jsxs)("div",{className:"flex gap-1 text-xs",children:[c.critical>0&&(0,b.jsxs)(bR,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#f3e8f1",color:"#883678"},children:[c.critical,"C"]}),c.high>0&&(0,b.jsxs)(bR,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdeaea",color:"#d94e4e"},children:[c.high,"H"]}),c.medium>0&&(0,b.jsxs)(bR,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdf0e8",color:"#d65f3c"},children:[c.medium,"M"]}),c.low>0&&(0,b.jsxs)(bR,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#fdf5e8",color:"#bf7534"},children:[c.low,"L"]}),c.info>0&&(0,b.jsxs)(bR,{variant:"outline",className:"px-1 py-0 text-xs border-0",style:{backgroundColor:"#eef0f7",color:"#4c5595"},children:[c.info,"I"]})]}),(0,b.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Total: ",d]})]})},sortingFn:(a,b)=>{let c=a.original,d=b.original,e=c.severity_counts,f=d.severity_counts;if(!e&&!f)return 0;if(!e)return 1;if(!f)return -1;let g=a=>1e3*a.critical+100*a.high+10*a.medium+ +a.low+.1*a.info,h=g(e);return g(f)-h}},{accessorKey:"type",header:"Type",cell:({row:a})=>{let c=a.getValue("type");return(0,b.jsx)(bR,{variant:"outline",children:c||"default"})}},{id:"last_scan_status",header:({column:a})=>(0,b.jsxs)(q.Button,{variant:"ghost",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),className:"h-8 px-2 lg:px-3",children:["Last Scan Status",(0,b.jsx)(bS,{className:"ml-2 h-4 w-4"})]}),cell:({row:a})=>{let c=a.original,d=c.last_scan_date,e=c.last_scan_session_status;if(!d)return(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(bU.XCircle,{className:"h-4 w-4 text-muted-foreground"}),(0,b.jsx)("span",{className:"text-muted-foreground",children:"Never scanned"})]});let f=a=>{try{let b=new Date(a),c=b.toLocaleDateString("en-CA",{year:"numeric",month:"2-digit",day:"2-digit"}),d=b.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1});return{date:c,time:d,full:`${c} ${d}`}}catch{return{date:a,time:"",full:a}}};return(0,b.jsxs)("div",{className:"flex flex-col gap-1",children:[(a=>{switch(a?.toLowerCase()){case"completed":return(0,b.jsxs)(bR,{variant:"outline",className:"flex items-center gap-1 w-fit text-green-700 border-green-200",children:[(0,b.jsx)(bT.CheckCircle,{className:"h-3 w-3"}),"Completed"]});case"running":case"scanning":return(0,b.jsxs)(bR,{variant:"outline",className:"flex items-center gap-1 w-fit text-blue-700 border-blue-200",children:[(0,b.jsx)(bV.Clock,{className:"h-3 w-3"}),"Running"]});case"failed":case"error":return(0,b.jsxs)(bR,{variant:"destructive",className:"flex items-center gap-1 w-fit",children:[(0,b.jsx)(bW,{className:"h-3 w-3"}),"Failed"]});case"aborted":case"cancelled":return(0,b.jsxs)(bR,{variant:"secondary",className:"flex items-center gap-1 w-fit",children:[(0,b.jsx)(bU.XCircle,{className:"h-3 w-3"}),"Aborted"]});default:return(0,b.jsxs)(bR,{variant:"outline",className:"flex items-center gap-1 w-fit",children:[(0,b.jsx)(bW,{className:"h-3 w-3"}),a||"Unknown"]})}})(e),(0,b.jsxs)("div",{className:"text-xs text-muted-foreground",children:[f(d).date," ",f(d).time]})]})},sortingFn:(a,b)=>{let c=a.original,d=b.original,e=c.last_scan_date,f=d.last_scan_date;return e||f?e?f?new Date(f).getTime()-new Date(e).getTime():-1:1:0}},{id:"actions",header:"Actions",cell:({row:c})=>{let d=c.original;return(0,b.jsxs)(bD.DropdownMenu,{children:[(0,b.jsx)(bD.DropdownMenuTrigger,{asChild:!0,children:(0,b.jsxs)(q.Button,{variant:"ghost",className:"h-8 w-8 p-0",size:"icon",children:[(0,b.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,b.jsx)(g.MoreHorizontal,{className:"h-4 w-4"})]})}),(0,b.jsxs)(bD.DropdownMenuContent,{align:"end",children:[(0,b.jsx)(bD.DropdownMenuItem,{onClick:()=>navigator.clipboard.writeText(d.address),children:"Copy address"}),(0,b.jsx)(bD.DropdownMenuSeparator,{}),(0,b.jsx)(bD.DropdownMenuItem,{onClick:()=>a(d),className:"text-red-600 focus:text-red-600",children:"Delete target"})]})]})}}])({onDelete:B}),[B]);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(bP,{columns:C,data:a,loading:e,refreshing:h,onAddTarget:()=>k(!0),onRefresh:y}),(0,b.jsx)(t.Dialog,{open:j,onOpenChange:k,children:(0,b.jsxs)(t.DialogContent,{className:"sm:max-w-[500px]",children:[(0,b.jsxs)(t.DialogHeader,{children:[(0,b.jsx)(t.DialogTitle,{children:"Add New Target"}),(0,b.jsx)(t.DialogDescription,{children:"Add a new target for security scanning. Enter the target URL or IP address."})]}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(s.Label,{htmlFor:"address",children:"Target Address *"}),(0,b.jsx)(r.Input,{id:"address",placeholder:"https://example.com or *************",value:p.address,onChange:a=>{u(b=>({...b,address:a.target.value})),v.address&&w(a=>({...a,address:""}))}}),v.address&&(0,b.jsx)("p",{className:"text-sm text-red-500",children:v.address})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(s.Label,{htmlFor:"description",children:"Description"}),(0,b.jsx)(r.Input,{id:"description",placeholder:"Optional description",value:p.description,onChange:a=>u(b=>({...b,description:a.target.value}))})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(s.Label,{htmlFor:"criticality",children:"Criticality Level"}),(0,b.jsxs)(_,{value:p.criticality.toString(),onValueChange:a=>u(b=>({...b,criticality:parseInt(a)})),children:[(0,b.jsx)(aU,{children:(0,b.jsx)(ad,{})}),(0,b.jsxs)(aX,{children:[(0,b.jsx)(aY,{value:"10",children:"Normal (10)"}),(0,b.jsx)(aY,{value:"20",children:"High (20)"}),(0,b.jsx)(aY,{value:"30",children:"Critical (30)"})]})]})]}),v.submit&&(0,b.jsx)("p",{className:"text-sm text-red-500",children:v.submit})]}),(0,b.jsxs)(t.DialogFooter,{children:[(0,b.jsx)(q.Button,{variant:"outline",onClick:()=>{k(!1),u({address:"",description:"",criticality:10}),w({})},disabled:e,children:"Cancel"}),(0,b.jsx)(q.Button,{onClick:z,disabled:e,children:e?"Adding...":"Add Target"})]})]})}),(0,b.jsx)(t.Dialog,{open:l,onOpenChange:m,children:(0,b.jsxs)(t.DialogContent,{children:[(0,b.jsxs)(t.DialogHeader,{children:[(0,b.jsx)(t.DialogTitle,{children:"Delete Target"}),(0,b.jsxs)(t.DialogDescription,{children:['Are you sure you want to delete the target "',n?.address,'"? This action cannot be undone.']})]}),(0,b.jsxs)(t.DialogFooter,{children:[(0,b.jsx)(q.Button,{variant:"outline",onClick:()=>{m(!1),o(null)},disabled:e,children:"Cancel"}),(0,b.jsx)(q.Button,{variant:"destructive",onClick:A,disabled:e,children:e?"Deleting...":"Delete"})]})]})})]})}function bY(){return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:[(0,b.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 1"})}),(0,b.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 2"})}),(0,b.jsx)("div",{className:"bg-muted/50 aspect-video rounded-xl flex items-center justify-center",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"Dashboard Widget 3"})})]}),(0,b.jsx)("div",{className:"bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min flex items-center justify-center",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Welcome to Acunetix Desktop"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Manage your security scanning infrastructure from this centralized dashboard."})]})})]})}function bZ(){let[a,e]=c.useState("dashboard"),f="targets"===a?{category:"Security",page:"Targets"}:{category:"Overview",page:"Dashboard"};return(0,b.jsxs)(p.SidebarProvider,{children:[(0,b.jsx)(d.AppSidebar,{onNavigate:e}),(0,b.jsxs)(p.SidebarInset,{children:[(0,b.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:(0,b.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,b.jsx)(p.SidebarTrigger,{className:"-ml-1"}),(0,b.jsx)(o.Separator,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),(0,b.jsx)(i,{children:(0,b.jsxs)(j,{children:[(0,b.jsx)(k,{className:"hidden md:block",children:(0,b.jsx)(l,{href:"#",onClick:()=>e("dashboard"),children:f.category})}),(0,b.jsx)(n,{className:"hidden md:block"}),(0,b.jsx)(k,{children:(0,b.jsx)(m,{children:f.page})})]})})]})}),(0,b.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:"targets"===a?(0,b.jsx)(bX,{}):(0,b.jsx)(bY,{})})]})]})}}];

//# sourceMappingURL=app_page_tsx_55b2e5ee._.js.map