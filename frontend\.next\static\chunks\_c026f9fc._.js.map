{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nfunction Collapsible({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\n}\n\nfunction CollapsibleTrigger({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleTrigger\n      data-slot=\"collapsible-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction CollapsibleContent({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleContent\n      data-slot=\"collapsible-content\"\n      {...props}\n    />\n  )\n}\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,6LAAC,kLAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,KAE2C;QAF3C,EAC1B,GAAG,OACkE,GAF3C;IAG1B,qBACE,6LAAC,gMAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,KAE2C;QAF3C,EAC1B,GAAG,OACkE,GAF3C;IAG1B,qBACE,6LAAC,gMAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,yKAAc,CAAsB;IAEpE,0KAAe;iCAAC;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,AAAC,eAAoC,OAAtB,oBAAoB,GAAE;YACnE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,IAAA,qHAAE,EACX,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,6KAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,gLAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,8KAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,+KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,gLAAsB;QACrB,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,gLAAsB;gBACrB,aAAU;gBACV,WAAW,IAAA,qHAAE,EACX,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,8KAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,4MAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,8KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,oLAA0B;QACzB,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,8KAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,iLAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,gLAAuB;kBACtB,cAAA,6LAAC,iLAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,qHAAE,EACX,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,+KAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, VariantProps } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,8KAAmB,CAA6B;AAEvE,SAAS;;IACP,MAAM,UAAU,2KAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,KAYxB;QAZwB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ,GAZwB;;IAavB,MAAM,WAAW,IAAA,wIAAW;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,yKAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,yKAAc,CAAC;IACzC,MAAM,OAAO,qBAAA,sBAAA,WAAY;IACzB,MAAM,UAAU,4KAAiB;gDAC/B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,AAAC,GAAyB,OAAvB,qBAAoB,KAAiC,OAA9B,WAAU,sBAA2C,OAAvB;QAC5E;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,4KAAiB;sDAAC;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,0KAAe;qCAAC;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,wKAAa;iDAChC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,kJAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,IAAA,qHAAE,EACX,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,wIAAW;;;KAbrB;AAkGT,SAAS,QAAQ,KAWhB;QAXgB,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ,GAXgB;;IAYf,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,qHAAE,EACX,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,sIAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,6IAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,4IAAW;wBAAC,WAAU;;0CACrB,6LAAC,2IAAU;0CAAC;;;;;;0CACZ,6LAAC,iJAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,IAAA,qHAAE,EACX,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,IAAA,qHAAE,EACX,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,KAIc;QAJd,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC,GAJd;;IAKtB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,wIAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,IAAA,qHAAE,EAAC,UAAU;QACxB,SAAS,CAAC;YACR,oBAAA,8BAAA,QAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,wOAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,KAAuD;QAAvD,EAAE,SAAS,EAAE,GAAG,OAAuC,GAAvD;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,IAAA,qHAAE,EACX,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAGe;QAHf,EACpB,SAAS,EACT,GAAG,OACgC,GAHf;IAIpB,qBACE,6LAAC,sIAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,KAGe;QAHf,EACxB,SAAS,EACT,GAAG,OACoC,GAHf;IAIxB,qBACE,6LAAC,8IAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,KAI2B;QAJ3B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD,GAJ3B;IAKzB,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,KAI6B;QAJ7B,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD,GAJ7B;IAK1B,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,KAGC;QAHD,EAC3B,SAAS,EACT,GAAG,OACyB,GAHD;IAI3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,IAAA,0KAAG,EACnC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,KAYuB;QAZvB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C,GAZvB;;IAazB,MAAM,OAAO,UAAU,2KAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,IAAA,qHAAE,EAAC,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,0IAAO;;0BACN,6LAAC,iJAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,iJAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,KAQ1B;QAR0B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ,GAR0B;IASzB,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,KAM5B;QAN4B,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ,GAN4B;;IAO3B,kCAAkC;IAClC,MAAM,QAAQ,wKAAa;8CAAC;YAC1B,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAG;QAChD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,4IAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,4IAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EACX,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,KAGC;QAHD,EAC1B,SAAS,EACT,GAAG,OACwB,GAHD;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,KAU7B;QAV6B,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ,GAV6B;IAW5B,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,IAAA,qHAAE,EACX,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/nav-main.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ChevronRight, type LucideIcon } from \"lucide-react\"\n\nimport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n} from \"@/components/ui/collapsible\"\nimport {\n  SidebarGroup,\n  SidebarGroupLabel,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n} from \"@/components/ui/sidebar\"\n\nexport function NavMain({\n  items,\n  onNavigate,\n}: {\n  items: {\n    title: string\n    url: string\n    icon?: LucideIcon\n    isActive?: boolean\n    onClick?: () => void\n    items?: {\n      title: string\n      url: string\n      onClick?: () => void\n    }[]\n  }[]\n  onNavigate?: (view: string) => void\n}) {\n  return (\n    <SidebarGroup>\n      <SidebarGroupLabel>Platform</SidebarGroupLabel>\n      <SidebarMenu>\n        {items.map((item) => (\n          item.items && item.items.length > 0 ? (\n            <Collapsible\n              key={item.title}\n              asChild\n              defaultOpen={item.isActive}\n              className=\"group/collapsible\"\n            >\n              <SidebarMenuItem>\n                <CollapsibleTrigger asChild>\n                  <SidebarMenuButton tooltip={item.title}>\n                    {item.icon && <item.icon />}\n                    <span>{item.title}</span>\n                    <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\n                  </SidebarMenuButton>\n                </CollapsibleTrigger>\n                <CollapsibleContent>\n                  <SidebarMenuSub>\n                    {item.items?.map((subItem) => (\n                      <SidebarMenuSubItem key={subItem.title}>\n                        <SidebarMenuSubButton\n                          onClick={subItem.onClick}\n                          className=\"cursor-pointer\"\n                        >\n                          <span>{subItem.title}</span>\n                        </SidebarMenuSubButton>\n                      </SidebarMenuSubItem>\n                    ))}\n                  </SidebarMenuSub>\n                </CollapsibleContent>\n              </SidebarMenuItem>\n            </Collapsible>\n          ) : (\n            <SidebarMenuItem key={item.title}>\n              <SidebarMenuButton\n                onClick={item.onClick}\n                tooltip={item.title}\n                className=\"cursor-pointer\"\n              >\n                {item.icon && <item.icon />}\n                <span>{item.title}</span>\n              </SidebarMenuButton>\n            </SidebarMenuItem>\n          )\n        ))}\n      </SidebarMenu>\n    </SidebarGroup>\n  )\n}"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAKA;AATA;;;;;AAoBO,SAAS,QAAQ,KAiBvB;QAjBuB,EACtB,KAAK,EACL,UAAU,EAeX,GAjBuB;IAkBtB,qBACE,6LAAC,+IAAY;;0BACX,6LAAC,oJAAiB;0BAAC;;;;;;0BACnB,6LAAC,8IAAW;0BACT,MAAM,GAAG,CAAC,CAAC;wBAkBC;2BAjBX,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,kBAChC,6LAAC,kJAAW;wBAEV,OAAO;wBACP,aAAa,KAAK,QAAQ;wBAC1B,WAAU;kCAEV,cAAA,6LAAC,kJAAe;;8CACd,6LAAC,yJAAkB;oCAAC,OAAO;8CACzB,cAAA,6LAAC,oJAAiB;wCAAC,SAAS,KAAK,KAAK;;4CACnC,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;;;;;0DACxB,6LAAC;0DAAM,KAAK,KAAK;;;;;;0DACjB,6LAAC,yOAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,6LAAC,yJAAkB;8CACjB,cAAA,6LAAC,iJAAc;mDACZ,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,GAAG,CAAC,CAAC,wBAChB,6LAAC,qJAAkB;0DACjB,cAAA,6LAAC,uJAAoB;oDACnB,SAAS,QAAQ,OAAO;oDACxB,WAAU;8DAEV,cAAA,6LAAC;kEAAM,QAAQ,KAAK;;;;;;;;;;;+CALC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;uBAhBzC,KAAK,KAAK;;;;6CA8BjB,6LAAC,kJAAe;kCACd,cAAA,6LAAC,oJAAiB;4BAChB,SAAS,KAAK,OAAO;4BACrB,SAAS,KAAK,KAAK;4BACnB,WAAU;;gCAET,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;;;;;8CACxB,6LAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;uBAPC,KAAK,KAAK;;;;;;;;;;;;;;;;;AAe5C;KAtEgB", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,uLAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,yLAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,0LAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,yLAA4B;kBAC3B,cAAA,6LAAC,0LAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,qHAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,wLAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,uLAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,qHAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+LAAkC;QACjC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,6LAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,4LAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,2NAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,wLAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,qHAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,4LAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,sLAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,6LAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,qHAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,iPAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,6LAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/nav-projects.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  Folder,\n  Forward,\n  More<PERSON><PERSON><PERSON><PERSON>,\n  Trash2,\n  type LucideIcon,\n} from \"lucide-react\"\n\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  SidebarGroup,\n  SidebarGroupLabel,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  useSidebar,\n} from \"@/components/ui/sidebar\"\n\nexport function NavProjects({\n  projects,\n}: {\n  projects: {\n    name: string\n    url: string\n    icon: LucideIcon\n  }[]\n}) {\n  const { isMobile } = useSidebar()\n\n  return (\n    <SidebarGroup className=\"group-data-[collapsible=icon]:hidden\">\n      <SidebarGroupLabel>Projects</SidebarGroupLabel>\n      <SidebarMenu>\n        {projects.map((item) => (\n          <SidebarMenuItem key={item.name}>\n            <SidebarMenuButton asChild>\n              <a href={item.url}>\n                <item.icon />\n                <span>{item.name}</span>\n              </a>\n            </SidebarMenuButton>\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <SidebarMenuAction showOnHover>\n                  <MoreHorizontal />\n                  <span className=\"sr-only\">More</span>\n                </SidebarMenuAction>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent\n                className=\"w-48 rounded-lg\"\n                side={isMobile ? \"bottom\" : \"right\"}\n                align={isMobile ? \"end\" : \"start\"}\n              >\n                <DropdownMenuItem>\n                  <Folder className=\"text-muted-foreground\" />\n                  <span>View Project</span>\n                </DropdownMenuItem>\n                <DropdownMenuItem>\n                  <Forward className=\"text-muted-foreground\" />\n                  <span>Share Project</span>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <Trash2 className=\"text-muted-foreground\" />\n                  <span>Delete Project</span>\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </SidebarMenuItem>\n        ))}\n        <SidebarMenuItem>\n          <SidebarMenuButton className=\"text-sidebar-foreground/70\">\n            <MoreHorizontal className=\"text-sidebar-foreground/70\" />\n            <span>More</span>\n          </SidebarMenuButton>\n        </SidebarMenuItem>\n      </SidebarMenu>\n    </SidebarGroup>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAQA;AAOA;;;AAjBA;;;;AA2BO,SAAS,YAAY,KAQ3B;QAR2B,EAC1B,QAAQ,EAOT,GAR2B;;IAS1B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAA,6IAAU;IAE/B,qBACE,6LAAC,+IAAY;QAAC,WAAU;;0BACtB,6LAAC,oJAAiB;0BAAC;;;;;;0BACnB,6LAAC,8IAAW;;oBACT,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,kJAAe;;8CACd,6LAAC,oJAAiB;oCAAC,OAAO;8CACxB,cAAA,6LAAC;wCAAE,MAAM,KAAK,GAAG;;0DACf,6LAAC,KAAK,IAAI;;;;;0DACV,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;8CAGpB,6LAAC,wJAAY;;sDACX,6LAAC,+JAAmB;4CAAC,OAAO;sDAC1B,cAAA,6LAAC,oJAAiB;gDAAC,WAAW;;kEAC5B,6LAAC,qOAAc;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+JAAmB;4CAClB,WAAU;4CACV,MAAM,WAAW,WAAW;4CAC5B,OAAO,WAAW,QAAQ;;8DAE1B,6LAAC,4JAAgB;;sEACf,6LAAC,mNAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,4JAAgB;;sEACf,6LAAC,sNAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,iKAAqB;;;;;8DACtB,6LAAC,4JAAgB;;sEACf,6LAAC,uNAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;2BA9BQ,KAAK,IAAI;;;;;kCAoCjC,6LAAC,kJAAe;kCACd,cAAA,6LAAC,oJAAiB;4BAAC,WAAU;;8CAC3B,6LAAC,qOAAc;oCAAC,WAAU;;;;;;8CAC1B,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GA7DgB;;QASO,6IAAU;;;KATjB", "debugId": null}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/nav-settings.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Pa<PERSON>,\n  Lock,\n  User,\n} from \"lucide-react\"\n\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  useSidebar,\n} from \"@/components/ui/sidebar\"\n\nexport function NavSettings() {\n  const { isMobile } = useSidebar()\n\n  return (\n    <SidebarMenu>\n      <SidebarMenuItem>\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <SidebarMenuButton\n              size=\"lg\"\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n              tooltip=\"Settings\"\n            >\n              <Settings className=\"size-4\" />\n              <span className=\"ml-2 truncate font-medium data-[collapsed]:hidden\">\n                Settings\n              </span>\n            </SidebarMenuButton>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\n            side={isMobile ? \"bottom\" : \"right\"}\n            align=\"end\"\n            sideOffset={4}\n          >\n            <DropdownMenuGroup>\n              <DropdownMenuItem>\n                <User />\n                Account\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Lock />\n                Security\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Bell />\n                Notifications\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Palette />\n                Appearance\n              </DropdownMenuItem>\n            </DropdownMenuGroup>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <Settings />\n              Settings\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </SidebarMenuItem>\n    </SidebarMenu>\n  )\n}"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AAQA;;;AAlBA;;;;AAyBO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAA,6IAAU;IAE/B,qBACE,6LAAC,8IAAW;kBACV,cAAA,6LAAC,kJAAe;sBACd,cAAA,6LAAC,wJAAY;;kCACX,6LAAC,+JAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,oJAAiB;4BAChB,MAAK;4BACL,WAAU;4BACV,SAAQ;;8CAER,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAoD;;;;;;;;;;;;;;;;;kCAKxE,6LAAC,+JAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,6JAAiB;;kDAChB,6LAAC,4JAAgB;;0DACf,6LAAC,6MAAI;;;;;4CAAG;;;;;;;kDAGV,6LAAC,4JAAgB;;0DACf,6LAAC,6MAAI;;;;;4CAAG;;;;;;;kDAGV,6LAAC,4JAAgB;;0DACf,6LAAC,6MAAI;;;;;4CAAG;;;;;;;kDAGV,6LAAC,4JAAgB;;0DACf,6LAAC,sNAAO;;;;;4CAAG;;;;;;;;;;;;;0CAIf,6LAAC,iKAAqB;;;;;0CACtB,6LAAC,4JAAgB;;kDACf,6LAAC,yNAAQ;;;;;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;GArDgB;;QACO,6IAAU;;;KADjB", "debugId": null}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/wailsjs/go/main/App.js"], "sourcesContent": ["// @ts-check\n// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL\n// This file is automatically generated. DO NOT EDIT\n\nexport function AddBackend(arg1) {\n  return window['go']['main']['App']['AddBackend'](arg1);\n}\n\nexport function AddTarget(arg1) {\n  return window['go']['main']['App']['AddTarget'](arg1);\n}\n\nexport function DeleteBackend(arg1) {\n  return window['go']['main']['App']['DeleteBackend'](arg1);\n}\n\nexport function DeleteTarget(arg1) {\n  return window['go']['main']['App']['DeleteTarget'](arg1);\n}\n\nexport function GetActiveBackend() {\n  return window['go']['main']['App']['GetActiveBackend']();\n}\n\nexport function GetBackends() {\n  return window['go']['main']['App']['GetBackends']();\n}\n\nexport function GetConfig() {\n  return window['go']['main']['App']['GetConfig']();\n}\n\nexport function GetConfigPath() {\n  return window['go']['main']['App']['GetConfigPath']();\n}\n\nexport function GetSettings() {\n  return window['go']['main']['App']['GetSettings']();\n}\n\nexport function GetTargets() {\n  return window['go']['main']['App']['GetTargets']();\n}\n\nexport function Greet(arg1) {\n  return window['go']['main']['App']['Greet'](arg1);\n}\n\nexport function SetActiveBackend(arg1) {\n  return window['go']['main']['App']['SetActiveBackend'](arg1);\n}\n\nexport function TestBackendConnection(arg1) {\n  return window['go']['main']['App']['TestBackendConnection'](arg1);\n}\n\nexport function TestBackendConnectionDirect(arg1) {\n  return window['go']['main']['App']['TestBackendConnectionDirect'](arg1);\n}\n\nexport function UpdateBackend(arg1, arg2) {\n  return window['go']['main']['App']['UpdateBackend'](arg1, arg2);\n}\n\nexport function UpdateSettings(arg1) {\n  return window['go']['main']['App']['UpdateSettings'](arg1);\n}\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,0DAA0D;AAC1D,oDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,SAAS,WAAW,IAAI;IAC7B,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;AACnD;KAFgB;AAIT,SAAS,UAAU,IAAI;IAC5B,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;AAClD;MAFgB;AAIT,SAAS,cAAc,IAAI;IAChC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;AACtD;MAFgB;AAIT,SAAS,aAAa,IAAI;IAC/B,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;AACrD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB;AACxD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc;AACnD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY;AACjD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;AACrD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc;AACnD;MAFgB;AAIT,SAAS;IACd,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa;AAClD;MAFgB;AAIT,SAAS,MAAM,IAAI;IACxB,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC9C;OAFgB;AAIT,SAAS,iBAAiB,IAAI;IACnC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;AACzD;OAFgB;AAIT,SAAS,sBAAsB,IAAI;IACxC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC;AAC9D;OAFgB;AAIT,SAAS,4BAA4B,IAAI;IAC9C,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,8BAA8B,CAAC;AACpE;OAFgB;AAIT,SAAS,cAAc,IAAI,EAAE,IAAI;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM;AAC5D;OAFgB;AAIT,SAAS,eAAe,IAAI;IACjC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;AACvD;OAFgB", "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/backend-switcher.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ChevronsUpDown, Plus, Server, Pencil} from \"lucide-react\"\nimport { GetBackends, GetActiveBackend, SetActiveBackend } from \"@/wailsjs/go/main/App\"\nimport { main } from \"@/wailsjs/go/models\"\n\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  useSidebar,\n} from \"@/components/ui/sidebar\"\n\ninterface BackendSwitcherProps {\n  onAddBackend?: () => void\n  onEditBackend?: (backend: main.Backend) => void\n  refreshTrigger?: number // Add this to trigger refresh from parent\n}\n\nexport function BackendSwitcher({ onAddBackend, onEditBackend, refreshTrigger }: BackendSwitcherProps) {\n  const { isMobile } = useSidebar()\n  const [backends, setBackends] = React.useState<main.Backend[]>([])\n  const [activeBackend, setActiveBackend] = React.useState<main.Backend | null>(null)\n  const [loading, setLoading] = React.useState(true)\n\n  // Load backends on component mount and when refreshTrigger changes\n  React.useEffect(() => {\n    loadBackends()\n  }, [refreshTrigger])\n\n  // Auto-refresh only latency data every 2 seconds\n  React.useEffect(() => {\n    const interval = setInterval(async () => {\n      try {\n        // Only refresh backend data, don't reload everything\n        const backendList = await GetBackends()\n        setBackends(backendList.backends || [])\n\n        // Update active backend if it exists\n        setActiveBackend(current => {\n          if (current) {\n            const updatedActiveBackend = backendList.backends?.find(b => b.id === current.id)\n            return updatedActiveBackend || current\n          }\n          return current\n        })\n      } catch (error) {\n        // Silently fail to avoid disrupting user experience\n        console.debug(\"Failed to refresh latency data:\", error)\n      }\n    }, 2000) // Refresh every 2 seconds\n\n    return () => clearInterval(interval)\n  }, []) // Empty dependency array - using functional updates to avoid dependencies\n\n  const loadBackends = async () => {\n    try {\n      setLoading(true)\n      const backendList = await GetBackends()\n      setBackends(backendList.backends || [])\n\n      // Try to get active backend\n      try {\n        const active = await GetActiveBackend()\n        setActiveBackend(active)\n      } catch {\n        // No active backend set, use first one if available\n        if (backendList.backends && backendList.backends.length > 0) {\n          setActiveBackend(backendList.backends[0])\n        } else {\n          // No backends available, clear active backend\n          setActiveBackend(null)\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to load backends:\", error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleBackendSelect = async (backend: main.Backend) => {\n    try {\n      await SetActiveBackend(backend.id)\n      setActiveBackend(backend)\n    } catch (error) {\n      console.error(\"Failed to set active backend:\", error)\n    }\n  }\n\n  const getStatusDisplay = (backend: main.Backend) => {\n    if (backend.status === \"connected\" && backend.latency >= 0) {\n      // Show latency with color coding\n      if (backend.latency < 100) {\n        return { text: `${backend.latency}ms`, color: \"text-green-600\" }\n      } else if (backend.latency < 500) {\n        return { text: `${backend.latency}ms`, color: \"text-yellow-600\" }\n      } else {\n        return { text: `${backend.latency}ms`, color: \"text-red-600\" }\n      }\n    } else if (backend.status === \"error\") {\n      return { text: \"Error\", color: \"text-red-600\" }\n    } else {\n      return { text: \"Testing...\", color: \"text-gray-500\" }\n    }\n  }\n\n  if (loading) {\n    return (\n      <SidebarMenu>\n        <SidebarMenuItem>\n          <SidebarMenuButton size=\"lg\" disabled>\n            <div className=\"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\">\n              <Server className=\"size-4\" />\n            </div>\n            <div className=\"grid flex-1 text-left text-sm leading-tight\">\n              <span className=\"truncate font-medium\">Loading...</span>\n              <span className=\"truncate text-xs\">Please wait</span>\n            </div>\n          </SidebarMenuButton>\n        </SidebarMenuItem>\n      </SidebarMenu>\n    )\n  }\n\n  if (!activeBackend && backends.length === 0) {\n    return (\n      <SidebarMenu>\n        <SidebarMenuItem>\n          <SidebarMenuButton\n            size=\"lg\"\n            onClick={onAddBackend}\n            className=\"cursor-pointer\"\n          >\n            <div className=\"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\">\n              <Plus className=\"size-4\" />\n            </div>\n            <div className=\"grid flex-1 text-left text-sm leading-tight\">\n              <span className=\"truncate font-medium\">Add Backend</span>\n              <span className=\"truncate text-xs\">No backends configured</span>\n            </div>\n          </SidebarMenuButton>\n        </SidebarMenuItem>\n      </SidebarMenu>\n    )\n  }\n\n  if (!activeBackend) {\n    return null\n  }\n\n  return (\n    <SidebarMenu>\n      <SidebarMenuItem>\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <SidebarMenuButton\n              size=\"lg\"\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n            >\n              <div className=\"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\">\n                <Server className=\"size-4\" />\n              </div>\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                <span className=\"truncate font-medium\">{activeBackend.name}</span>\n                <span className={`truncate text-xs ${getStatusDisplay(activeBackend).color}`}>\n                  {getStatusDisplay(activeBackend).text}\n                </span>\n              </div>\n              <ChevronsUpDown className=\"ml-auto\" />\n            </SidebarMenuButton>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\n            align=\"start\"\n            side={isMobile ? \"bottom\" : \"right\"}\n            sideOffset={4}\n          >\n            <DropdownMenuLabel className=\"text-muted-foreground text-xs\">\n              Backends\n            </DropdownMenuLabel>\n            {backends.map((backend, index) => (\n              <DropdownMenuItem\n                key={backend.id}\n                onClick={() => handleBackendSelect(backend)}\n                className=\"gap-2 p-2\"\n              >\n                <div className=\"flex size-6 items-center justify-center rounded-md border\">\n                  <Server className=\"size-3.5 shrink-0\" />\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"font-medium\">{backend.name}</div>\n                  <div className={`text-xs ${getStatusDisplay(backend).color}`}>\n                    {getStatusDisplay(backend).text}\n                  </div>\n                </div>\n                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>\n              </DropdownMenuItem>\n            ))}\n            <DropdownMenuSeparator />\n            {activeBackend && (\n              <DropdownMenuItem\n                className=\"gap-2 p-2\"\n                onClick={() => onEditBackend?.(activeBackend)}\n              >\n                <div className=\"flex size-6 items-center justify-center rounded-md border bg-transparent\">\n                  <Pencil className=\"size-4\" />\n                </div>\n                <div className=\"text-muted-foreground font-medium\">Edit Current Backend</div>\n              </DropdownMenuItem>\n            )}\n            <DropdownMenuItem\n              className=\"gap-2 p-2\"\n              onClick={onAddBackend}\n            >\n              <div className=\"flex size-6 items-center justify-center rounded-md border bg-transparent\">\n                <Plus className=\"size-4\" />\n              </div>\n              <div className=\"text-muted-foreground font-medium\">Add Backend</div>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </SidebarMenuItem>\n    </SidebarMenu>\n  )\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AASA;;;AAhBA;;;;;;AA6BO,SAAS,gBAAgB,KAAqE;QAArE,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAwB,GAArE;;IAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAA,6IAAU;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,yKAAc,CAAiB,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,yKAAc,CAAsB;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,yKAAc,CAAC;IAE7C,mEAAmE;IACnE,0KAAe;qCAAC;YACd;QACF;oCAAG;QAAC;KAAe;IAEnB,iDAAiD;IACjD,0KAAe;qCAAC;YACd,MAAM,WAAW;sDAAY;oBAC3B,IAAI;wBACF,qDAAqD;wBACrD,MAAM,cAAc,MAAM,IAAA,8IAAW;wBACrC,YAAY,YAAY,QAAQ,IAAI,EAAE;wBAEtC,qCAAqC;wBACrC;kEAAiB,CAAA;gCACf,IAAI,SAAS;wCACkB;oCAA7B,MAAM,wBAAuB,wBAAA,YAAY,QAAQ,cAApB,4CAAA,sBAAsB,IAAI;8EAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;;oCAChF,OAAO,wBAAwB;gCACjC;gCACA,OAAO;4BACT;;oBACF,EAAE,OAAO,OAAO;wBACd,oDAAoD;wBACpD,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;qDAAG,MAAM,0BAA0B;;YAEnC;6CAAO,IAAM,cAAc;;QAC7B;oCAAG,EAAE,GAAE,0EAA0E;IAEjF,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,cAAc,MAAM,IAAA,8IAAW;YACrC,YAAY,YAAY,QAAQ,IAAI,EAAE;YAEtC,4BAA4B;YAC5B,IAAI;gBACF,MAAM,SAAS,MAAM,IAAA,mJAAgB;gBACrC,iBAAiB;YACnB,EAAE,UAAM;gBACN,oDAAoD;gBACpD,IAAI,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;oBAC3D,iBAAiB,YAAY,QAAQ,CAAC,EAAE;gBAC1C,OAAO;oBACL,8CAA8C;oBAC9C,iBAAiB;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,IAAA,mJAAgB,EAAC,QAAQ,EAAE;YACjC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,OAAO,IAAI,GAAG;YAC1D,iCAAiC;YACjC,IAAI,QAAQ,OAAO,GAAG,KAAK;gBACzB,OAAO;oBAAE,MAAM,AAAC,GAAkB,OAAhB,QAAQ,OAAO,EAAC;oBAAK,OAAO;gBAAiB;YACjE,OAAO,IAAI,QAAQ,OAAO,GAAG,KAAK;gBAChC,OAAO;oBAAE,MAAM,AAAC,GAAkB,OAAhB,QAAQ,OAAO,EAAC;oBAAK,OAAO;gBAAkB;YAClE,OAAO;gBACL,OAAO;oBAAE,MAAM,AAAC,GAAkB,OAAhB,QAAQ,OAAO,EAAC;oBAAK,OAAO;gBAAe;YAC/D;QACF,OAAO,IAAI,QAAQ,MAAM,KAAK,SAAS;YACrC,OAAO;gBAAE,MAAM;gBAAS,OAAO;YAAe;QAChD,OAAO;YACL,OAAO;gBAAE,MAAM;gBAAc,OAAO;YAAgB;QACtD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,8IAAW;sBACV,cAAA,6LAAC,kJAAe;0BACd,cAAA,6LAAC,oJAAiB;oBAAC,MAAK;oBAAK,QAAQ;;sCACnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mNAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAuB;;;;;;8CACvC,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM/C;IAEA,IAAI,CAAC,iBAAiB,SAAS,MAAM,KAAK,GAAG;QAC3C,qBACE,6LAAC,8IAAW;sBACV,cAAA,6LAAC,kJAAe;0BACd,cAAA,6LAAC,oJAAiB;oBAChB,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAuB;;;;;;8CACvC,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM/C;IAEA,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,6LAAC,8IAAW;kBACV,cAAA,6LAAC,kJAAe;sBACd,cAAA,6LAAC,wJAAY;;kCACX,6LAAC,+JAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,oJAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwB,cAAc,IAAI;;;;;;sDAC1D,6LAAC;4CAAK,WAAW,AAAC,oBAAyD,OAAtC,iBAAiB,eAAe,KAAK;sDACvE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;8CAGzC,6LAAC,mPAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+JAAmB;wBAClB,WAAU;wBACV,OAAM;wBACN,MAAM,WAAW,WAAW;wBAC5B,YAAY;;0CAEZ,6LAAC,6JAAiB;gCAAC,WAAU;0CAAgC;;;;;;4BAG5D,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,4JAAgB;oCAEf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mNAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe,QAAQ,IAAI;;;;;;8DAC1C,6LAAC;oDAAI,WAAW,AAAC,WAA0C,OAAhC,iBAAiB,SAAS,KAAK;8DACvD,iBAAiB,SAAS,IAAI;;;;;;;;;;;;sDAGnC,6LAAC,gKAAoB;;gDAAC;gDAAE,QAAQ;;;;;;;;mCAb3B,QAAQ,EAAE;;;;;0CAgBnB,6LAAC,iKAAqB;;;;;4BACrB,+BACC,6LAAC,4JAAgB;gCACf,WAAU;gCACV,SAAS,IAAM,0BAAA,oCAAA,cAAgB;;kDAE/B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;;;;;;;0CAGvD,6LAAC,4JAAgB;gCACf,WAAU;gCACV,SAAS;;kDAET,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE;GA9MgB;;QACO,6IAAU;;;KADjB", "debugId": null}}, {"offset": {"line": 2949, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,gLAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,+KAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,gLAAuB;gBACtB,aAAU;gBACV,WAAW,IAAA,qHAAE,EACX,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,8KAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,4MAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,oLAA2B;QAC1B,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3165, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,4KAAmB;QAClB,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3199, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/wailsjs/go/models.ts"], "sourcesContent": ["export namespace main {\n\t\n\texport class Backend {\n\t    id: string;\n\t    name: string;\n\t    url: string;\n\t    api_key: string;\n\t    is_active: boolean;\n\t    // Go type: time\n\t    created_at: any;\n\t    // Go type: time\n\t    updated_at: any;\n\t    // Go type: time\n\t    last_tested: any;\n\t    status: string;\n\t    latency: number;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new Backend(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.id = source[\"id\"];\n\t        this.name = source[\"name\"];\n\t        this.url = source[\"url\"];\n\t        this.api_key = source[\"api_key\"];\n\t        this.is_active = source[\"is_active\"];\n\t        this.created_at = this.convertValues(source[\"created_at\"], null);\n\t        this.updated_at = this.convertValues(source[\"updated_at\"], null);\n\t        this.last_tested = this.convertValues(source[\"last_tested\"], null);\n\t        this.status = source[\"status\"];\n\t        this.latency = source[\"latency\"];\n\t    }\n\t\n\t\tconvertValues(a: any, classs: any, asMap: boolean = false): any {\n\t\t    if (!a) {\n\t\t        return a;\n\t\t    }\n\t\t    if (a.slice && a.map) {\n\t\t        return (a as any[]).map(elem => this.convertValues(elem, classs));\n\t\t    } else if (\"object\" === typeof a) {\n\t\t        if (asMap) {\n\t\t            for (const key of Object.keys(a)) {\n\t\t                a[key] = new classs(a[key]);\n\t\t            }\n\t\t            return a;\n\t\t        }\n\t\t        return new classs(a);\n\t\t    }\n\t\t    return a;\n\t\t}\n\t}\n\texport class BackendConnectionTest {\n\t    success: boolean;\n\t    message: string;\n\t    status: number;\n\t    latency: number;\n\t    version?: string;\n\t    // Go type: time\n\t    tested_at: any;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new BackendConnectionTest(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.success = source[\"success\"];\n\t        this.message = source[\"message\"];\n\t        this.status = source[\"status\"];\n\t        this.latency = source[\"latency\"];\n\t        this.version = source[\"version\"];\n\t        this.tested_at = this.convertValues(source[\"tested_at\"], null);\n\t    }\n\t\n\t\tconvertValues(a: any, classs: any, asMap: boolean = false): any {\n\t\t    if (!a) {\n\t\t        return a;\n\t\t    }\n\t\t    if (a.slice && a.map) {\n\t\t        return (a as any[]).map(elem => this.convertValues(elem, classs));\n\t\t    } else if (\"object\" === typeof a) {\n\t\t        if (asMap) {\n\t\t            for (const key of Object.keys(a)) {\n\t\t                a[key] = new classs(a[key]);\n\t\t            }\n\t\t            return a;\n\t\t        }\n\t\t        return new classs(a);\n\t\t    }\n\t\t    return a;\n\t\t}\n\t}\n\texport class BackendList {\n\t    backends: Backend[];\n\t    count: number;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new BackendList(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.backends = this.convertValues(source[\"backends\"], Backend);\n\t        this.count = source[\"count\"];\n\t    }\n\t\n\t\tconvertValues(a: any, classs: any, asMap: boolean = false): any {\n\t\t    if (!a) {\n\t\t        return a;\n\t\t    }\n\t\t    if (a.slice && a.map) {\n\t\t        return (a as any[]).map(elem => this.convertValues(elem, classs));\n\t\t    } else if (\"object\" === typeof a) {\n\t\t        if (asMap) {\n\t\t            for (const key of Object.keys(a)) {\n\t\t                a[key] = new classs(a[key]);\n\t\t            }\n\t\t            return a;\n\t\t        }\n\t\t        return new classs(a);\n\t\t    }\n\t\t    return a;\n\t\t}\n\t}\n\texport class Settings {\n\t    auto_connect: boolean;\n\t    check_interval: number;\n\t    theme: string;\n\t    language: string;\n\t    log_level: string;\n\t    max_retries: number;\n\t    connection_timeout: number;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new Settings(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.auto_connect = source[\"auto_connect\"];\n\t        this.check_interval = source[\"check_interval\"];\n\t        this.theme = source[\"theme\"];\n\t        this.language = source[\"language\"];\n\t        this.log_level = source[\"log_level\"];\n\t        this.max_retries = source[\"max_retries\"];\n\t        this.connection_timeout = source[\"connection_timeout\"];\n\t    }\n\t}\n\texport class Config {\n\t    version: string;\n\t    backends: Backend[];\n\t    settings: Settings;\n\t    // Go type: time\n\t    last_save: any;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new Config(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.version = source[\"version\"];\n\t        this.backends = this.convertValues(source[\"backends\"], Backend);\n\t        this.settings = this.convertValues(source[\"settings\"], Settings);\n\t        this.last_save = this.convertValues(source[\"last_save\"], null);\n\t    }\n\t\n\t\tconvertValues(a: any, classs: any, asMap: boolean = false): any {\n\t\t    if (!a) {\n\t\t        return a;\n\t\t    }\n\t\t    if (a.slice && a.map) {\n\t\t        return (a as any[]).map(elem => this.convertValues(elem, classs));\n\t\t    } else if (\"object\" === typeof a) {\n\t\t        if (asMap) {\n\t\t            for (const key of Object.keys(a)) {\n\t\t                a[key] = new classs(a[key]);\n\t\t            }\n\t\t            return a;\n\t\t        }\n\t\t        return new classs(a);\n\t\t    }\n\t\t    return a;\n\t\t}\n\t}\n\texport class NewBackendRequest {\n\t    name: string;\n\t    url: string;\n\t    api_key: string;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new NewBackendRequest(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.name = source[\"name\"];\n\t        this.url = source[\"url\"];\n\t        this.api_key = source[\"api_key\"];\n\t    }\n\t}\n\texport class NewTargetRequest {\n\t    address: string;\n\t    description: string;\n\t    criticality: number;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new NewTargetRequest(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.address = source[\"address\"];\n\t        this.description = source[\"description\"];\n\t        this.criticality = source[\"criticality\"];\n\t    }\n\t}\n\t\n\texport class Target {\n\t    target_id: string;\n\t    address: string;\n\t    description: string;\n\t    criticality: number;\n\t    type: string;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new Target(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.target_id = source[\"target_id\"];\n\t        this.address = source[\"address\"];\n\t        this.description = source[\"description\"];\n\t        this.criticality = source[\"criticality\"];\n\t        this.type = source[\"type\"];\n\t    }\n\t}\n\texport class TargetList {\n\t    targets: Target[];\n\t    count: number;\n\t\n\t    static createFrom(source: any = {}) {\n\t        return new TargetList(source);\n\t    }\n\t\n\t    constructor(source: any = {}) {\n\t        if ('string' === typeof source) source = JSON.parse(source);\n\t        this.targets = this.convertValues(source[\"targets\"], Target);\n\t        this.count = source[\"count\"];\n\t    }\n\t\n\t\tconvertValues(a: any, classs: any, asMap: boolean = false): any {\n\t\t    if (!a) {\n\t\t        return a;\n\t\t    }\n\t\t    if (a.slice && a.map) {\n\t\t        return (a as any[]).map(elem => this.convertValues(elem, classs));\n\t\t    } else if (\"object\" === typeof a) {\n\t\t        if (asMap) {\n\t\t            for (const key of Object.keys(a)) {\n\t\t                a[key] = new classs(a[key]);\n\t\t            }\n\t\t            return a;\n\t\t        }\n\t\t        return new classs(a);\n\t\t    }\n\t\t    return a;\n\t\t}\n\t}\n\n}\n\n"], "names": [], "mappings": ";;;;;;UAAiB;IAET,MAAM;QAeT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,QAAQ;QACvB;QAgBH,cAAc,CAAM,EAAE,MAAW,EAA+B;gBAA7B,QAAA,iEAAiB;YAChD,IAAI,CAAC,GAAG;gBACJ,OAAO;YACX;YACA,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,AAAC,EAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7D,OAAO,IAAI,aAAa,OAAO,GAAG;gBAC9B,IAAI,OAAO;oBACP,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,GAAI;wBAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI;oBAC9B;oBACA,OAAO;gBACX;gBACA,OAAO,IAAI,OAAO;YACtB;YACA,OAAO;QACX;QA9BG,YAAY,SAAc,CAAC,CAAC,CAAE;YAlB9B,+KAAA,MAAA,KAAA;YACA,+KAAA,QAAA,KAAA;YACA,+KAAA,OAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YACA,+KAAA,aAAA,KAAA;YACA,gBAAgB;YAChB,+KAAA,cAAA,KAAA;YACA,gBAAgB;YAChB,+KAAA,cAAA,KAAA;YACA,gBAAgB;YAChB,+KAAA,eAAA,KAAA;YACA,+KAAA,UAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK;YACtB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO;YAC1B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM;YACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY;YACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE;YAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE;YAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE;YAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS;YAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;QACpC;IAmBJ;SAlDa,UAAA;IAmDN,MAAM;QAST,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,sBAAsB;QACrC;QAYH,cAAc,CAAM,EAAE,MAAW,EAA+B;gBAA7B,QAAA,iEAAiB;YAChD,IAAI,CAAC,GAAG;gBACJ,OAAO;YACX;YACA,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,AAAC,EAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7D,OAAO,IAAI,aAAa,OAAO,GAAG;gBAC9B,IAAI,OAAO;oBACP,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,GAAI;wBAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI;oBAC9B;oBACA,OAAO;gBACX;gBACA,OAAO,IAAI,OAAO;YACtB;YACA,OAAO;QACX;QA1BG,YAAY,SAAc,CAAC,CAAC,CAAE;YAZ9B,+KAAA,WAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YACA,+KAAA,UAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YACA,gBAAgB;YAChB,+KAAA,aAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS;YAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,EAAE;QAC7D;IAmBJ;SAxCa,wBAAA;IAyCN,MAAM;QAIT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,YAAY;QAC3B;QAQH,cAAc,CAAM,EAAE,MAAW,EAA+B;gBAA7B,QAAA,iEAAiB;YAChD,IAAI,CAAC,GAAG;gBACJ,OAAO;YACX;YACA,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,AAAC,EAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7D,OAAO,IAAI,aAAa,OAAO,GAAG;gBAC9B,IAAI,OAAO;oBACP,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,GAAI;wBAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI;oBAC9B;oBACA,OAAO;gBACX;gBACA,OAAO,IAAI,OAAO;YACtB;YACA,OAAO;QACX;QAtBG,YAAY,SAAc,CAAC,CAAC,CAAE;YAP9B,+KAAA,YAAA,KAAA;YACA,+KAAA,SAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE;YACvD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;QAChC;IAmBJ;SA/Ba,cAAA;IAgCN,MAAM;QAST,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,SAAS;QACxB;QAEA,YAAY,SAAc,CAAC,CAAC,CAAE;YAZ9B,+KAAA,gBAAA,KAAA;YACA,+KAAA,kBAAA,KAAA;YACA,+KAAA,SAAA,KAAA;YACA,+KAAA,YAAA,KAAA;YACA,+KAAA,aAAA,KAAA;YACA,+KAAA,eAAA,KAAA;YACA,+KAAA,sBAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,eAAe;YAC1C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,iBAAiB;YAC9C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;YAC5B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,WAAW;YAClC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY;YACpC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc;YACxC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,qBAAqB;QAC1D;IACJ;SAvBa,WAAA;IAwBN,MAAM;QAOT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,OAAO;QACtB;QAUH,cAAc,CAAM,EAAE,MAAW,EAA+B;gBAA7B,QAAA,iEAAiB;YAChD,IAAI,CAAC,GAAG;gBACJ,OAAO;YACX;YACA,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,AAAC,EAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7D,OAAO,IAAI,aAAa,OAAO,GAAG;gBAC9B,IAAI,OAAO;oBACP,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,GAAI;wBAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI;oBAC9B;oBACA,OAAO;gBACX;gBACA,OAAO,IAAI,OAAO;YACtB;YACA,OAAO;QACX;QAxBG,YAAY,SAAc,CAAC,CAAC,CAAE;YAV9B,+KAAA,WAAA,KAAA;YACA,+KAAA,YAAA,KAAA;YACA,+KAAA,YAAA,KAAA;YACA,gBAAgB;YAChB,+KAAA,aAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE;YACvD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE;YACvD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,EAAE;QAC7D;IAmBJ;SApCa,SAAA;IAqCN,MAAM;QAKT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,kBAAkB;QACjC;QAEA,YAAY,SAAc,CAAC,CAAC,CAAE;YAR9B,+KAAA,QAAA,KAAA;YACA,+KAAA,OAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO;YAC1B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM;YACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;QACpC;IACJ;SAfa,oBAAA;IAgBN,MAAM;QAKT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,iBAAiB;QAChC;QAEA,YAAY,SAAc,CAAC,CAAC,CAAE;YAR9B,+KAAA,WAAA,KAAA;YACA,+KAAA,eAAA,KAAA;YACA,+KAAA,eAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc;YACxC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc;QAC5C;IACJ;SAfa,mBAAA;IAiBN,MAAM;QAOT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,OAAO;QACtB;QAEA,YAAY,SAAc,CAAC,CAAC,CAAE;YAV9B,+KAAA,aAAA,KAAA;YACA,+KAAA,WAAA,KAAA;YACA,+KAAA,eAAA,KAAA;YACA,+KAAA,eAAA,KAAA;YACA,+KAAA,QAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY;YACpC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU;YAChC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc;YACxC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc;YACxC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO;QAC9B;IACJ;SAnBa,SAAA;IAoBN,MAAM;QAIT,OAAO,aAA6B;gBAAlB,SAAA,iEAAc,CAAC;YAC7B,OAAO,IAAI,WAAW;QAC1B;QAQH,cAAc,CAAM,EAAE,MAAW,EAA+B;gBAA7B,QAAA,iEAAiB;YAChD,IAAI,CAAC,GAAG;gBACJ,OAAO;YACX;YACA,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,AAAC,EAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7D,OAAO,IAAI,aAAa,OAAO,GAAG;gBAC9B,IAAI,OAAO;oBACP,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,GAAI;wBAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI;oBAC9B;oBACA,OAAO;gBACX;gBACA,OAAO,IAAI,OAAO;YACtB;YACA,OAAO;QACX;QAtBG,YAAY,SAAc,CAAC,CAAC,CAAE;YAP9B,+KAAA,WAAA,KAAA;YACA,+KAAA,SAAA,KAAA;YAOI,IAAI,aAAa,OAAO,QAAQ,SAAS,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;QAChC;IAmBJ;SA/Ba,aAAA;AAiCd,GAjRiB,SAAA", "debugId": null}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/connection-test-result.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Label } from \"@/components/ui/label\"\nimport { Loader2, CheckCircle, XCircle, Clock } from \"lucide-react\"\nimport { main } from \"@/wailsjs/go/models\"\n\ninterface ConnectionTestResultProps {\n  formData: {\n    name: string\n    url: string\n    api_key: string\n  }\n  disabled?: boolean\n}\n\nexport function ConnectionTestResult({ formData, disabled = false }: ConnectionTestResultProps) {\n  const [isTesting, setIsTesting] = React.useState(false)\n  const [testResult, setTestResult] = React.useState<main.BackendConnectionTest | null>(null)\n  const [errors, setErrors] = React.useState<Record<string, string>>({})\n\n  const handleTestConnection = async () => {\n    if (!formData.url || !formData.api_key) {\n      setErrors({\n        url: !formData.url ? \"URL is required for testing\" : \"\",\n        api_key: !formData.api_key ? \"API Key is required for testing\" : \"\",\n      })\n      return\n    }\n\n    setIsTesting(true)\n    setTestResult(null)\n    setErrors({})\n\n    try {\n      const { TestBackendConnectionDirect } = await import(\"@/wailsjs/go/main/App\")\n      const result = await TestBackendConnectionDirect({\n        name: formData.name || \"Test\",\n        url: formData.url,\n        api_key: formData.api_key,\n      })\n      setTestResult(result)\n    } catch (error) {\n      const errorResult = new main.BackendConnectionTest({\n        success: false,\n        message: `Test failed: ${error}`,\n        status: 0,\n        latency: 0,\n        tested_at: new Date(),\n      })\n      setTestResult(errorResult)\n    } finally {\n      setIsTesting(false)\n    }\n  }\n\n  // Clear test result when form data changes\n  React.useEffect(() => {\n    setTestResult(null)\n    setErrors({})\n  }, [formData.url, formData.api_key])\n\n  return (\n    <div className=\"space-y-2\">\n      <Label>Connection Test</Label>\n      <Button\n        type=\"button\"\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={handleTestConnection}\n        disabled={disabled || isTesting || !formData.url || !formData.api_key}\n      >\n        {isTesting ? (\n          <>\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n            Testing...\n          </>\n        ) : (\n          \"Test Connection\"\n        )}\n      </Button>\n\n      {/* Test Result Display */}\n      {testResult && (\n        <div className={`p-3 rounded-md border ${\n          testResult.success \n            ? \"bg-green-50 border-green-200 text-green-800\" \n            : \"bg-red-50 border-red-200 text-red-800\"\n        }`}>\n          <div className=\"flex items-center gap-2\">\n            {testResult.success ? (\n              <CheckCircle className=\"h-4 w-4\" />\n            ) : (\n              <XCircle className=\"h-4 w-4\" />\n            )}\n            <span className=\"font-medium\">\n              {testResult.success ? \"Connection Successful\" : \"Connection Failed\"}\n            </span>\n          </div>\n          \n          <p className=\"text-sm mt-1\">{testResult.message}</p>\n          \n          {testResult.success && (\n            <div className=\"flex items-center gap-4 mt-2 text-xs\">\n              <div className=\"flex items-center gap-1\">\n                <Clock className=\"h-3 w-3\" />\n                <span>Latency: {testResult.latency}ms</span>\n              </div>\n              <div>Status: {testResult.status}</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Error Display */}\n      {(errors.url || errors.api_key) && (\n        <div className=\"text-sm text-red-500\">\n          {errors.url && <p>{errors.url}</p>}\n          {errors.api_key && <p>{errors.api_key}</p>}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAWO,SAAS,qBAAqB,KAAyD;QAAzD,EAAE,QAAQ,EAAE,WAAW,KAAK,EAA6B,GAAzD;;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,yKAAc,CAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,yKAAc,CAAoC;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,yKAAc,CAAyB,CAAC;IAEpE,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,OAAO,EAAE;YACtC,UAAU;gBACR,KAAK,CAAC,SAAS,GAAG,GAAG,gCAAgC;gBACrD,SAAS,CAAC,SAAS,OAAO,GAAG,oCAAoC;YACnE;YACA;QACF;QAEA,aAAa;QACb,cAAc;QACd,UAAU,CAAC;QAEX,IAAI;YACF,MAAM,EAAE,2BAA2B,EAAE,GAAG;YACxC,MAAM,SAAS,MAAM,4BAA4B;gBAC/C,MAAM,SAAS,IAAI,IAAI;gBACvB,KAAK,SAAS,GAAG;gBACjB,SAAS,SAAS,OAAO;YAC3B;YACA,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,cAAc,IAAI,kIAAI,CAAC,qBAAqB,CAAC;gBACjD,SAAS;gBACT,SAAS,AAAC,gBAAqB,OAAN;gBACzB,QAAQ;gBACR,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA,2CAA2C;IAC3C,0KAAe;0CAAC;YACd,cAAc;YACd,UAAU,CAAC;QACb;yCAAG;QAAC,SAAS,GAAG;QAAE,SAAS,OAAO;KAAC;IAEnC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAK;0BAAC;;;;;;0BACP,6LAAC,wIAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,UAAU,YAAY,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,OAAO;0BAEpE,0BACC;;sCACE,6LAAC,+NAAO;4BAAC,WAAU;;;;;;wBAA8B;;mCAInD;;;;;;YAKH,4BACC,6LAAC;gBAAI,WAAW,AAAC,yBAIhB,OAHC,WAAW,OAAO,GACd,gDACA;;kCAEJ,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,OAAO,iBACjB,6LAAC,6OAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,0NAAO;gCAAC,WAAU;;;;;;0CAErB,6LAAC;gCAAK,WAAU;0CACb,WAAW,OAAO,GAAG,0BAA0B;;;;;;;;;;;;kCAIpD,6LAAC;wBAAE,WAAU;kCAAgB,WAAW,OAAO;;;;;;oBAE9C,WAAW,OAAO,kBACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gNAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAK;4CAAU,WAAW,OAAO;4CAAC;;;;;;;;;;;;;0CAErC,6LAAC;;oCAAI;oCAAS,WAAW,MAAM;;;;;;;;;;;;;;;;;;;YAOtC,CAAC,OAAO,GAAG,IAAI,OAAO,OAAO,mBAC5B,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,kBAAI,6LAAC;kCAAG,OAAO,GAAG;;;;;;oBAC5B,OAAO,OAAO,kBAAI,6LAAC;kCAAG,OAAO,OAAO;;;;;;;;;;;;;;;;;;AAK/C;GA3GgB;KAAA", "debugId": null}}, {"offset": {"line": 3719, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/add-backend-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Loader2 } from \"lucide-react\"\nimport { AddBackend } from \"@/wailsjs/go/main/App\"\nimport { ConnectionTestResult } from \"./connection-test-result\"\n\ninterface AddBackendDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onBackendAdded?: () => void\n}\n\nexport function AddBackendDialog({ open, onOpenChange, onBackendAdded }: AddBackendDialogProps) {\n  const [formData, setFormData] = React.useState({\n    name: \"\",\n    url: \"\",\n    api_key: \"\",\n  })\n  const [isLoading, setIsLoading] = React.useState(false)\n  const [errors, setErrors] = React.useState<Record<string, string>>({})\n\n  const resetForm = () => {\n    setFormData({\n      name: \"\",\n      url: \"\",\n      api_key: \"\",\n    })\n    setErrors({})\n  }\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: \"\" }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n    \n    if (!formData.name.trim()) {\n      newErrors.name = \"Backend name is required\"\n    }\n    \n    if (!formData.url.trim()) {\n      newErrors.url = \"Backend URL is required\"\n    } else {\n      // Basic URL validation\n      try {\n        const url = formData.url.startsWith('http') ? formData.url : `https://${formData.url}`\n        new URL(url)\n      } catch {\n        newErrors.url = \"Please enter a valid URL\"\n      }\n    }\n    \n    if (!formData.api_key.trim()) {\n      newErrors.api_key = \"API Key is required\"\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      await AddBackend(formData)\n      onBackendAdded?.()\n      onOpenChange(false)\n      resetForm()\n    } catch (error) {\n      setErrors({ submit: `Failed to add backend: ${error}` })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (!newOpen) {\n      resetForm()\n    }\n    onOpenChange(newOpen)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleOpenChange}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>Add New Backend</DialogTitle>\n          <DialogDescription>\n            Add a new Acunetix scanner backend. You can test the connection before saving.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"name\">Backend Name</Label>\n            <Input\n              id=\"name\"\n              placeholder=\"e.g., Production Scanner\"\n              value={formData.name}\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\n              className={errors.name ? \"border-red-500\" : \"\"}\n            />\n            {errors.name && <p className=\"text-sm text-red-500\">{errors.name}</p>}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"url\">Backend URL</Label>\n            <Input\n              id=\"url\"\n              placeholder=\"https://scanner.example.com:3443\"\n              value={formData.url}\n              onChange={(e) => handleInputChange(\"url\", e.target.value)}\n              className={errors.url ? \"border-red-500\" : \"\"}\n            />\n            {errors.url && <p className=\"text-sm text-red-500\">{errors.url}</p>}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"api_key\">API Key</Label>\n            <Input\n              id=\"api_key\"\n              type=\"password\"\n              placeholder=\"Enter your API key\"\n              value={formData.api_key}\n              onChange={(e) => handleInputChange(\"api_key\", e.target.value)}\n              className={errors.api_key ? \"border-red-500\" : \"\"}\n            />\n            {errors.api_key && <p className=\"text-sm text-red-500\">{errors.api_key}</p>}\n          </div>\n\n\n\n          {/* Test Connection Section */}\n          <ConnectionTestResult\n            formData={formData}\n            disabled={isLoading}\n          />\n\n          {errors.submit && (\n            <p className=\"text-sm text-red-500\">{errors.submit}</p>\n          )}\n        </form>\n\n        <DialogFooter>\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={() => handleOpenChange(false)}\n            disabled={isLoading}\n          >\n            Cancel\n          </Button>\n          <Button\n            type=\"submit\"\n            onClick={handleSubmit}\n            disabled={isLoading || !formData.name || !formData.url || !formData.api_key}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Adding...\n              </>\n            ) : (\n              \"Add Backend\"\n            )}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;AAwBO,SAAS,iBAAiB,KAA6D;QAA7D,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,EAAyB,GAA7D;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,yKAAc,CAAC;QAC7C,MAAM;QACN,KAAK;QACL,SAAS;IACX;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,yKAAc,CAAC;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,yKAAc,CAAyB,CAAC;IAEpE,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,KAAK;YACL,SAAS;QACX;QACA,UAAU,CAAC;IACb;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI;YACxB,UAAU,GAAG,GAAG;QAClB,OAAO;YACL,uBAAuB;YACvB,IAAI;gBACF,MAAM,MAAM,SAAS,GAAG,CAAC,UAAU,CAAC,UAAU,SAAS,GAAG,GAAG,AAAC,WAAuB,OAAb,SAAS,GAAG;gBACpF,IAAI,IAAI;YACV,EAAE,UAAM;gBACN,UAAU,GAAG,GAAG;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAIA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,IAAA,6IAAU,EAAC;YACjB,2BAAA,qCAAA;YACA,aAAa;YACb;QACF,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,QAAQ,AAAC,0BAA+B,OAAN;YAAQ;QACxD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,SAAS;YACZ;QACF;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,wIAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,+IAAa;YAAC,WAAU;;8BACvB,6LAAC,8IAAY;;sCACX,6LAAC,6IAAW;sCAAC;;;;;;sCACb,6LAAC,mJAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,sIAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gCAE7C,OAAO,IAAI,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,IAAI;;;;;;;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAM;;;;;;8CACrB,6LAAC,sIAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,GAAG;oCACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACxD,WAAW,OAAO,GAAG,GAAG,mBAAmB;;;;;;gCAE5C,OAAO,GAAG,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,GAAG;;;;;;;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAU;;;;;;8CACzB,6LAAC,sIAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gCAEhD,OAAO,OAAO,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,OAAO;;;;;;;;;;;;sCAMxE,6LAAC,sKAAoB;4BACnB,UAAU;4BACV,UAAU;;;;;;wBAGX,OAAO,MAAM,kBACZ,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,MAAM;;;;;;;;;;;;8BAItD,6LAAC,8IAAY;;sCACX,6LAAC,wIAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,IAAM,iBAAiB;4BAChC,UAAU;sCACX;;;;;;sCAGD,6LAAC,wIAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,OAAO;sCAE1E,0BACC;;kDACE,6LAAC,+NAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA3KgB;KAAA", "debugId": null}}, {"offset": {"line": 4047, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/edit-backend-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Loader2, Trash2 } from \"lucide-react\"\nimport { UpdateBackend, DeleteBackend } from \"@/wailsjs/go/main/App\"\nimport { main } from \"@/wailsjs/go/models\"\nimport { ConnectionTestResult } from \"./connection-test-result\"\n\ninterface EditBackendDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  backend: main.Backend | null\n  onBackendUpdated?: () => void\n  onBackendDeleted?: () => void\n}\n\nexport function EditBackendDialog({ \n  open, \n  onOpenChange, \n  backend, \n  onBackendUpdated,\n  onBackendDeleted \n}: EditBackendDialogProps) {\n  const [formData, setFormData] = React.useState({\n    name: \"\",\n    url: \"\",\n    api_key: \"\",\n  })\n  const [isLoading, setIsLoading] = React.useState(false)\n  const [isDeleting, setIsDeleting] = React.useState(false)\n  const [errors, setErrors] = React.useState<Record<string, string>>({})\n\n  // Update form data when backend changes or dialog opens\n  React.useEffect(() => {\n    if (backend && open) {\n      setFormData({\n        name: backend.name || \"\",\n        url: backend.url || \"\",\n        api_key: backend.api_key || \"\",\n      })\n      // Clear any previous errors\n      setErrors({})\n    }\n  }, [backend, open])\n\n  const resetForm = () => {\n    setFormData({\n      name: \"\",\n      url: \"\",\n      api_key: \"\",\n    })\n    setErrors({})\n  }\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: \"\" }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n    \n    if (!formData.name.trim()) {\n      newErrors.name = \"Backend name is required\"\n    }\n    \n    if (!formData.url.trim()) {\n      newErrors.url = \"Backend URL is required\"\n    } else {\n      // Basic URL validation\n      try {\n        const url = formData.url.startsWith('http') ? formData.url : `https://${formData.url}`\n        new URL(url)\n      } catch {\n        newErrors.url = \"Please enter a valid URL\"\n      }\n    }\n    \n    if (!formData.api_key.trim()) {\n      newErrors.api_key = \"API Key is required\"\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!backend || !validateForm()) {\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      await UpdateBackend(backend.id, formData)\n      onBackendUpdated?.()\n      onOpenChange(false)\n    } catch (error) {\n      setErrors({ submit: `Failed to update backend: ${error}` })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleDelete = async () => {\n    if (!backend) return\n\n    if (!confirm(`Are you sure you want to delete &quot;${backend.name}&quot;? This action cannot be undone.`)) {\n      return\n    }\n\n    setIsDeleting(true)\n\n    try {\n      await DeleteBackend(backend.id)\n      onBackendDeleted?.()\n      onOpenChange(false)\n    } catch (error) {\n      setErrors({ delete: `Failed to delete backend: ${error}` })\n    } finally {\n      setIsDeleting(false)\n    }\n  }\n\n  const handleOpenChange = (newOpen: boolean) => {\n    // Prevent closing dialog while deleting\n    if (!newOpen && isDeleting) {\n      return\n    }\n\n    onOpenChange(newOpen)\n    // Only reset form when closing, and do it after the dialog closes\n    if (!newOpen) {\n      setTimeout(() => {\n        resetForm()\n      }, 100)\n    }\n  }\n\n  if (!backend) {\n    return null\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleOpenChange}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>Edit Backend</DialogTitle>\n          <DialogDescription>\n            Update the configuration for &quot;{backend.name}&quot;. You can test the connection after making changes.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"edit-name\">Backend Name</Label>\n            <Input\n              id=\"edit-name\"\n              placeholder=\"e.g., Production Scanner\"\n              value={formData.name}\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\n              className={errors.name ? \"border-red-500\" : \"\"}\n            />\n            {errors.name && <p className=\"text-sm text-red-500\">{errors.name}</p>}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"edit-url\">Backend URL</Label>\n            <Input\n              id=\"edit-url\"\n              placeholder=\"https://scanner.example.com:3443\"\n              value={formData.url}\n              onChange={(e) => handleInputChange(\"url\", e.target.value)}\n              className={errors.url ? \"border-red-500\" : \"\"}\n            />\n            {errors.url && <p className=\"text-sm text-red-500\">{errors.url}</p>}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"edit-api_key\">API Key</Label>\n            <Input\n              id=\"edit-api_key\"\n              type=\"password\"\n              placeholder=\"Enter your API key\"\n              value={formData.api_key}\n              onChange={(e) => handleInputChange(\"api_key\", e.target.value)}\n              className={errors.api_key ? \"border-red-500\" : \"\"}\n            />\n            {errors.api_key && <p className=\"text-sm text-red-500\">{errors.api_key}</p>}\n          </div>\n\n\n\n          {/* Test Connection Section */}\n          <ConnectionTestResult\n            formData={formData}\n            disabled={isLoading || isDeleting}\n          />\n\n          {errors.submit && (\n            <p className=\"text-sm text-red-500\">{errors.submit}</p>\n          )}\n          {errors.delete && (\n            <p className=\"text-sm text-red-500\">{errors.delete}</p>\n          )}\n        </form>\n\n        <DialogFooter className=\"flex justify-between\">\n          <Button\n            type=\"button\"\n            variant=\"destructive\"\n            onClick={handleDelete}\n            disabled={isLoading || isDeleting}\n            className=\"mr-auto\"\n          >\n            {isDeleting ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Deleting...\n              </>\n            ) : (\n              <>\n                <Trash2 className=\"mr-2 h-4 w-4\" />\n                Delete\n              </>\n            )}\n          </Button>\n          \n          <div className=\"flex gap-2\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => handleOpenChange(false)}\n              disabled={isLoading || isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              onClick={handleSubmit}\n              disabled={isLoading || isDeleting || !formData.name || !formData.url || !formData.api_key}\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Updating...\n                </>\n              ) : (\n                \"Update Backend\"\n              )}\n            </Button>\n          </div>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AAAA;AACA;AAEA;;;AAjBA;;;;;;;;;AA2BO,SAAS,kBAAkB,KAMT;QANS,EAChC,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,gBAAgB,EAChB,gBAAgB,EACO,GANS;;IAOhC,MAAM,CAAC,UAAU,YAAY,GAAG,yKAAc,CAAC;QAC7C,MAAM;QACN,KAAK;QACL,SAAS;IACX;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,yKAAc,CAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,yKAAc,CAAC;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,yKAAc,CAAyB,CAAC;IAEpE,wDAAwD;IACxD,0KAAe;uCAAC;YACd,IAAI,WAAW,MAAM;gBACnB,YAAY;oBACV,MAAM,QAAQ,IAAI,IAAI;oBACtB,KAAK,QAAQ,GAAG,IAAI;oBACpB,SAAS,QAAQ,OAAO,IAAI;gBAC9B;gBACA,4BAA4B;gBAC5B,UAAU,CAAC;YACb;QACF;sCAAG;QAAC;QAAS;KAAK;IAElB,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,KAAK;YACL,SAAS;QACX;QACA,UAAU,CAAC;IACb;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI;YACxB,UAAU,GAAG,GAAG;QAClB,OAAO;YACL,uBAAuB;YACvB,IAAI;gBACF,MAAM,MAAM,SAAS,GAAG,CAAC,UAAU,CAAC,UAAU,SAAS,GAAG,GAAG,AAAC,WAAuB,OAAb,SAAS,GAAG;gBACpF,IAAI,IAAI;YACV,EAAE,UAAM;gBACN,UAAU,GAAG,GAAG;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAIA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC/B;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,IAAA,gJAAa,EAAC,QAAQ,EAAE,EAAE;YAChC,6BAAA,uCAAA;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,QAAQ,AAAC,6BAAkC,OAAN;YAAQ;QAC3D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,QAAQ,AAAC,yCAAqD,OAAb,QAAQ,IAAI,EAAC,2CAAyC;YAC1G;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,IAAA,gJAAa,EAAC,QAAQ,EAAE;YAC9B,6BAAA,uCAAA;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,QAAQ,AAAC,6BAAkC,OAAN;YAAQ;QAC3D,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,wCAAwC;QACxC,IAAI,CAAC,WAAW,YAAY;YAC1B;QACF;QAEA,aAAa;QACb,kEAAkE;QAClE,IAAI,CAAC,SAAS;YACZ,WAAW;gBACT;YACF,GAAG;QACL;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,wIAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,+IAAa;YAAC,WAAU;;8BACvB,6LAAC,8IAAY;;sCACX,6LAAC,6IAAW;sCAAC;;;;;;sCACb,6LAAC,mJAAiB;;gCAAC;gCACmB,QAAQ,IAAI;gCAAC;;;;;;;;;;;;;8BAIrD,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,6LAAC,sIAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gCAE7C,OAAO,IAAI,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,IAAI;;;;;;;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,sIAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,GAAG;oCACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACxD,WAAW,OAAO,GAAG,GAAG,mBAAmB;;;;;;gCAE5C,OAAO,GAAG,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,GAAG;;;;;;;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,6LAAC,sIAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gCAEhD,OAAO,OAAO,kBAAI,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,OAAO;;;;;;;;;;;;sCAMxE,6LAAC,sKAAoB;4BACnB,UAAU;4BACV,UAAU,aAAa;;;;;;wBAGxB,OAAO,MAAM,kBACZ,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,MAAM;;;;;;wBAEnD,OAAO,MAAM,kBACZ,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,MAAM;;;;;;;;;;;;8BAItD,6LAAC,8IAAY;oBAAC,WAAU;;sCACtB,6LAAC,wIAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,aAAa;4BACvB,WAAU;sCAET,2BACC;;kDACE,6LAAC,+NAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,uNAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wIAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,iBAAiB;oCAChC,UAAU,aAAa;8CACxB;;;;;;8CAGD,6LAAC,wIAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU,aAAa,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,OAAO;8CAExF,0BACC;;0DACE,6LAAC,+NAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAvPgB;KAAA", "debugId": null}}, {"offset": {"line": 4477, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  Radar,\n  Frame,\n  ChartColumn,\n  Bug,\n  Target,\n  Home,\n} from \"lucide-react\"\n\nimport { NavMain } from \"@/components/nav-main\"\nimport { NavProjects } from \"@/components/nav-projects\"\nimport { NavSettings } from \"@/components/nav-settings\"\nimport { BackendSwitcher } from \"@/components/backend-switcher\"\nimport { AddBackendDialog } from \"@/components/add-backend-dialog\"\nimport { EditBackendDialog } from \"@/components/edit-backend-dialog\"\nimport { main } from \"@/wailsjs/go/models\"\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarHeader,\n  SidebarRail,\n} from \"@/components/ui/sidebar\"\n\ninterface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {\n  onNavigate?: (view: string) => void\n}\n\nexport function AppSidebar({ onNavigate, ...props }: AppSidebarProps) {\n  const [showAddBackend, setShowAddBackend] = React.useState(false)\n  const [showEditBackend, setShowEditBackend] = React.useState(false)\n  const [editingBackend, setEditingBackend] = React.useState<main.Backend | null>(null)\n  const [refreshTrigger, setRefreshTrigger] = React.useState(0)\n\n  // This is sample data.\n  const data = {\n    user: {\n      name: \"shadcn\",\n      email: \"<EMAIL>\",\n      avatar: \"/avatars/shadcn.jpg\",\n    },\n    navMain: [\n      {\n        title: \"Dashboard\",\n        url: \"#\",\n        icon: Home,\n        isActive: true,\n        onClick: () => onNavigate?.(\"dashboard\"),\n      },\n      {\n        title: \"Targets\",\n        url: \"#\",\n        icon: Target,\n        onClick: () => onNavigate?.(\"targets\"),\n        items: [\n          {\n            title: \"Manage Targets\",\n            url: \"#\",\n            onClick: () => onNavigate?.(\"targets\"),\n          },\n        {\n          title: \"Target Groups\",\n          url: \"#\",\n        },\n      ],\n    },\n    {\n      title: \"Scans\",\n      url: \"#\",\n      icon: Radar,\n    },\n    {\n      title: \"Vulnerabilities\",\n      url: \"#\",\n      icon: Bug,\n    },\n    {\n      title: \"Reports\",\n      url: \"#\",\n      icon: ChartColumn,\n    },\n  ],\n  projects: [\n    {\n      name: \"Project A\",\n      url: \"#\",\n      icon: Frame,\n    },\n    {\n      name: \"Project B\",\n      url: \"#\",\n      icon: Frame,\n    },\n    {\n      name: \"Project C\",\n      url: \"#\",\n      icon: Frame,\n    },\n  ],\n}\n\n  const handleAddBackend = () => {\n    setShowAddBackend(true)\n  }\n\n  const handleEditBackend = (backend: main.Backend) => {\n    setEditingBackend(backend)\n    setShowEditBackend(true)\n  }\n\n  const handleBackendAdded = () => {\n    // Trigger refresh of the backend list\n    setRefreshTrigger(prev => prev + 1)\n  }\n\n  const handleBackendUpdated = () => {\n    // Trigger refresh of the backend list\n    setRefreshTrigger(prev => prev + 1)\n  }\n\n  const handleBackendDeleted = () => {\n    // Trigger refresh of the backend list\n    setRefreshTrigger(prev => prev + 1)\n  }\n\n  return (\n    <>\n      <Sidebar collapsible=\"icon\" {...props}>\n        <SidebarHeader>\n          <BackendSwitcher\n            onAddBackend={handleAddBackend}\n            onEditBackend={handleEditBackend}\n            refreshTrigger={refreshTrigger}\n          />\n        </SidebarHeader>\n        <SidebarContent>\n          <NavMain items={data.navMain} onNavigate={onNavigate} />\n          <NavProjects projects={data.projects} />\n        </SidebarContent>\n        <SidebarFooter>\n          <NavSettings />\n        </SidebarFooter>\n        <SidebarRail />\n      </Sidebar>\n\n      <AddBackendDialog\n        open={showAddBackend}\n        onOpenChange={setShowAddBackend}\n        onBackendAdded={handleBackendAdded}\n      />\n\n      <EditBackendDialog\n        open={showEditBackend}\n        onOpenChange={setShowEditBackend}\n        backend={editingBackend}\n        onBackendUpdated={handleBackendUpdated}\n        onBackendDeleted={handleBackendDeleted}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAnBA;;;;;;;;;;AA+BO,SAAS,WAAW,KAAyC;QAAzC,EAAE,UAAU,EAAE,GAAG,OAAwB,GAAzC;;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,yKAAc,CAAC;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,yKAAc,CAAC;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,yKAAc,CAAsB;IAChF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,yKAAc,CAAC;IAE3D,uBAAuB;IACvB,MAAM,OAAO;QACX,MAAM;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAI;gBACV,UAAU;gBACV,SAAS,IAAM,uBAAA,iCAAA,WAAa;YAC9B;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mNAAM;gBACZ,SAAS,IAAM,uBAAA,iCAAA,WAAa;gBAC5B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;wBACL,SAAS,IAAM,uBAAA,iCAAA,WAAa;oBAC9B;oBACF;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAK;YACb;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAG;YACX;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sOAAW;YACnB;SACD;QACD,UAAU;YACR;gBACE,MAAM;gBACN,KAAK;gBACL,MAAM,gNAAK;YACb;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,MAAM,gNAAK;YACb;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,MAAM,gNAAK;YACb;SACD;IACH;IAEE,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB;QACzB,sCAAsC;QACtC,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,MAAM,uBAAuB;QAC3B,sCAAsC;QACtC,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,MAAM,uBAAuB;QAC3B,sCAAsC;QACtC,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,qBACE;;0BACE,6LAAC,0IAAO;gBAAC,aAAY;gBAAQ,GAAG,KAAK;;kCACnC,6LAAC,gJAAa;kCACZ,cAAA,6LAAC,wJAAe;4BACd,cAAc;4BACd,eAAe;4BACf,gBAAgB;;;;;;;;;;;kCAGpB,6LAAC,iJAAc;;0CACb,6LAAC,wIAAO;gCAAC,OAAO,KAAK,OAAO;gCAAE,YAAY;;;;;;0CAC1C,6LAAC,gJAAW;gCAAC,UAAU,KAAK,QAAQ;;;;;;;;;;;;kCAEtC,6LAAC,gJAAa;kCACZ,cAAA,6LAAC,gJAAW;;;;;;;;;;kCAEd,6LAAC,8IAAW;;;;;;;;;;;0BAGd,6LAAC,8JAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,gBAAgB;;;;;;0BAGlB,6LAAC,gKAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,SAAS;gBACT,kBAAkB;gBAClB,kBAAkB;;;;;;;;AAI1B;GApIgB;KAAA", "debugId": null}}, {"offset": {"line": 4700, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\n}\n\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\n  return (\n    <ol\n      data-slot=\"breadcrumb-list\"\n      className={cn(\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-item\"\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbLink({\n  asChild,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"breadcrumb-link\"\n      className={cn(\"hover:text-foreground transition-colors\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-page\"\n      role=\"link\"\n      aria-disabled=\"true\"\n      aria-current=\"page\"\n      className={cn(\"text-foreground font-normal\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbSeparator({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-separator\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"[&>svg]:size-3.5\", className)}\n      {...props}\n    >\n      {children ?? <ChevronRight />}\n    </li>\n  )\n}\n\nfunction BreadcrumbEllipsis({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-ellipsis\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"flex size-9 items-center justify-center\", className)}\n      {...props}\n    >\n      <MoreHorizontal className=\"size-4\" />\n      <span className=\"sr-only\">More</span>\n    </span>\n  )\n}\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,KAAyC;QAAzC,EAAE,GAAG,OAAoC,GAAzC;IAClB,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,KAMvB;QANuB,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ,GANuB;IAOtB,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,IAAA,qHAAE,EAAC,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,KAIA;QAJA,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,GAJA;IAK3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,qHAAE,EAAC,oBAAoB;QACjC,GAAG,KAAK;kBAER,qBAAA,sBAAA,yBAAY,6LAAC,yOAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,KAGG;QAHH,EAC1B,SAAS,EACT,GAAG,OAC0B,GAHH;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,qHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,qOAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 4862, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,6KAAoB;AAEnC,MAAM,cAAc,8KAAqB;AAEzC,MAAM,cAAc,8KAAqB;AAEzC,MAAM,8BAAgB,2KAAgB,MAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,gLAAuB;QACtB,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,sOAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,2KAAgB,CAG3C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,uLAA8B;QAC7B,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,gOAAS;YAAC,WAAU;;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,uLAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,2KAAgB,CAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,yLAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,sOAAW;YAAC,WAAU;;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,yLAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,2KAAgB,OAGpC,QAAyD;QAAxD,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO;yBACvD,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,KAAK;YACL,WAAW,IAAA,qHAAE,EACX,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,qHAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,qHAAE,EAAC,0CAA0C;QACvD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,8KAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,2KAAgB,OAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,6KAAoB;QACnB,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,gNAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,2KAAgB,QAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,kLAAyB;QACxB,KAAK;QACL,WAAW,IAAA,qHAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kLAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,2KAAgB,MAG5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,IAAA,qHAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAM,KAAK;QAAK,WAAW,IAAA,qHAAE,EAAC,mBAAmB;QAAa,GAAG,KAAK;;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,2KAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,2KAAgB,OAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,2KAAgB,QAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EACX,oGACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,2KAAgB,QAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EAAC,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,2KAAgB,QAGnC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,qHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5274, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/targets/table-skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\"\n\ninterface TableSkeletonProps {\n  rows?: number\n  columns?: number\n}\n\nexport function TableSkeleton({ rows = 5, columns = 5 }: TableSkeletonProps) {\n  return (\n    <div className=\"rounded-md border\">\n      <Table>\n        <TableHeader>\n          <TableRow>\n            {Array.from({ length: columns }).map((_, index) => (\n              <TableHead key={index}>\n                <Skeleton className=\"h-4 w-20\" />\n              </TableHead>\n            ))}\n          </TableRow>\n        </TableHeader>\n        <TableBody>\n          {Array.from({ length: rows }).map((_, rowIndex) => (\n            <TableRow key={rowIndex}>\n              {Array.from({ length: columns }).map((_, colIndex) => (\n                <TableCell key={colIndex}>\n                  {colIndex === 0 ? (\n                    <Skeleton className=\"h-4 w-32\" />\n                  ) : colIndex === 1 ? (\n                    <Skeleton className=\"h-4 w-24\" />\n                  ) : colIndex === 2 ? (\n                    <div className=\"flex items-center gap-1\">\n                      <Skeleton className=\"h-3 w-3 rounded-full\" />\n                      <Skeleton className=\"h-4 w-16\" />\n                    </div>\n                  ) : colIndex === 3 ? (\n                    <Skeleton className=\"h-4 w-12\" />\n                  ) : (\n                    <Skeleton className=\"h-8 w-8 rounded\" />\n                  )}\n                </TableCell>\n              ))}\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAcO,SAAS,cAAc,KAA6C;QAA7C,EAAE,OAAO,CAAC,EAAE,UAAU,CAAC,EAAsB,GAA7C;IAC5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sIAAK;;8BACJ,6LAAC,4IAAW;8BACV,cAAA,6LAAC,yIAAQ;kCACN,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,sBACvC,6LAAC,0IAAS;0CACR,cAAA,6LAAC,4IAAQ;oCAAC,WAAU;;;;;;+BADN;;;;;;;;;;;;;;;8BAMtB,6LAAC,0IAAS;8BACP,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,6LAAC,yIAAQ;sCACN,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC,0IAAS;8CACP,aAAa,kBACZ,6LAAC,4IAAQ;wCAAC,WAAU;;;;;+CAClB,aAAa,kBACf,6LAAC,4IAAQ;wCAAC,WAAU;;;;;+CAClB,aAAa,kBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4IAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,4IAAQ;gDAAC,WAAU;;;;;;;;;;;+CAEpB,aAAa,kBACf,6LAAC,4IAAQ;wCAAC,WAAU;;;;;6DAEpB,6LAAC,4IAAQ;wCAAC,WAAU;;;;;;mCAbR;;;;;2BAFL;;;;;;;;;;;;;;;;;;;;;AAyB3B;KAxCgB", "debugId": null}}, {"offset": {"line": 5408, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/refresh-icon.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { RefreshCw } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface RefreshIconProps {\n  isRefreshing?: boolean\n  className?: string\n}\n\nexport function RefreshIcon({ isRefreshing = false, className }: RefreshIconProps) {\n  const [isHovered, setIsHovered] = React.useState(false)\n\n  return (\n    <RefreshCw\n      className={cn(\n        \"h-4 w-4 transition-all duration-300 ease-in-out\",\n        isRefreshing && \"animate-spin\",\n        !isRefreshing && isHovered && \"rotate-180 scale-110\",\n        className\n      )}\n      onMouseEnter={() => !isRefreshing && setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      style={{\n        transformOrigin: \"center\",\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAOO,SAAS,YAAY,KAAqD;QAArD,EAAE,eAAe,KAAK,EAAE,SAAS,EAAoB,GAArD;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,yKAAc,CAAC;IAEjD,qBACE,6LAAC,gOAAS;QACR,WAAW,IAAA,qHAAE,EACX,mDACA,gBAAgB,gBAChB,CAAC,gBAAgB,aAAa,wBAC9B;QAEF,cAAc,IAAM,CAAC,gBAAgB,aAAa;QAClD,cAAc,IAAM,aAAa;QACjC,OAAO;YACL,iBAAiB;QACnB;;;;;;AAGN;GAlBgB;KAAA", "debugId": null}}, {"offset": {"line": 5449, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/targets/data-table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ColumnDef,\n  ColumnFiltersState,\n  SortingState,\n  VisibilityState,\n  flexRender,\n  getCoreRowModel,\n  getFilteredRowModel,\n  getPaginationRowModel,\n  getSortedRowModel,\n  useReactTable,\n} from \"@tanstack/react-table\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\"\nimport { ChevronDown, Plus } from \"lucide-react\"\nimport { TableSkeleton } from \"./table-skeleton\"\nimport { RefreshIcon } from \"@/components/ui/refresh-icon\"\n\ninterface DataTableProps<TData, TValue> {\n  columns: ColumnDef<TData, TValue>[]\n  data: TData[]\n  loading?: boolean\n  refreshing?: boolean\n  onAddTarget?: () => void\n  onRefresh?: () => void\n}\n\nexport function DataTable<TData, TValue>({\n  columns,\n  data,\n  loading = false,\n  refreshing = false,\n  onAddTarget,\n  onRefresh,\n}: DataTableProps<TData, TValue>) {\n  const [sorting, setSorting] = React.useState<SortingState>([])\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])\n  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})\n  const [rowSelection, setRowSelection] = React.useState({})\n\n  const table = useReactTable({\n    data,\n    columns,\n    onSortingChange: setSorting,\n    onColumnFiltersChange: setColumnFilters,\n    getCoreRowModel: getCoreRowModel(),\n    getPaginationRowModel: getPaginationRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getFilteredRowModel: getFilteredRowModel(),\n    onColumnVisibilityChange: setColumnVisibility,\n    onRowSelectionChange: setRowSelection,\n    state: {\n      sorting,\n      columnFilters,\n      columnVisibility,\n      rowSelection,\n    },\n  })\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"flex items-center justify-between py-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Input\n            placeholder=\"Filter addresses...\"\n            value={(table.getColumn(\"address\")?.getFilterValue() as string) ?? \"\"}\n            onChange={(event) =>\n              table.getColumn(\"address\")?.setFilterValue(event.target.value)\n            }\n            className=\"max-w-sm\"\n          />\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"outline\" className=\"ml-auto\">\n                Columns <ChevronDown className=\"ml-2 h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              {table\n                .getAllColumns()\n                .filter((column) => column.getCanHide())\n                .map((column) => {\n                  return (\n                    <DropdownMenuCheckboxItem\n                      key={column.id}\n                      className=\"capitalize\"\n                      checked={column.getIsVisible()}\n                      onCheckedChange={(value) =>\n                        column.toggleVisibility(!!value)\n                      }\n                    >\n                      {column.id}\n                    </DropdownMenuCheckboxItem>\n                  )\n                })}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onRefresh}\n            disabled={loading || refreshing}\n            className=\"group transition-all duration-200 hover:bg-muted/50\"\n          >\n            <RefreshIcon isRefreshing={refreshing} className=\"mr-2\" />\n            Refresh\n          </Button>\n          <Button onClick={onAddTarget}>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Target\n          </Button>\n        </div>\n      </div>\n      {loading ? (\n        <TableSkeleton rows={5} columns={5} />\n      ) : (\n        <div className={`rounded-md border transition-opacity duration-300 ${refreshing ? 'opacity-60' : 'opacity-100'}`}>\n          <Table>\n            <TableHeader>\n              {table.getHeaderGroups().map((headerGroup) => (\n                <TableRow key={headerGroup.id}>\n                  {headerGroup.headers.map((header) => {\n                    return (\n                      <TableHead key={header.id}>\n                        {header.isPlaceholder\n                          ? null\n                          : flexRender(\n                              header.column.columnDef.header,\n                              header.getContext()\n                            )}\n                      </TableHead>\n                    )\n                  })}\n                </TableRow>\n              ))}\n            </TableHeader>\n            <TableBody>\n              {table.getRowModel().rows?.length ? (\n                table.getRowModel().rows.map((row) => (\n                  <TableRow\n                    key={row.id}\n                    data-state={row.getIsSelected() && \"selected\"}\n                    className=\"transition-all duration-200 hover:bg-muted/50 animate-in fade-in-0 slide-in-from-left-1\"\n                  >\n                    {row.getVisibleCells().map((cell) => (\n                      <TableCell key={cell.id}>\n                        {flexRender(\n                          cell.column.columnDef.cell,\n                          cell.getContext()\n                        )}\n                      </TableCell>\n                    ))}\n                  </TableRow>\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"h-24 text-center\">\n                    No targets found. Add your first target to get started.\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </div>\n      )}\n      <div className=\"flex items-center justify-end space-x-2 py-4\">\n        <div className=\"flex-1 text-sm text-muted-foreground\">\n          {table.getFilteredSelectedRowModel().rows.length} of{\" \"}\n          {table.getFilteredRowModel().rows.length} row(s) selected.\n        </div>\n        <div className=\"space-x-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => table.previousPage()}\n            disabled={!table.getCanPreviousPage()}\n          >\n            Previous\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => table.nextPage()}\n            disabled={!table.getCanNextPage()}\n          >\n            Next\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAYA;AACA;AACA;AAMA;AAQA;AAAA;AACA;AACA;;;AAjCA;;;;;;;;;;AA4CO,SAAS,UAAyB,KAOT;QAPS,EACvC,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,KAAK,EAClB,WAAW,EACX,SAAS,EACqB,GAPS;QAsCrB,kBAyEL;;IAvGb,MAAM,CAAC,SAAS,WAAW,GAAG,yKAAc,CAAe,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,yKAAc,CAAqB,EAAE;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,yKAAc,CAAkB,CAAC;IACjF,MAAM,CAAC,cAAc,gBAAgB,GAAG,yKAAc,CAAC,CAAC;IAExD,MAAM,QAAQ,IAAA,0MAAa,EAAC;QAC1B;QACA;QACA,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB,IAAA,2LAAe;QAChC,uBAAuB,IAAA,iMAAqB;QAC5C,mBAAmB,IAAA,6LAAiB;QACpC,qBAAqB,IAAA,+LAAmB;QACxC,0BAA0B;QAC1B,sBAAsB;QACtB,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;QAQiB;IANjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAK;gCACJ,aAAY;gCACZ,OAAO,CAAA,OAAA,CAAC,mBAAA,MAAM,SAAS,CAAC,UAAsC,cAAtD,uCAAA,iBAA4B,cAAc,gBAA3C,kBAAA,OAA4D;gCACnE,UAAU,CAAC;wCACT;4CAAA,mBAAA,MAAM,SAAS,CAAC,wBAAhB,uCAAA,iBAA4B,cAAc,CAAC,MAAM,MAAM,CAAC,KAAK;;gCAE/D,WAAU;;;;;;0CAEZ,6LAAC,wJAAY;;kDACX,6LAAC,+JAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,wIAAM;4CAAC,SAAQ;4CAAU,WAAU;;gDAAU;8DACpC,6LAAC,sOAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGnC,6LAAC,+JAAmB;wCAAC,OAAM;kDACxB,MACE,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC;4CACJ,qBACE,6LAAC,oKAAwB;gDAEvB,WAAU;gDACV,SAAS,OAAO,YAAY;gDAC5B,iBAAiB,CAAC,QAChB,OAAO,gBAAgB,CAAC,CAAC,CAAC;0DAG3B,OAAO,EAAE;+CAPL,OAAO,EAAE;;;;;wCAUpB;;;;;;;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW;gCACrB,WAAU;;kDAEV,6LAAC,sJAAW;wCAAC,cAAc;wCAAY,WAAU;;;;;;oCAAS;;;;;;;0CAG5D,6LAAC,wIAAM;gCAAC,SAAS;;kDACf,6LAAC,6MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAKtC,wBACC,6LAAC,+JAAa;gBAAC,MAAM;gBAAG,SAAS;;;;;qCAEjC,6LAAC;gBAAI,WAAW,AAAC,qDAA8F,OAA1C,aAAa,eAAe;0BAC/F,cAAA,6LAAC,sIAAK;;sCACJ,6LAAC,4IAAW;sCACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,yIAAQ;8CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;wCACxB,qBACE,6LAAC,0IAAS;sDACP,OAAO,aAAa,GACjB,OACA,IAAA,uMAAU,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CALT,OAAO,EAAE;;;;;oCAS7B;mCAZa,YAAY,EAAE;;;;;;;;;;sCAgBjC,6LAAC,0IAAS;sCACP,EAAA,0BAAA,MAAM,WAAW,GAAG,IAAI,cAAxB,8CAAA,wBAA0B,MAAM,IAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,6LAAC,yIAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;oCACnC,WAAU;8CAET,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,0IAAS;sDACP,IAAA,uMAAU,EACT,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAHH,KAAK,EAAE;;;;;mCALpB,IAAI,EAAE;;;;0DAef,6LAAC,yIAAQ;0CACP,cAAA,6LAAC,0IAAS;oCAAC,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;4BAAC;4BAAI;4BACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;4BAAC;;;;;;;kCAE3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;0CACpC;;;;;;0CAGD,6LAAC,wIAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;0CAChC;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GArKgB;;QAaA,0MAAa;;;KAbb", "debugId": null}}, {"offset": {"line": 5791, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,0KAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 5840, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/targets/columns.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ColumnDef } from \"@tanstack/react-table\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { ArrowUpDown, MoreHorizontal, Globe, Shield, AlertTriangle } from \"lucide-react\"\nimport { main } from \"@/wailsjs/go/models\"\n\nexport type Target = main.Target\n\ninterface ColumnsProps {\n  onDelete: (target: Target) => void\n}\n\nexport const createColumns = ({ onDelete }: ColumnsProps): ColumnDef<Target>[] => [\n  {\n    accessorKey: \"address\",\n    header: ({ column }) => {\n      return (\n        <Button\n          variant=\"ghost\"\n          onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\n          className=\"h-8 px-2 lg:px-3\"\n        >\n          Address\n          <ArrowUpDown className=\"ml-2 h-4 w-4\" />\n        </Button>\n      )\n    },\n    cell: ({ row }) => {\n      const address = row.getValue(\"address\") as string\n      return (\n        <div className=\"font-medium\">\n          {address}\n        </div>\n      )\n    },\n  },\n  {\n    accessorKey: \"description\",\n    header: \"Description\",\n    cell: ({ row }) => {\n      const description = row.getValue(\"description\") as string\n      return (\n        <div className=\"max-w-[200px] truncate\">\n          {description || \"-\"}\n        </div>\n      )\n    },\n  },\n  {\n    accessorKey: \"criticality\",\n    header: ({ column }) => {\n      return (\n        <Button\n          variant=\"ghost\"\n          onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\n          className=\"h-8 px-2 lg:px-3\"\n        >\n          Criticality\n          <ArrowUpDown className=\"ml-2 h-4 w-4\" />\n        </Button>\n      )\n    },\n    cell: ({ row }) => {\n      const criticality = row.getValue(\"criticality\") as number\n      \n      if (criticality >= 30) {\n        return (\n          <Badge variant=\"destructive\" className=\"flex items-center gap-1 w-fit\">\n            <AlertTriangle className=\"h-3 w-3\" />\n            Critical\n          </Badge>\n        )\n      } else if (criticality >= 20) {\n        return (\n          <Badge variant=\"secondary\" className=\"flex items-center gap-1 w-fit\">\n            <Shield className=\"h-3 w-3\" />\n            High\n          </Badge>\n        )\n      } else if (criticality >= 10) {\n        return (\n          <Badge variant=\"outline\" className=\"flex items-center gap-1 w-fit\">\n            <Globe className=\"h-3 w-3\" />\n            Normal\n          </Badge>\n        )\n      } else {\n        return <Badge variant=\"secondary\">Low</Badge>\n      }\n    },\n    sortingFn: (rowA, rowB) => {\n      const a = rowA.getValue(\"criticality\") as number\n      const b = rowB.getValue(\"criticality\") as number\n      return a - b\n    },\n  },\n  {\n    accessorKey: \"type\",\n    header: \"Type\",\n    cell: ({ row }) => {\n      const type = row.getValue(\"type\") as string\n      return (\n        <Badge variant=\"outline\">\n          {type || \"Web\"}\n        </Badge>\n      )\n    },\n  },\n  {\n    id: \"actions\",\n    header: \"Actions\",\n    cell: ({ row }) => {\n      const target = row.original\n\n      return (\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"h-8 w-8 p-0\" size=\"icon\">\n              <span className=\"sr-only\">Open menu</span>\n              <MoreHorizontal className=\"h-4 w-4\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            <DropdownMenuItem\n              onClick={() => navigator.clipboard.writeText(target.address)}\n            >\n              Copy address\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem\n              onClick={() => onDelete(target)}\n              className=\"text-red-600 focus:text-red-600\"\n            >\n              Delete target\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      )\n    },\n  },\n]\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;AAqBO,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAgB;WAA0B;QAChF;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC,wIAAM;oBACL,SAAQ;oBACR,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;;wBACX;sCAEC,6LAAC,0OAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ,CAAC;gBAC7B,qBACE,6LAAC;oBAAI,WAAU;8BACZ;;;;;;YAGP;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC;gBACjC,qBACE,6LAAC;oBAAI,WAAU;8BACZ,eAAe;;;;;;YAGtB;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC,wIAAM;oBACL,SAAQ;oBACR,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;oBAC7D,WAAU;;wBACX;sCAEC,6LAAC,0OAAW;4BAAC,WAAU;;;;;;;;;;;;YAG7B;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC;gBAEjC,IAAI,eAAe,IAAI;oBACrB,qBACE,6LAAC,sIAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,6LAAC,4OAAa;gCAAC,WAAU;;;;;;4BAAY;;;;;;;gBAI3C,OAAO,IAAI,eAAe,IAAI;oBAC5B,qBACE,6LAAC,sIAAK;wBAAC,SAAQ;wBAAY,WAAU;;0CACnC,6LAAC,mNAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;gBAIpC,OAAO,IAAI,eAAe,IAAI;oBAC5B,qBACE,6LAAC,sIAAK;wBAAC,SAAQ;wBAAU,WAAU;;0CACjC,6LAAC,gNAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;gBAInC,OAAO;oBACL,qBAAO,6LAAC,sIAAK;wBAAC,SAAQ;kCAAY;;;;;;gBACpC;YACF;YACA,WAAW,CAAC,MAAM;gBAChB,MAAM,IAAI,KAAK,QAAQ,CAAC;gBACxB,MAAM,IAAI,KAAK,QAAQ,CAAC;gBACxB,OAAO,IAAI;YACb;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,qBACE,6LAAC,sIAAK;oBAAC,SAAQ;8BACZ,QAAQ;;;;;;YAGf;QACF;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ;gBAE3B,qBACE,6LAAC,wJAAY;;sCACX,6LAAC,+JAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC,wIAAM;gCAAC,SAAQ;gCAAQ,WAAU;gCAAc,MAAK;;kDACnD,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,qOAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG9B,6LAAC,+JAAmB;4BAAC,OAAM;;8CACzB,6LAAC,4JAAgB;oCACf,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;8CAC5D;;;;;;8CAGD,6LAAC,iKAAqB;;;;;8CACtB,6LAAC,4JAAgB;oCACf,SAAS,IAAM,SAAS;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAMT;QACF;KACD", "debugId": null}}, {"offset": {"line": 6121, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/targets-content.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  <PERSON>alogTitle,\n} from \"@/components/ui/dialog\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { GetTargets, AddTarget, DeleteTarget } from \"@/wailsjs/go/main/App\"\nimport { DataTable } from \"@/components/targets/data-table\"\nimport { createColumns, Target } from \"@/components/targets/columns\"\n\nexport function TargetsContent() {\n  const [targets, setTargets] = React.useState<Target[]>([])\n  const [loading, setLoading] = React.useState(false)\n  const [refreshing, setRefreshing] = React.useState(false)\n  const [addDialogOpen, setAddDialogOpen] = React.useState(false)\n  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)\n  const [selectedTarget, setSelectedTarget] = React.useState<Target | null>(null)\n  const [formData, setFormData] = React.useState({\n    address: \"\",\n    description: \"\",\n    criticality: 10,\n  })\n  const [errors, setErrors] = React.useState<Record<string, string>>({})\n\n  const loadTargets = async (isRefresh = false) => {\n    try {\n      if (isRefresh) {\n        setRefreshing(true)\n      } else {\n        setLoading(true)\n      }\n\n      const result = await GetTargets()\n      setTargets(result.targets || [])\n    } catch (error) {\n      console.error(\"Failed to load targets:\", error)\n      setTargets([])\n    } finally {\n      if (isRefresh) {\n        setRefreshing(false)\n      } else {\n        setLoading(false)\n      }\n    }\n  }\n\n  React.useEffect(() => {\n    loadTargets()\n  }, [])\n\n  const handleRefresh = React.useCallback(() => {\n    loadTargets(true)\n  }, [])\n\n  const handleAddTarget = async () => {\n    try {\n      // Validate form\n      const newErrors: Record<string, string> = {}\n      if (!formData.address.trim()) {\n        newErrors.address = \"Target address is required\"\n      }\n      \n      if (Object.keys(newErrors).length > 0) {\n        setErrors(newErrors)\n        return\n      }\n\n      setLoading(true)\n      await AddTarget({\n        address: formData.address.trim(),\n        description: formData.description.trim(),\n        criticality: formData.criticality,\n      })\n      \n      // Reset form and close dialog\n      setFormData({ address: \"\", description: \"\", criticality: 10 })\n      setErrors({})\n      setAddDialogOpen(false)\n      \n      // Reload targets\n      await loadTargets()\n    } catch (error) {\n      setErrors({ submit: `Failed to add target: ${error}` })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteTarget = async () => {\n    if (!selectedTarget) return\n\n    try {\n      setLoading(true)\n      await DeleteTarget(selectedTarget.target_id)\n      setDeleteDialogOpen(false)\n      setSelectedTarget(null)\n      await loadTargets()\n    } catch (error) {\n      console.error(\"Failed to delete target:\", error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const openDeleteDialog = React.useCallback((target: Target) => {\n    setSelectedTarget(target)\n    setDeleteDialogOpen(true)\n  }, [])\n\n  // Create columns with delete handler\n  const columns = React.useMemo(\n    () => createColumns({ onDelete: openDeleteDialog }),\n    [openDeleteDialog]\n  )\n\n  return (\n    <>\n      {/* Data Table */}\n      <DataTable\n        columns={columns}\n        data={targets}\n        loading={loading}\n        refreshing={refreshing}\n        onAddTarget={() => setAddDialogOpen(true)}\n        onRefresh={handleRefresh}\n      />\n\n      {/* Add Target Dialog */}\n      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>\n        <DialogContent className=\"sm:max-w-[500px]\">\n          <DialogHeader>\n            <DialogTitle>Add New Target</DialogTitle>\n            <DialogDescription>\n              Add a new target for security scanning. Enter the target URL or IP address.\n            </DialogDescription>\n          </DialogHeader>\n          \n          <div className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"address\">Target Address *</Label>\n              <Input\n                id=\"address\"\n                placeholder=\"https://example.com or *************\"\n                value={formData.address}\n                onChange={(e) => {\n                  setFormData(prev => ({ ...prev, address: e.target.value }))\n                  if (errors.address) setErrors(prev => ({ ...prev, address: \"\" }))\n                }}\n              />\n              {errors.address && <p className=\"text-sm text-red-500\">{errors.address}</p>}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <Input\n                id=\"description\"\n                placeholder=\"Optional description\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"criticality\">Criticality Level</Label>\n              <Select\n                value={formData.criticality.toString()}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, criticality: parseInt(value) }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"10\">Normal (10)</SelectItem>\n                  <SelectItem value=\"20\">High (20)</SelectItem>\n                  <SelectItem value=\"30\">Critical (30)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {errors.submit && (\n              <p className=\"text-sm text-red-500\">{errors.submit}</p>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setAddDialogOpen(false)\n                setFormData({ address: \"\", description: \"\", criticality: 10 })\n                setErrors({})\n              }}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleAddTarget} disabled={loading}>\n              {loading ? \"Adding...\" : \"Add Target\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Target</DialogTitle>\n            <DialogDescription>\n              Are you sure you want to delete the target &quot;{selectedTarget?.address}&quot;? \n              This action cannot be undone.\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setDeleteDialogOpen(false)\n                setSelectedTarget(null)\n              }}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteTarget}\n              disabled={loading}\n            >\n              {loading ? \"Deleting...\" : \"Delete\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAOA;AACA;AACA;;;AAvBA;;;;;;;;;;AAyBO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,yKAAc,CAAW,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,yKAAc,CAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,yKAAc,CAAC;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,yKAAc,CAAC;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,yKAAc,CAAC;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,yKAAc,CAAgB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,yKAAc,CAAC;QAC7C,SAAS;QACT,aAAa;QACb,aAAa;IACf;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,yKAAc,CAAyB,CAAC;IAEpE,MAAM,cAAc;YAAO,6EAAY;QACrC,IAAI;YACF,IAAI,WAAW;gBACb,cAAc;YAChB,OAAO;gBACL,WAAW;YACb;YAEA,MAAM,SAAS,MAAM,IAAA,6IAAU;YAC/B,WAAW,OAAO,OAAO,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,WAAW,EAAE;QACf,SAAU;YACR,IAAI,WAAW;gBACb,cAAc;YAChB,OAAO;gBACL,WAAW;YACb;QACF;IACF;IAEA,0KAAe;oCAAC;YACd;QACF;mCAAG,EAAE;IAEL,MAAM,gBAAgB,4KAAiB;qDAAC;YACtC,YAAY;QACd;oDAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,gBAAgB;YAChB,MAAM,YAAoC,CAAC;YAC3C,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;gBAC5B,UAAU,OAAO,GAAG;YACtB;YAEA,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;gBACrC,UAAU;gBACV;YACF;YAEA,WAAW;YACX,MAAM,IAAA,4IAAS,EAAC;gBACd,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,aAAa,SAAS,WAAW;YACnC;YAEA,8BAA8B;YAC9B,YAAY;gBAAE,SAAS;gBAAI,aAAa;gBAAI,aAAa;YAAG;YAC5D,UAAU,CAAC;YACX,iBAAiB;YAEjB,iBAAiB;YACjB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,QAAQ,AAAC,yBAA8B,OAAN;YAAQ;QACvD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,WAAW;YACX,MAAM,IAAA,+IAAY,EAAC,eAAe,SAAS;YAC3C,oBAAoB;YACpB,kBAAkB;YAClB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,4KAAiB;wDAAC,CAAC;YAC1C,kBAAkB;YAClB,oBAAoB;QACtB;uDAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,UAAU,wKAAa;2CAC3B,IAAM,IAAA,qJAAa,EAAC;gBAAE,UAAU;YAAiB;0CACjD;QAAC;KAAiB;IAGpB,qBACE;;0BAEE,6LAAC,uJAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,YAAY;gBACZ,aAAa,IAAM,iBAAiB;gBACpC,WAAW;;;;;;0BAIb,6LAAC,wIAAM;gBAAC,MAAM;gBAAe,cAAc;0BACzC,cAAA,6LAAC,+IAAa;oBAAC,WAAU;;sCACvB,6LAAC,8IAAY;;8CACX,6LAAC,6IAAW;8CAAC;;;;;;8CACb,6LAAC,mJAAiB;8CAAC;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,sIAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC;gDACT,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;gDACzD,IAAI,OAAO,OAAO,EAAE,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS;oDAAG,CAAC;4CACjE;;;;;;wCAED,OAAO,OAAO,kBAAI,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO;;;;;;;;;;;;8CAGxE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,sIAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;;;;;;;;;;;;8CAIlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,wIAAM;4CACL,OAAO,SAAS,WAAW,CAAC,QAAQ;4CACpC,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,SAAS;oDAAO,CAAC;;8DAExF,6LAAC,+IAAa;8DACZ,cAAA,6LAAC,6IAAW;;;;;;;;;;8DAEd,6LAAC,+IAAa;;sEACZ,6LAAC,4IAAU;4DAAC,OAAM;sEAAK;;;;;;sEACvB,6LAAC,4IAAU;4DAAC,OAAM;sEAAK;;;;;;sEACvB,6LAAC,4IAAU;4DAAC,OAAM;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;gCAK5B,OAAO,MAAM,kBACZ,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,MAAM;;;;;;;;;;;;sCAItD,6LAAC,8IAAY;;8CACX,6LAAC,wIAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,iBAAiB;wCACjB,YAAY;4CAAE,SAAS;4CAAI,aAAa;4CAAI,aAAa;wCAAG;wCAC5D,UAAU,CAAC;oCACb;oCACA,UAAU;8CACX;;;;;;8CAGD,6LAAC,wIAAM;oCAAC,SAAS;oCAAiB,UAAU;8CACzC,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC,wIAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,6LAAC,+IAAa;;sCACZ,6LAAC,8IAAY;;8CACX,6LAAC,6IAAW;8CAAC;;;;;;8CACb,6LAAC,mJAAiB;;wCAAC;wCACiC,2BAAA,qCAAA,eAAgB,OAAO;wCAAC;;;;;;;;;;;;;sCAI9E,6LAAC,8IAAY;;8CACX,6LAAC,wIAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,oBAAoB;wCACpB,kBAAkB;oCACpB;oCACA,UAAU;8CACX;;;;;;8CAGD,6LAAC,wIAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GAlOgB;KAAA", "debugId": null}}, {"offset": {"line": 6594, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/components/dashboard-content.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nexport function DashboardContent() {\n  return (\n    <>\n      <div className=\"grid auto-rows-min gap-4 md:grid-cols-3\">\n        <div className=\"bg-muted/50 aspect-video rounded-xl flex items-center justify-center\">\n          <p className=\"text-muted-foreground\">Dashboard Widget 1</p>\n        </div>\n        <div className=\"bg-muted/50 aspect-video rounded-xl flex items-center justify-center\">\n          <p className=\"text-muted-foreground\">Dashboard Widget 2</p>\n        </div>\n        <div className=\"bg-muted/50 aspect-video rounded-xl flex items-center justify-center\">\n          <p className=\"text-muted-foreground\">Dashboard Widget 3</p>\n        </div>\n      </div>\n      <div className=\"bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-2\">Welcome to Acunetix Desktop</h2>\n          <p className=\"text-muted-foreground\">\n            Manage your security scanning infrastructure from this centralized dashboard.\n          </p>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAIO,SAAS;IACd,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAGzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;AAO/C;KAxBgB", "debugId": null}}, {"offset": {"line": 6703, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/AcunetixDesktop/frontend/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { AppSidebar } from \"@/components/app-sidebar\"\nimport {\n  Breadcrumb,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbList,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n} from \"@/components/ui/breadcrumb\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  SidebarInset,\n  SidebarProvider,\n  SidebarTrigger,\n} from \"@/components/ui/sidebar\"\nimport { TargetsContent } from \"@/components/targets-content\"\nimport { DashboardContent } from \"@/components/dashboard-content\"\n\nexport default function Page() {\n  const [currentView, setCurrentView] = React.useState(\"dashboard\")\n\n  const getBreadcrumbItems = () => {\n    switch (currentView) {\n      case \"targets\":\n        return {\n          category: \"Security\",\n          page: \"Targets\"\n        }\n      case \"dashboard\":\n      default:\n        return {\n          category: \"Overview\",\n          page: \"Dashboard\"\n        }\n    }\n  }\n\n  const breadcrumb = getBreadcrumbItems()\n\n  const renderContent = () => {\n    switch (currentView) {\n      case \"targets\":\n        return <TargetsContent />\n      case \"dashboard\":\n      default:\n        return <DashboardContent />\n    }\n  }\n\n  return (\n    <SidebarProvider>\n      <AppSidebar onNavigate={setCurrentView} />\n      <SidebarInset>\n        <header className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12\">\n          <div className=\"flex items-center gap-2 px-4\">\n            <SidebarTrigger className=\"-ml-1\" />\n            <Separator\n              orientation=\"vertical\"\n              className=\"mr-2 data-[orientation=vertical]:h-4\"\n            />\n            <Breadcrumb>\n              <BreadcrumbList>\n                <BreadcrumbItem className=\"hidden md:block\">\n                  <BreadcrumbLink href=\"#\" onClick={() => setCurrentView(\"dashboard\")}>\n                    {breadcrumb.category}\n                  </BreadcrumbLink>\n                </BreadcrumbItem>\n                <BreadcrumbSeparator className=\"hidden md:block\" />\n                <BreadcrumbItem>\n                  <BreadcrumbPage>{breadcrumb.page}</BreadcrumbPage>\n                </BreadcrumbItem>\n              </BreadcrumbList>\n            </Breadcrumb>\n          </div>\n        </header>\n        <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\n          {renderContent()}\n        </div>\n      </SidebarInset>\n    </SidebarProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AACA;AAKA;AACA;;;AAnBA;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,yKAAc,CAAC;IAErD,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,UAAU;oBACV,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,sJAAc;;;;;YACxB,KAAK;YACL;gBACE,qBAAO,6LAAC,0JAAgB;;;;;QAC5B;IACF;IAEA,qBACE,6LAAC,kJAAe;;0BACd,6LAAC,8IAAU;gBAAC,YAAY;;;;;;0BACxB,6LAAC,+IAAY;;kCACX,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAc;oCAAC,WAAU;;;;;;8CAC1B,6LAAC,8IAAS;oCACR,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC,gJAAU;8CACT,cAAA,6LAAC,oJAAc;;0DACb,6LAAC,oJAAc;gDAAC,WAAU;0DACxB,cAAA,6LAAC,oJAAc;oDAAC,MAAK;oDAAI,SAAS,IAAM,eAAe;8DACpD,WAAW,QAAQ;;;;;;;;;;;0DAGxB,6LAAC,yJAAmB;gDAAC,WAAU;;;;;;0DAC/B,6LAAC,oJAAc;0DACb,cAAA,6LAAC,oJAAc;8DAAE,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;GA/DwB;KAAA", "debugId": null}}]}