"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { AddBackend } from "@/wailsjs/go/main/App"
import { ConnectionTestResult } from "./connection-test-result"

interface AddBackendDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onBackendAdded?: () => void
}

export function AddBackendDialog({ open, onOpenChange, onBackendAdded }: AddBackendDialogProps) {
  const [formData, setFormData] = React.useState({
    name: "",
    url: "",
    api_key: "",
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  const resetForm = () => {
    setFormData({
      name: "",
      url: "",
      api_key: "",
    })
    setErrors({})
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Backend name is required"
    }
    
    if (!formData.url.trim()) {
      newErrors.url = "Backend URL is required"
    } else {
      // Basic URL validation
      try {
        const url = formData.url.startsWith('http') ? formData.url : `https://${formData.url}`
        new URL(url)
      } catch {
        newErrors.url = "Please enter a valid URL"
      }
    }
    
    if (!formData.api_key.trim()) {
      newErrors.api_key = "API Key is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      await AddBackend(formData)
      onBackendAdded?.()
      onOpenChange(false)
      resetForm()
    } catch (error) {
      setErrors({ submit: `Failed to add backend: ${error}` })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetForm()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Backend</DialogTitle>
          <DialogDescription>
            Add a new Acunetix scanner backend. You can test the connection before saving.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Backend Name</Label>
            <Input
              id="name"
              placeholder="e.g., Production Scanner"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">Backend URL</Label>
            <Input
              id="url"
              placeholder="https://scanner.example.com:3443"
              value={formData.url}
              onChange={(e) => handleInputChange("url", e.target.value)}
              className={errors.url ? "border-red-500" : ""}
            />
            {errors.url && <p className="text-sm text-red-500">{errors.url}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="api_key">API Key</Label>
            <Input
              id="api_key"
              type="password"
              placeholder="Enter your API key"
              value={formData.api_key}
              onChange={(e) => handleInputChange("api_key", e.target.value)}
              className={errors.api_key ? "border-red-500" : ""}
            />
            {errors.api_key && <p className="text-sm text-red-500">{errors.api_key}</p>}
          </div>



          {/* Test Connection Section */}
          <ConnectionTestResult
            formData={formData}
            disabled={isLoading}
          />

          {errors.submit && (
            <p className="text-sm text-red-500">{errors.submit}</p>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading || !formData.name || !formData.url || !formData.api_key}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </>
            ) : (
              "Add Backend"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
