package main

import (
	"context"
	"fmt"
)

// App struct
type App struct {
	ctx            context.Context
	backendManager *BackendManager
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		backendManager: NewBackendManager(),
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// G<PERSON> returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

// Backend Management Methods

// GetBackends returns all backend configurations
func (a *App) GetBackends() BackendList {
	return a.backendManager.GetBackends()
}

// AddBackend adds a new backend configuration
func (a *App) AddBackend(req NewBackendRequest) (*Backend, error) {
	return a.backendManager.AddBackend(req)
}

// UpdateBackend updates an existing backend
func (a *App) UpdateBackend(id string, req NewBackendRequest) (*Backend, error) {
	return a.backendManager.UpdateBackend(id, req)
}

// DeleteBackend removes a backend configuration
func (a *App) DeleteBackend(id string) error {
	return a.backendManager.DeleteBackend(id)
}

// SetActiveBackend sets a backend as active
func (a *App) SetActiveBackend(id string) error {
	return a.backendManager.SetActiveBackend(id)
}

// GetActiveBackend returns the currently active backend
func (a *App) GetActiveBackend() (*Backend, error) {
	return a.backendManager.GetActiveBackend()
}

// TestBackendConnection tests the connection to a backend
func (a *App) TestBackendConnection(id string) (BackendConnectionTest, error) {
	backend, err := a.backendManager.GetBackend(id)
	if err != nil {
		return BackendConnectionTest{}, err
	}

	test := a.backendManager.TestBackendConnection(backend)

	// Update backend status
	a.backendManager.UpdateBackendStatus(id, test)

	return test, nil
}

// TestBackendConnectionDirect tests connection with provided credentials without saving
func (a *App) TestBackendConnectionDirect(req NewBackendRequest) BackendConnectionTest {
	// Create a temporary backend for testing
	tempBackend := &Backend{
		URL:    req.URL,
		APIKey: req.APIKey,
	}

	return a.backendManager.TestBackendConnection(tempBackend)
}

// Configuration Management Methods

// GetConfig returns the current application configuration
func (a *App) GetConfig() *Config {
	return a.backendManager.configManager.GetConfig()
}

// GetSettings returns application settings
func (a *App) GetSettings() Settings {
	return a.backendManager.configManager.GetSettings()
}

// UpdateSettings updates application settings
func (a *App) UpdateSettings(settings Settings) error {
	return a.backendManager.configManager.UpdateSettings(settings)
}

// GetConfigPath returns the path to the config file
func (a *App) GetConfigPath() string {
	return a.backendManager.configManager.GetConfigPath()
}

// GetTargets retrieves all targets from the active backend
func (a *App) GetTargets() (*TargetList, error) {
	return a.backendManager.GetTargets()
}

// AddTarget adds a new target to the active backend
func (a *App) AddTarget(req NewTargetRequest) (*TargetItemResponse, error) {
	return a.backendManager.AddTarget(req)
}

// DeleteTarget deletes a target from the active backend
func (a *App) DeleteTarget(targetID string) error {
	return a.backendManager.DeleteTarget(targetID)
}

// GetSeverityCounts returns a sample SeverityCounts for type generation
func (a *App) GetSeverityCounts() SeverityCounts {
	return SeverityCounts{}
}

// GetLink returns a sample Link for type generation
func (a *App) GetLink() Link {
	return Link{}
}

// GetScanAuthorization returns a sample ScanAuthorization for type generation
func (a *App) GetScanAuthorization() ScanAuthorization {
	return ScanAuthorization{}
}

// GetDefaultOverrides returns a sample DefaultOverrides for type generation
func (a *App) GetDefaultOverrides() DefaultOverrides {
	return DefaultOverrides{}
}

// GetTargetAgent returns a sample TargetAgent for type generation
func (a *App) GetTargetAgent() TargetAgent {
	return TargetAgent{}
}
