(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,20783,91918,48425,7670,47163,69035,e=>{"use strict";e.s(["composeRefs",()=>n,"useComposedRefs",()=>o],20783);var t=e.i(71645);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{let n=!1,o=t.map(t=>{let o=r(t,e);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let e=0;e<o.length;e++){let n=o[e];"function"==typeof n?n():r(t[e],null)}}}}function o(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return t.useCallback(n(...r),r)}e.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>p],48425);var a=e.i(74080);e.s(["Slot",()=>l,"createSlot",()=>s,"createSlottable",()=>d],91918);var i=e.i(43476);function s(e){let r=function(e){let r=t.forwardRef((e,r)=>{let{children:o,...a}=e;if(t.isValidElement(o)){var i,s,l;let e,c,d=(c=(e=null==(s=Object.getOwnPropertyDescriptor((i=o).props,"ref"))?void 0:s.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(l=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=a(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,o.props);return o.type!==t.Fragment&&(u.ref=r?n(r,d):d),t.cloneElement(o,u)}return t.Children.count(o)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),o=t.forwardRef((e,n)=>{let{children:o,...a}=e,s=t.Children.toArray(o),l=s.find(u);if(l){let e=l.props.children,o=s.map(r=>r!==l?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...a,ref:n,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,i.jsx)(r,{...a,ref:n,children:o})});return o.displayName="".concat(e,".Slot"),o}var l=s("Slot"),c=Symbol("radix.slottable");function d(e){let t=e=>{let{children:t}=e;return(0,i.jsx)(i.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=c,t}function u(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let n=s("Primitive.".concat(r)),o=t.forwardRef((e,t)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:r,{...a,ref:t})});return o.displayName="Primitive.".concat(r),{...e,[r]:o}},{});function p(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}function m(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}e.s(["clsx",()=>m],7670),e.s(["cn",()=>ep],47163);let h=(e,t)=>{var r;if(0===e.length)return t.classGroupId;let n=e[0],o=t.nextPart.get(n),a=o?h(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return null==(r=t.validators.find(e=>{let{validator:t}=e;return t(i)}))?void 0:r.classGroupId},v=/^\[(.+)\]$/,g=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:b(t,e)).classGroupId=r;return}if("function"==typeof e)return x(e)?void g(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(e=>{let[o,a]=e;g(a,b(t,o),r,n)})})},b=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},x=e=>e.isThemeGetter,y=/\s+/;function w(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=k(e))&&(n&&(n+=" "),n+=t);return n}let k=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=k(e[n]))&&(r&&(r+=" "),r+=t);return r},j=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},C=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,N=/^\((?:(\w[\w-]*):)?(.+)\)$/i,S=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,A=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,P=e=>S.test(e),T=e=>!!e&&!Number.isNaN(Number(e)),D=e=>!!e&&Number.isInteger(Number(e)),O=e=>e.endsWith("%")&&T(e.slice(0,-1)),z=e=>E.test(e),L=()=>!0,I=e=>R.test(e)&&!_.test(e),F=()=>!1,B=e=>M.test(e),H=e=>A.test(e),W=e=>!q(e)&&!J(e),V=e=>en(e,es,F),q=e=>C.test(e),K=e=>en(e,el,I),U=e=>en(e,ec,T),G=e=>en(e,ea,F),X=e=>en(e,ei,H),Y=e=>en(e,eu,B),J=e=>N.test(e),$=e=>eo(e,el),Z=e=>eo(e,ed),Q=e=>eo(e,ea),ee=e=>eo(e,es),et=e=>eo(e,ei),er=e=>eo(e,eu,!0),en=(e,t,r)=>{let n=C.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},eo=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=N.exec(e);return!!n&&(n[1]?t(n[1]):r)},ea=e=>"position"===e||"percentage"===e,ei=e=>"image"===e||"url"===e,es=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,ec=e=>"number"===e,ed=e=>"family-name"===e,eu=e=>"shadow"===e;Symbol.toStringTag;let ef=function(e){let t,r,n;for(var o=arguments.length,a=Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];let s=function(o){let i;return r=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}})((i=a.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],o=0,a=0,i=0;for(let r=0;r<e.length;r++){let s=e[r];if(0===o&&0===a){if(":"===s){n.push(e.slice(i,r)),i=r+1;continue}if("/"===s){t=r;continue}}"["===s?o++:"]"===s?o--:"("===s?a++:")"===s&&a--}let s=0===n.length?e:e.substring(i),l=(r=s).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:l!==s,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(i),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(i),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)g(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),h(r,t)||(e=>{if(v.test(e)){let t=v.exec(e)[1],r=null==t?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}})(i)}).cache.get,n=t.cache.set,s=l,l(o)};function l(e){let o=r(e);if(o)return o;let a=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],s=e.trim().split(y),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(c){l=t+(l.length>0?" "+l:l);continue}let m=!!p,h=n(m?f.substring(0,p):f);if(!h){if(!m||!(h=n(f))){l=t+(l.length>0?" "+l:l);continue}m=!1}let v=a(d).join(":"),g=u?v+"!":v,b=g+h;if(i.includes(b))continue;i.push(b);let x=o(h,m);for(let e=0;e<x.length;++e){let t=x[e];i.push(g+t)}l=t+(l.length>0?" "+l:l)}return l})(e,t);return n(e,a),a}return function(){return s(w.apply(null,arguments))}}(()=>{let e=j("color"),t=j("font"),r=j("text"),n=j("font-weight"),o=j("tracking"),a=j("leading"),i=j("breakpoint"),s=j("container"),l=j("spacing"),c=j("radius"),d=j("shadow"),u=j("inset-shadow"),f=j("text-shadow"),p=j("drop-shadow"),m=j("blur"),h=j("perspective"),v=j("aspect"),g=j("ease"),b=j("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),J,q],k=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto","contain","none"],N=()=>[J,q,l],S=()=>[P,"full","auto",...N()],E=()=>[D,"none","subgrid",J,q],R=()=>["auto",{span:["full",D,J,q]},D,J,q],_=()=>[D,"auto",J,q],M=()=>["auto","min","max","fr",J,q],A=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],F=()=>["auto",...N()],B=()=>[P,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],H=()=>[e,J,q],en=()=>[...y(),Q,G,{position:[J,q]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",ee,V,{size:[J,q]}],ei=()=>[O,$,K],es=()=>["","none","full",c,J,q],el=()=>["",T,$,K],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[T,O,Q,G],ef=()=>["","none",m,J,q],ep=()=>["none",T,J,q],em=()=>["none",T,J,q],eh=()=>[T,J,q],ev=()=>[P,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[L],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",T],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",P,q,J,v]}],container:["container"],columns:[{columns:[T,q,J,s]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",J,q]}],basis:[{basis:[P,"full","auto",s,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[T,P,"auto","initial","none",q]}],grow:[{grow:["",T,J,q]}],shrink:[{shrink:["",T,J,q]}],order:[{order:[D,"first","last","none",J,q]}],"grid-cols":[{"grid-cols":E()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":E()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...A(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...A()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":A()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:F()}],mx:[{mx:F()}],my:[{my:F()}],ms:[{ms:F()}],me:[{me:F()}],mt:[{mt:F()}],mr:[{mr:F()}],mb:[{mb:F()}],ml:[{ml:F()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:B()}],w:[{w:[s,"screen",...B()]}],"min-w":[{"min-w":[s,"screen","none",...B()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[i]},...B()]}],h:[{h:["screen","lh",...B()]}],"min-h":[{"min-h":["screen","lh","none",...B()]}],"max-h":[{"max-h":["screen","lh",...B()]}],"font-size":[{text:["base",r,$,K]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,J,U]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",O,q]}],"font-family":[{font:[Z,q,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,J,q]}],"line-clamp":[{"line-clamp":[T,"none",J,U]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",J,q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",J,q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:H()}],"text-color":[{text:H()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[T,"from-font","auto",J,K]}],"text-decoration-color":[{decoration:H()}],"underline-offset":[{"underline-offset":[T,"auto",J,q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J,q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J,q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,J,q],radial:["",J,q],conic:[D,J,q]},et,X]}],"bg-color":[{bg:H()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:H()}],"gradient-via":[{via:H()}],"gradient-to":[{to:H()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:H()}],"border-color-x":[{"border-x":H()}],"border-color-y":[{"border-y":H()}],"border-color-s":[{"border-s":H()}],"border-color-e":[{"border-e":H()}],"border-color-t":[{"border-t":H()}],"border-color-r":[{"border-r":H()}],"border-color-b":[{"border-b":H()}],"border-color-l":[{"border-l":H()}],"divide-color":[{divide:H()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[T,J,q]}],"outline-w":[{outline:["",T,$,K]}],"outline-color":[{outline:H()}],shadow:[{shadow:["","none",d,er,Y]}],"shadow-color":[{shadow:H()}],"inset-shadow":[{"inset-shadow":["none",u,er,Y]}],"inset-shadow-color":[{"inset-shadow":H()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:H()}],"ring-offset-w":[{"ring-offset":[T,K]}],"ring-offset-color":[{"ring-offset":H()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":H()}],"text-shadow":[{"text-shadow":["none",f,er,Y]}],"text-shadow-color":[{"text-shadow":H()}],opacity:[{opacity:[T,J,q]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[T]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":H()}],"mask-image-linear-to-color":[{"mask-linear-to":H()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":H()}],"mask-image-t-to-color":[{"mask-t-to":H()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":H()}],"mask-image-r-to-color":[{"mask-r-to":H()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":H()}],"mask-image-b-to-color":[{"mask-b-to":H()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":H()}],"mask-image-l-to-color":[{"mask-l-to":H()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":H()}],"mask-image-x-to-color":[{"mask-x-to":H()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":H()}],"mask-image-y-to-color":[{"mask-y-to":H()}],"mask-image-radial":[{"mask-radial":[J,q]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":H()}],"mask-image-radial-to-color":[{"mask-radial-to":H()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[T]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":H()}],"mask-image-conic-to-color":[{"mask-conic-to":H()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",J,q]}],filter:[{filter:["","none",J,q]}],blur:[{blur:ef()}],brightness:[{brightness:[T,J,q]}],contrast:[{contrast:[T,J,q]}],"drop-shadow":[{"drop-shadow":["","none",p,er,Y]}],"drop-shadow-color":[{"drop-shadow":H()}],grayscale:[{grayscale:["",T,J,q]}],"hue-rotate":[{"hue-rotate":[T,J,q]}],invert:[{invert:["",T,J,q]}],saturate:[{saturate:[T,J,q]}],sepia:[{sepia:["",T,J,q]}],"backdrop-filter":[{"backdrop-filter":["","none",J,q]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[T,J,q]}],"backdrop-contrast":[{"backdrop-contrast":[T,J,q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",T,J,q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[T,J,q]}],"backdrop-invert":[{"backdrop-invert":["",T,J,q]}],"backdrop-opacity":[{"backdrop-opacity":[T,J,q]}],"backdrop-saturate":[{"backdrop-saturate":[T,J,q]}],"backdrop-sepia":[{"backdrop-sepia":["",T,J,q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",J,q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[T,"initial",J,q]}],ease:[{ease:["linear","initial",g,J,q]}],delay:[{delay:[T,J,q]}],animate:[{animate:["none",b,J,q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,J,q]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[J,q,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:H()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:H()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J,q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J,q]}],fill:[{fill:["none",...H()]}],"stroke-w":[{stroke:[T,$,K,U]}],stroke:[{stroke:["none",...H()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ep(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return ef(m(t))}e.s(["Separator",()=>eg],69035);var em="horizontal",eh=["horizontal","vertical"],ev=t.forwardRef((e,t)=>{var r;let{decorative:n,orientation:o=em,...a}=e,s=(r=o,eh.includes(r))?o:em;return(0,i.jsx)(f.div,{"data-orientation":s,...n?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...a,ref:t})});function eg(e){let{className:t,orientation:r="horizontal",decorative:n=!0,...o}=e;return(0,i.jsx)(ev,{"data-slot":"separator",decorative:n,orientation:r,className:ep("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}ev.displayName="Separator"},75254,81140,30030,34620,69340,96626,10772,25913,67881,23750,30207,26330,65491,74606,3536,85369,86312,26999,95926,71428,53660,59411,11522,e=>{"use strict";e.s(["default",()=>d],75254);var t,r,n,o,a=e.i(71645);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:r,strokeWidth:i?24*Number(o)/Number(n):o,className:s("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:o,...l}=r;return(0,a.createElement)(c,{ref:n,iconNode:t,className:s("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...l})});return r.displayName=i(e),r};function u(e,t){let{checkForDefaultPrevented:r=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(n){if(null==e||e(n),!1===r||!n.defaultPrevented)return null==t?void 0:t(n)}}e.s(["composeEventHandlers",()=>u],81140),"undefined"!=typeof window&&window.document&&window.document.createElement,e.s(["createContext",()=>p,"createContextScope",()=>m],30030);var f=e.i(43476);function p(e,t){let r=a.createContext(t),n=e=>{let{children:t,...n}=e,o=a.useMemo(()=>n,Object.values(n));return(0,f.jsx)(r.Provider,{value:o,children:t})};return n.displayName=e+"Provider",[n,function(n){let o=a.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error("`".concat(n,"` must be used within `").concat(e,"`"))}]}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=[],n=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=(null==r?void 0:r[e])||t;return a.useMemo(()=>({["__scope".concat(e)]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=a.createContext(n),i=r.length;r=[...r,n];let s=t=>{var r;let{scope:n,children:s,...l}=t,c=(null==n||null==(r=n[e])?void 0:r[i])||o,d=a.useMemo(()=>l,Object.values(l));return(0,f.jsx)(c.Provider,{value:d,children:s})};return s.displayName=t+"Provider",[s,function(r,s){var l;let c=(null==s||null==(l=s[e])?void 0:l[i])||o,d=a.useContext(c);if(d)return d;if(void 0!==n)return n;throw Error("`".concat(r,"` must be used within `").concat(t,"`"))}]},function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=t[0];if(1===t.length)return n;let o=()=>{let e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){let r=e.reduce((e,r)=>{let{useScope:n,scopeName:o}=r,a=n(t)["__scope".concat(o)];return{...e,...a}},{});return a.useMemo(()=>({["__scope".concat(n.scopeName)]:r}),[r])}};return o.scopeName=n.scopeName,o}(n,...t)]}e.s(["useControllableState",()=>g],69340),e.s(["useLayoutEffect",()=>h],34620);var h=(null==(r=globalThis)?void 0:r.document)?a.useLayoutEffect:()=>{};a[" useEffectEvent ".trim().toString()],a[" useInsertionEffect ".trim().toString()];var v=a[" useInsertionEffect ".trim().toString()]||h;function g(e){let{prop:t,defaultProp:r,onChange:n=()=>{},caller:o}=e,[i,s,l]=function(e){let{defaultProp:t,onChange:r}=e,[n,o]=a.useState(t),i=a.useRef(n),s=a.useRef(r);return v(()=>{s.current=r},[r]),a.useEffect(()=>{if(i.current!==n){var e;null==(e=s.current)||e.call(s,n),i.current=n}},[n,i]),[n,o,s]}({defaultProp:r,onChange:n}),c=void 0!==t,d=c?t:i;{let e=a.useRef(void 0!==t);a.useEffect(()=>{let t=e.current;if(t!==c){let e=c?"controlled":"uncontrolled";console.warn("".concat(o," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=c},[c,o])}return[d,a.useCallback(e=>{if(c){let n="function"==typeof e?e(t):e;if(n!==t){var r;null==(r=l.current)||r.call(l,n)}}else s(e)},[c,t,s,l])]}Symbol("RADIX:SYNC_STATE"),e.s(["Presence",()=>x],96626);var b=e.i(20783),x=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=a.useState(),i=a.useRef(null),s=a.useRef(e),l=a.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return a.useEffect(()=>{let e=y(i.current);l.current="mounted"===c?e:"none"},[c]),h(()=>{let t=i.current,r=s.current;if(r!==e){let n=l.current,o=y(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),h(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=y(i.current).includes(CSS.escape(e.animationName));if(e.target===n&&o&&(d("ANIMATION_END"),!s.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},a=e=>{e.target===n&&(l.current=y(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):a.Children.only(r),i=(0,b.useComposedRefs)(n.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?a.cloneElement(o,{ref:i}):null};function y(e){return(null==e?void 0:e.animationName)||"none"}x.displayName="Presence",e.s(["useId",()=>j],10772);var w=a[" useId ".trim().toString()]||(()=>void 0),k=0;function j(e){let[t,r]=a.useState(w());return h(()=>{e||r(e=>null!=e?e:String(k++))},[e]),e||(t?"radix-".concat(t):"")}e.s(["Sidebar",()=>nK,"SidebarContent",()=>nQ,"SidebarFooter",()=>n$,"SidebarGroup",()=>n0,"SidebarGroupAction",()=>n2,"SidebarGroupContent",()=>n3,"SidebarGroupLabel",()=>n1,"SidebarHeader",()=>nJ,"SidebarInput",()=>nY,"SidebarInset",()=>nX,"SidebarMenu",()=>n5,"SidebarMenuAction",()=>n7,"SidebarMenuBadge",()=>n9,"SidebarMenuButton",()=>n8,"SidebarMenuItem",()=>n4,"SidebarMenuSkeleton",()=>oe,"SidebarMenuSub",()=>ot,"SidebarMenuSubButton",()=>on,"SidebarMenuSubItem",()=>or,"SidebarProvider",()=>nq,"SidebarRail",()=>nG,"SidebarSeparator",()=>nZ,"SidebarTrigger",()=>nU,"useSidebar",()=>nV],11522);var C=e.i(91918);e.s(["cva",()=>R],25913);var N=e.i(7670);let S=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,E=N.clsx,R=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return E(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=t,i=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=S(t)||S(n);return o[e][i]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return E(e,i,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...s}[t]):({...a,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)},_=d("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var M=e.i(47163);e.s(["Button",()=>P],67881);let A=R("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function P(e){let{className:t,variant:r,size:n,asChild:o=!1,...a}=e,i=o?C.Slot:"button";return(0,f.jsx)(i,{"data-slot":"button",className:(0,M.cn)(A({variant:r,size:n,className:t})),...a})}function T(e){let{className:t,type:r,...n}=e;return(0,f.jsx)("input",{type:r,"data-slot":"input",className:(0,M.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}e.s(["Input",()=>T],23750);var D=e.i(69035);e.s(["Close",()=>tS,"Content",()=>tj,"Description",()=>tN,"Overlay",()=>tk,"Portal",()=>tw,"Root",()=>tx,"Title",()=>tC,"Trigger",()=>ty],26999),e.s(["DismissableLayer",()=>F],26330);var O=e.i(48425);function z(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>function(){for(var e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return null==(e=t.current)?void 0:e.call(t,...n)},[])}e.s(["useCallbackRef",()=>z],30207);var L="dismissableLayer.update",I=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),F=a.forwardRef((e,t)=>{var r,o;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:c,onInteractOutside:d,onDismiss:p,...m}=e,h=a.useContext(I),[v,g]=a.useState(null),x=null!=(o=null==v?void 0:v.ownerDocument)?o:null==(r=globalThis)?void 0:r.document,[,y]=a.useState({}),w=(0,b.useComposedRefs)(t,e=>g(e)),k=Array.from(h.layers),[j]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),C=k.indexOf(j),N=v?k.indexOf(v):-1,S=h.layersWithOutsidePointerEventsDisabled.size>0,E=N>=C,R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=z(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){H("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...h.branches].some(e=>e.contains(t));E&&!r&&(null==l||l(e),null==d||d(e),e.defaultPrevented||null==p||p())},x),_=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=z(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&H("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==c||c(e),null==d||d(e),e.defaultPrevented||null==p||p())},x);return!function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=z(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return r.addEventListener("keydown",e,{capture:!0}),()=>r.removeEventListener("keydown",e,{capture:!0})},[n,r])}(e=>{N===h.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},x),a.useEffect(()=>{if(v)return i&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(n=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(v)),h.layers.add(v),B(),()=>{i&&1===h.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=n)}},[v,x,i,h]),a.useEffect(()=>()=>{v&&(h.layers.delete(v),h.layersWithOutsidePointerEventsDisabled.delete(v),B())},[v,h]),a.useEffect(()=>{let e=()=>y({});return document.addEventListener(L,e),()=>document.removeEventListener(L,e)},[]),(0,f.jsx)(O.Primitive.div,{...m,ref:w,style:{pointerEvents:S?E?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,_.onFocusCapture),onBlurCapture:u(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,R.onPointerDownCapture)})});function B(){let e=new CustomEvent(L);document.dispatchEvent(e)}function H(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,O.dispatchDiscreteCustomEvent)(a,i):a.dispatchEvent(i)}F.displayName="DismissableLayer",a.forwardRef((e,t)=>{let r=a.useContext(I),n=a.useRef(null),o=(0,b.useComposedRefs)(t,n);return a.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,f.jsx)(O.Primitive.div,{...e,ref:o})}).displayName="DismissableLayerBranch",e.s(["FocusScope",()=>K],65491);var W="focusScope.autoFocusOnMount",V="focusScope.autoFocusOnUnmount",q={bubbles:!1,cancelable:!0},K=a.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[l,c]=a.useState(null),d=z(o),u=z(i),p=a.useRef(null),m=(0,b.useComposedRefs)(t,e=>c(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(n){let e=function(e){if(h.paused||!l)return;let t=e.target;l.contains(t)?p.current=t:X(p.current,{select:!0})},t=function(e){if(h.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||X(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&X(l)});return l&&r.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,l,h.paused]),a.useEffect(()=>{if(l){Y.add(h);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(W,q);l.addEventListener(W,d),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(X(n,{select:t}),document.activeElement!==r)return}(U(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&X(l))}return()=>{l.removeEventListener(W,d),setTimeout(()=>{let t=new CustomEvent(V,q);l.addEventListener(V,u),l.dispatchEvent(t),t.defaultPrevented||X(null!=e?e:document.body,{select:!0}),l.removeEventListener(V,u),Y.remove(h)},0)}}},[l,d,u,h]);let v=a.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=U(e);return[G(t,e),G(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&X(a,{select:!0})):(e.preventDefault(),r&&X(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,f.jsx)(O.Primitive.div,{tabIndex:-1,...s,ref:m,onKeyDown:v})});function U(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function G(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function X(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}K.displayName="FocusScope";var Y=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=J(e,t)).unshift(t)},remove(t){var r;null==(r=(e=J(e,t))[0])||r.resume()}}}();function J(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}e.s(["Portal",()=>Z],74606);var $=e.i(74080),Z=a.forwardRef((e,t)=>{var r,n;let{container:o,...i}=e,[s,l]=a.useState(!1);h(()=>l(!0),[]);let c=o||s&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return c?$.default.createPortal((0,f.jsx)(O.Primitive.div,{...i,ref:t}),c):null});Z.displayName="Portal",e.s(["useFocusGuards",()=>ee],3536);var Q=0;function ee(){a.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:et()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:et()),Q++,()=>{1===Q&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),Q--}},[])}function et(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}e.s(["RemoveScroll",()=>eW],85369);var er=function(){return(er=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function en(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var eo=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),ea="width-before-scroll-bar";function ei(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var es="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,el=new WeakMap;function ec(e){return e}var ed=function(e){void 0===e&&(e={});var t,r,n,o=(void 0===t&&(t=ec),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return o.options=er({async:!0,ssr:!1},e),o}(),eu=function(){},ef=a.forwardRef(function(e,t){var r,n,o,i,s=a.useRef(null),l=a.useState({onScrollCapture:eu,onWheelCapture:eu,onTouchMoveCapture:eu}),c=l[0],d=l[1],u=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,v=e.shards,g=e.sideCar,b=e.noRelative,x=e.noIsolation,y=e.inert,w=e.allowPinchZoom,k=e.as,j=e.gapMode,C=en(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(r=[s,t],n=function(e){return r.forEach(function(t){return ei(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,i=o.facade,es(function(){var e=el.get(i);if(e){var t=new Set(e),n=new Set(r),o=i.current;t.forEach(function(e){n.has(e)||ei(e,null)}),n.forEach(function(e){t.has(e)||ei(e,o)})}el.set(i,r)},[r]),i),S=er(er({},C),c);return a.createElement(a.Fragment,null,h&&a.createElement(g,{sideCar:ed,removeScrollBar:m,shards:v,noRelative:b,noIsolation:x,inert:y,setCallbacks:d,allowPinchZoom:!!w,lockRef:s,gapMode:j}),u?a.cloneElement(a.Children.only(f),er(er({},S),{ref:N})):a.createElement(void 0===k?"div":k,er({},S,{className:p,ref:N}),f))});ef.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ef.classNames={fullWidth:ea,zeroRight:eo};var ep=function(e){var t=e.sideCar,r=en(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,er({},r))};ep.isSideCarExport=!0;var em=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return t&&e.setAttribute("nonce",t),e}())){var n,a;(n=t).styleSheet?n.styleSheet.cssText=r:n.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},eh=function(){var e=em();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},ev=function(){var e=eh();return function(t){return e(t.styles,t.dynamic),null}},eg={left:0,top:0,right:0,gap:0},eb=function(e){return parseInt(e||"",10)||0},ex=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[eb(r),eb(n),eb(o)]},ey=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eg;var t=ex(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},ew=ev(),ek="data-scroll-locked",ej=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(ek,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(eo," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(ea," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(eo," .").concat(eo," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(ea," .").concat(ea," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(ek,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},eC=function(){var e=parseInt(document.body.getAttribute(ek)||"0",10);return isFinite(e)?e:0},eN=function(){a.useEffect(function(){return document.body.setAttribute(ek,(eC()+1).toString()),function(){var e=eC()-1;e<=0?document.body.removeAttribute(ek):document.body.setAttribute(ek,e.toString())}},[])},eS=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;eN();var i=a.useMemo(function(){return ey(o)},[o]);return a.createElement(ew,{styles:ej(i,!t,o,r?"":"!important")})},eE=!1;if("undefined"!=typeof window)try{var eR=Object.defineProperty({},"passive",{get:function(){return eE=!0,!0}});window.addEventListener("test",eR,eR),window.removeEventListener("test",eR,eR)}catch(e){eE=!1}var e_=!!eE&&{passive:!1},eM=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},eA=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),eP(e,n)){var o=eT(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body)return!1},eP=function(e,t){return"v"===e?eM(t,"overflowY"):eM(t,"overflowX")},eT=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eD=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),s=i*n,l=r.target,c=t.contains(l),d=!1,u=s>0,f=0,p=0;do{if(!l)break;var m=eT(e,l),h=m[0],v=m[1]-m[2]-i*h;(h||v)&&eP(e,l)&&(f+=v,p+=h);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l))return u&&(o&&1>Math.abs(f)||!o&&s>f)?d=!0:!u&&(o&&1>Math.abs(p)||!o&&-s>p)&&(d=!0),d},eO=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ez=function(e){return[e.deltaX,e.deltaY]},eL=function(e){return e&&"current"in e?e.current:e},eI=0,eF=[];let eB=(t=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(eI++)[0],i=a.useState(ev)[0],s=a.useRef(e);a.useEffect(function(){s.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eL),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var o,a=eO(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,u=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=eA(u,d);if(!f)return!0;if(f?o=u:(o="v"===u?"h":"v",f=eA(u,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||c)&&(n.current=o),!o)return!0;var p=n.current||o;return eD(p,t,e,"h"===p?l:c,!0)},[]),c=a.useCallback(function(e){if(eF.length&&eF[eF.length-1]===i){var r="deltaY"in e?ez(e):eO(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(s.current.shards||[]).map(eL).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=a.useCallback(function(e){r.current=eO(e),n.current=void 0},[]),f=a.useCallback(function(t){d(t.type,ez(t),t.target,l(t,e.lockRef.current))},[]),p=a.useCallback(function(t){d(t.type,eO(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return eF.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,e_),document.addEventListener("touchmove",c,e_),document.addEventListener("touchstart",u,e_),function(){eF=eF.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,e_),document.removeEventListener("touchmove",c,e_),document.removeEventListener("touchstart",u,e_)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(eS,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},ed.useMedium(t),ep);var eH=a.forwardRef(function(e,t){return a.createElement(ef,er({},e,{ref:t,sideCar:eB}))});eH.classNames=ef.classNames;let eW=eH;e.s(["hideOthers",()=>eY],86312);var eV=new WeakMap,eq=new WeakMap,eK={},eU=0,eG=function(e){return e&&(e.host||eG(e.parentNode))},eX=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=eG(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eK[r]||(eK[r]=new WeakMap);var a=eK[r],i=[],s=new Set,l=new Set(o),c=function(e){!e||s.has(e)||(s.add(e),c(e.parentNode))};o.forEach(c);var d=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(s.has(e))d(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,l=(eV.get(e)||0)+1,c=(a.get(e)||0)+1;eV.set(e,l),a.set(e,c),i.push(e),1===l&&o&&eq.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),s.clear(),eU++,function(){i.forEach(function(e){var t=eV.get(e)-1,o=a.get(e)-1;eV.set(e,t),a.set(e,o),t||(eq.has(e)||e.removeAttribute(n),eq.delete(e)),o||e.removeAttribute(r)}),--eU||(eV=new WeakMap,eV=new WeakMap,eq=new WeakMap,eK={})}},eY=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),eX(n,o,r,"aria-hidden")):function(){return null}},eJ="Dialog",[e$,eZ]=m(eJ),[eQ,e0]=e$(eJ),e1=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,l=a.useRef(null),c=a.useRef(null),[d,u]=g({prop:n,defaultProp:null!=o&&o,onChange:i,caller:eJ});return(0,f.jsx)(eQ,{scope:t,triggerRef:l,contentRef:c,contentId:j(),titleId:j(),descriptionId:j(),open:d,onOpenChange:u,onOpenToggle:a.useCallback(()=>u(e=>!e),[u]),modal:s,children:r})};e1.displayName=eJ;var e2="DialogTrigger",e3=a.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=e0(e2,r),a=(0,b.useComposedRefs)(t,o.triggerRef);return(0,f.jsx)(O.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":tp(o.open),...n,ref:a,onClick:u(e.onClick,o.onOpenToggle)})});e3.displayName=e2;var e5="DialogPortal",[e4,e6]=e$(e5,{forceMount:void 0}),e8=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,i=e0(e5,t);return(0,f.jsx)(e4,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,f.jsx)(x,{present:r||i.open,children:(0,f.jsx)(Z,{asChild:!0,container:o,children:e})}))})};e8.displayName=e5;var e7="DialogOverlay",e9=a.forwardRef((e,t)=>{let r=e6(e7,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=e0(e7,e.__scopeDialog);return a.modal?(0,f.jsx)(x,{present:n||a.open,children:(0,f.jsx)(tt,{...o,ref:t})}):null});e9.displayName=e7;var te=(0,C.createSlot)("DialogOverlay.RemoveScroll"),tt=a.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=e0(e7,r);return(0,f.jsx)(eW,{as:te,allowPinchZoom:!0,shards:[o.contentRef],children:(0,f.jsx)(O.Primitive.div,{"data-state":tp(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),tr="DialogContent",tn=a.forwardRef((e,t)=>{let r=e6(tr,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=e0(tr,e.__scopeDialog);return(0,f.jsx)(x,{present:n||a.open,children:a.modal?(0,f.jsx)(to,{...o,ref:t}):(0,f.jsx)(ta,{...o,ref:t})})});tn.displayName=tr;var to=a.forwardRef((e,t)=>{let r=e0(tr,e.__scopeDialog),n=a.useRef(null),o=(0,b.useComposedRefs)(t,r.contentRef,n);return a.useEffect(()=>{let e=n.current;if(e)return eY(e)},[]),(0,f.jsx)(ti,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:u(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:u(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:u(e.onFocusOutside,e=>e.preventDefault())})}),ta=a.forwardRef((e,t)=>{let r=e0(tr,e.__scopeDialog),n=a.useRef(!1),o=a.useRef(!1);return(0,f.jsx)(ti,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(n.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),ti=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,l=e0(tr,r),c=a.useRef(null),d=(0,b.useComposedRefs)(t,c);return ee(),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(K,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,f.jsx)(F,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":tp(l.open),...s,ref:d,onDismiss:()=>l.onOpenChange(!1)})}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(tg,{titleId:l.titleId}),(0,f.jsx)(tb,{contentRef:c,descriptionId:l.descriptionId})]})]})}),ts="DialogTitle",tl=a.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=e0(ts,r);return(0,f.jsx)(O.Primitive.h2,{id:o.titleId,...n,ref:t})});tl.displayName=ts;var tc="DialogDescription",td=a.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=e0(tc,r);return(0,f.jsx)(O.Primitive.p,{id:o.descriptionId,...n,ref:t})});td.displayName=tc;var tu="DialogClose",tf=a.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=e0(tu,r);return(0,f.jsx)(O.Primitive.button,{type:"button",...n,ref:t,onClick:u(e.onClick,()=>o.onOpenChange(!1))})});function tp(e){return e?"open":"closed"}tf.displayName=tu;var tm="DialogTitleWarning",[th,tv]=p(tm,{contentName:tr,titleName:ts,docsSlug:"dialog"}),tg=e=>{let{titleId:t}=e,r=tv(tm),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},tb=e=>{let{contentRef:t,descriptionId:r}=e,n=tv("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},tx=e1,ty=e3,tw=e8,tk=e9,tj=tn,tC=tl,tN=td,tS=tf;e.s(["XIcon",()=>tE],95926);let tE=d("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function tR(e){let{...t}=e;return(0,f.jsx)(tx,{"data-slot":"sheet",...t})}function t_(e){let{...t}=e;return(0,f.jsx)(tw,{"data-slot":"sheet-portal",...t})}function tM(e){let{className:t,...r}=e;return(0,f.jsx)(tk,{"data-slot":"sheet-overlay",className:(0,M.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function tA(e){let{className:t,children:r,side:n="right",...o}=e;return(0,f.jsxs)(t_,{children:[(0,f.jsx)(tM,{}),(0,f.jsxs)(tj,{"data-slot":"sheet-content",className:(0,M.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...o,children:[r,(0,f.jsxs)(tS,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,f.jsx)(tE,{className:"size-4"}),(0,f.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function tP(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sheet-header",className:(0,M.cn)("flex flex-col gap-1.5 p-4",t),...r})}function tT(e){let{className:t,...r}=e;return(0,f.jsx)(tC,{"data-slot":"sheet-title",className:(0,M.cn)("text-foreground font-semibold",t),...r})}function tD(e){let{className:t,...r}=e;return(0,f.jsx)(tN,{"data-slot":"sheet-description",className:(0,M.cn)("text-muted-foreground text-sm",t),...r})}function tO(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"skeleton",className:(0,M.cn)("bg-accent animate-pulse rounded-md",t),...r})}e.s(["Skeleton",()=>tO],71428),e.s(["Anchor",()=>na,"Arrow",()=>ns,"Content",()=>ni,"Root",()=>no,"createPopperScope",()=>rZ],53660);let tz=["top","right","bottom","left"],tL=Math.min,tI=Math.max,tF=Math.round,tB=Math.floor,tH=e=>({x:e,y:e}),tW={left:"right",right:"left",bottom:"top",top:"bottom"},tV={start:"end",end:"start"};function tq(e,t){return"function"==typeof e?e(t):e}function tK(e){return e.split("-")[0]}function tU(e){return e.split("-")[1]}function tG(e){return"x"===e?"y":"x"}function tX(e){return"y"===e?"height":"width"}let tY=new Set(["top","bottom"]);function tJ(e){return tY.has(tK(e))?"y":"x"}function t$(e){return e.replace(/start|end/g,e=>tV[e])}let tZ=["left","right"],tQ=["right","left"],t0=["top","bottom"],t1=["bottom","top"];function t2(e){return e.replace(/left|right|bottom|top/g,e=>tW[e])}function t3(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function t5(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function t4(e,t,r){let n,{reference:o,floating:a}=e,i=tJ(t),s=tG(tJ(t)),l=tX(s),c=tK(t),d="y"===i,u=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,p=o[l]/2-a[l]/2;switch(c){case"top":n={x:u,y:o.y-a.height};break;case"bottom":n={x:u,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(tU(t)){case"start":n[s]-=p*(r&&d?-1:1);break;case"end":n[s]+=p*(r&&d?-1:1)}return n}let t6=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:i}=r,s=a.filter(Boolean),l=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=t4(c,n,l),f=n,p={},m=0;for(let r=0;r<s.length;r++){let{name:a,fn:h}=s[r],{x:v,y:g,data:b,reset:x}=await h({x:d,y:u,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=v?v:d,u=null!=g?g:u,p={...p,[a]:{...p[a],...b}},x&&m<=50&&(m++,"object"==typeof x&&(x.placement&&(f=x.placement),x.rects&&(c=!0===x.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:d,y:u}=t4(c,f,l)),r=-1)}return{x:d,y:u,placement:f,strategy:o,middlewareData:p}};async function t8(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:i,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=tq(t,e),m=t3(p),h=s[f?"floating"===u?"reference":"floating":u],v=t5(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(h)))||r?h:h.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:l})),g="floating"===u?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),x=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},y=t5(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:b,strategy:l}):g);return{top:(v.top-y.top+m.top)/x.y,bottom:(y.bottom-v.bottom+m.bottom)/x.y,left:(v.left-y.left+m.left)/x.x,right:(y.right-v.right+m.right)/x.x}}function t7(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function t9(e){return tz.some(t=>e[t]>=0)}let re=new Set(["left","top"]);async function rt(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=tK(r),s=tU(r),l="y"===tJ(r),c=re.has(i)?-1:1,d=a&&l?-1:1,u=tq(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),l?{x:p*d,y:f*c}:{x:f*c,y:p*d}}function rr(){return"undefined"!=typeof window}function rn(e){return ri(e)?(e.nodeName||"").toLowerCase():"#document"}function ro(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ra(e){var t;return null==(t=(ri(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ri(e){return!!rr()&&(e instanceof Node||e instanceof ro(e).Node)}function rs(e){return!!rr()&&(e instanceof Element||e instanceof ro(e).Element)}function rl(e){return!!rr()&&(e instanceof HTMLElement||e instanceof ro(e).HTMLElement)}function rc(e){return!!rr()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ro(e).ShadowRoot)}let rd=new Set(["inline","contents"]);function ru(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=rk(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!rd.has(o)}let rf=new Set(["table","td","th"]),rp=[":popover-open",":modal"];function rm(e){return rp.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let rh=["transform","translate","scale","rotate","perspective"],rv=["transform","translate","scale","rotate","perspective","filter"],rg=["paint","layout","strict","content"];function rb(e){let t=rx(),r=rs(e)?rk(e):e;return rh.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||rv.some(e=>(r.willChange||"").includes(e))||rg.some(e=>(r.contain||"").includes(e))}function rx(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ry=new Set(["html","body","#document"]);function rw(e){return ry.has(rn(e))}function rk(e){return ro(e).getComputedStyle(e)}function rj(e){return rs(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function rC(e){if("html"===rn(e))return e;let t=e.assignedSlot||e.parentNode||rc(e)&&e.host||ra(e);return rc(t)?t.host:t}function rN(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=rC(t);return rw(r)?t.ownerDocument?t.ownerDocument.body:t.body:rl(r)&&ru(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),i=ro(o);if(a){let e=rS(i);return t.concat(i,i.visualViewport||[],ru(o)?o:[],e&&r?rN(e):[])}return t.concat(o,rN(o,[],r))}function rS(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function rE(e){let t=rk(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=rl(e),a=o?e.offsetWidth:r,i=o?e.offsetHeight:n,s=tF(r)!==a||tF(n)!==i;return s&&(r=a,n=i),{width:r,height:n,$:s}}function rR(e){return rs(e)?e:e.contextElement}function r_(e){let t=rR(e);if(!rl(t))return tH(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=rE(t),i=(a?tF(r.width):r.width)/n,s=(a?tF(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let rM=tH(0);function rA(e){let t=ro(e);return rx()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:rM}function rP(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),i=rR(e),s=tH(1);t&&(n?rs(n)&&(s=r_(n)):s=r_(e));let l=(void 0===(o=r)&&(o=!1),n&&(!o||n===ro(i))&&o)?rA(i):tH(0),c=(a.left+l.x)/s.x,d=(a.top+l.y)/s.y,u=a.width/s.x,f=a.height/s.y;if(i){let e=ro(i),t=n&&rs(n)?ro(n):n,r=e,o=rS(r);for(;o&&n&&t!==r;){let e=r_(o),t=o.getBoundingClientRect(),n=rk(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,u*=e.x,f*=e.y,c+=a,d+=i,o=rS(r=ro(o))}}return t5({width:u,height:f,x:c,y:d})}function rT(e,t){let r=rj(e).scrollLeft;return t?t.left+r:rP(ra(e)).left+r}function rD(e,t){let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-rT(e,r),y:r.top+t.scrollTop}}let rO=new Set(["absolute","fixed"]);function rz(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ro(e),n=ra(e),o=r.visualViewport,a=n.clientWidth,i=n.clientHeight,s=0,l=0;if(o){a=o.width,i=o.height;let e=rx();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}let c=rT(n);if(c<=0){let e=n.ownerDocument,t=e.body,r=getComputedStyle(t),o="CSS1Compat"===e.compatMode&&parseFloat(r.marginLeft)+parseFloat(r.marginRight)||0,i=Math.abs(n.clientWidth-t.clientWidth-o);i<=25&&(a-=i)}else c<=25&&(a+=c);return{width:a,height:i,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=ra(e),r=rj(e),n=e.ownerDocument.body,o=tI(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=tI(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+rT(e),s=-r.scrollTop;return"rtl"===rk(n).direction&&(i+=tI(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:i,y:s}}(ra(e));else if(rs(t))n=function(e,t){let r=rP(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=rl(e)?r_(e):tH(1),i=e.clientWidth*a.x,s=e.clientHeight*a.y;return{width:i,height:s,x:o*a.x,y:n*a.y}}(t,r);else{let r=rA(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return t5(n)}function rL(e){return"static"===rk(e).position}function rI(e,t){if(!rl(e)||"fixed"===rk(e).position)return null;if(t)return t(e);let r=e.offsetParent;return ra(e)===r&&(r=r.ownerDocument.body),r}function rF(e,t){var r;let n=ro(e);if(rm(e))return n;if(!rl(e)){let t=rC(e);for(;t&&!rw(t);){if(rs(t)&&!rL(t))return t;t=rC(t)}return n}let o=rI(e,t);for(;o&&(r=o,rf.has(rn(r)))&&rL(o);)o=rI(o,t);return o&&rw(o)&&rL(o)&&!rb(o)?n:o||function(e){let t=rC(e);for(;rl(t)&&!rw(t);){if(rb(t))return t;if(rm(t))break;t=rC(t)}return null}(e)||n}let rB=async function(e){let t=this.getOffsetParent||rF,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=rl(t),o=ra(t),a="fixed"===r,i=rP(e,!0,a,t),s={scrollLeft:0,scrollTop:0},l=tH(0);if(n||!n&&!a)if(("body"!==rn(t)||ru(o))&&(s=rj(t)),n){let e=rP(t,!0,a,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=rT(o));a&&!n&&o&&(l.x=rT(o));let c=!o||n||a?tH(0):rD(o,s);return{x:i.left+s.scrollLeft-l.x-c.x,y:i.top+s.scrollTop-l.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},rH={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,i=ra(n),s=!!t&&rm(t.floating);if(n===i||s&&a)return r;let l={scrollLeft:0,scrollTop:0},c=tH(1),d=tH(0),u=rl(n);if((u||!u&&!a)&&(("body"!==rn(n)||ru(i))&&(l=rj(n)),rl(n))){let e=rP(n);c=r_(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let f=!i||u||a?tH(0):rD(i,l);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:r.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:ra,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?rm(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=rN(e,[],!1).filter(e=>rs(e)&&"body"!==rn(e)),o=null,a="fixed"===rk(e).position,i=a?rC(e):e;for(;rs(i)&&!rw(i);){let t=rk(i),r=rb(i);r||"fixed"!==t.position||(o=null),(a?!r&&!o:!r&&"static"===t.position&&!!o&&rO.has(o.position)||ru(i)&&!r&&function e(t,r){let n=rC(t);return!(n===r||!rs(n)||rw(n))&&("fixed"===rk(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=rC(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=a[0],s=a.reduce((e,r)=>{let n=rz(t,r,o);return e.top=tI(n.top,e.top),e.right=tL(n.right,e.right),e.bottom=tL(n.bottom,e.bottom),e.left=tI(n.left,e.left),e},rz(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:rF,getElementRects:rB,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=rE(e);return{width:t,height:r}},getScale:r_,isElement:rs,isRTL:function(e){return"rtl"===rk(e).direction}};function rW(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let rV=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:i,elements:s,middlewareData:l}=t,{element:c,padding:d=0}=tq(e,t)||{};if(null==c)return{};let u=t3(d),f={x:r,y:n},p=tG(tJ(o)),m=tX(p),h=await i.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",b=a.reference[m]+a.reference[p]-f[p]-a.floating[m],x=f[p]-a.reference[p],y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),w=y?y[g]:0;w&&await (null==i.isElement?void 0:i.isElement(y))||(w=s.floating[g]||a.floating[m]);let k=w/2-h[m]/2-1,j=tL(u[v?"top":"left"],k),C=tL(u[v?"bottom":"right"],k),N=w-h[m]-C,S=w/2-h[m]/2+(b/2-x/2),E=tI(j,tL(S,N)),R=!l.arrow&&null!=tU(o)&&S!==E&&a.reference[m]/2-(S<j?j:C)-h[m]/2<0,_=R?S<j?S-j:S-N:0;return{[p]:f[p]+_,data:{[p]:E,centerOffset:S-E-_,...R&&{alignmentOffset:_}},reset:R}}});var rq="undefined"!=typeof document?a.useLayoutEffect:function(){};function rK(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!rK(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!rK(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function rU(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function rG(e,t){let r=rU(e);return Math.round(t*r)/r}function rX(e){let t=a.useRef(e);return rq(()=>{t.current=e}),t}var rY=a.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,f.jsx)(O.Primitive.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,f.jsx)("polygon",{points:"0,0 30,0 15,10"})})});rY.displayName="Arrow";var rJ="Popper",[r$,rZ]=m(rJ),[rQ,r0]=r$(rJ),r1=e=>{let{__scopePopper:t,children:r}=e,[n,o]=a.useState(null);return(0,f.jsx)(rQ,{scope:t,anchor:n,onAnchorChange:o,children:r})};r1.displayName=rJ;var r2="PopperAnchor",r3=a.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=r0(r2,r),s=a.useRef(null),l=(0,b.useComposedRefs)(t,s),c=a.useRef(null);return a.useEffect(()=>{let e=c.current;c.current=(null==n?void 0:n.current)||s.current,e!==c.current&&i.onAnchorChange(c.current)}),n?null:(0,f.jsx)(O.Primitive.div,{...o,ref:l})});r3.displayName=r2;var r5="PopperContent",[r4,r6]=r$(r5),r8=a.forwardRef((e,t)=>{var r,n,o,i,s,l,c,d;let{__scopePopper:u,side:p="bottom",sideOffset:m=0,align:v="center",alignOffset:g=0,arrowPadding:x=0,avoidCollisions:y=!0,collisionBoundary:w=[],collisionPadding:k=0,sticky:j="partial",hideWhenDetached:C=!1,updatePositionStrategy:N="optimized",onPlaced:S,...E}=e,R=r0(r5,u),[_,M]=a.useState(null),A=(0,b.useComposedRefs)(t,e=>M(e)),[P,T]=a.useState(null),D=function(e){let[t,r]=a.useState(void 0);return h(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(P),L=null!=(c=null==D?void 0:D.width)?c:0,I=null!=(d=null==D?void 0:D.height)?d:0,F="number"==typeof k?k:{top:0,right:0,bottom:0,left:0,...k},B=Array.isArray(w)?w:[w],H=B.length>0,W={padding:F,boundary:B.filter(nt),altBoundary:H},{refs:V,floatingStyles:q,placement:K,isPositioned:U,middlewareData:G}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:c,open:d}=e,[u,f]=a.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=a.useState(n);rK(p,n)||m(n);let[h,v]=a.useState(null),[g,b]=a.useState(null),x=a.useCallback(e=>{e!==j.current&&(j.current=e,v(e))},[]),y=a.useCallback(e=>{e!==C.current&&(C.current=e,b(e))},[]),w=i||h,k=s||g,j=a.useRef(null),C=a.useRef(null),N=a.useRef(u),S=null!=c,E=rX(c),R=rX(o),_=rX(d),M=a.useCallback(()=>{if(!j.current||!C.current)return;let e={placement:t,strategy:r,middleware:p};R.current&&(e.platform=R.current),((e,t,r)=>{let n=new Map,o={platform:rH,...r},a={...o.platform,_c:n};return t6(e,t,{...o,platform:a})})(j.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==_.current};A.current&&!rK(N.current,t)&&(N.current=t,$.flushSync(()=>{f(t)}))})},[p,t,r,R,_]);rq(()=>{!1===d&&N.current.isPositioned&&(N.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[d]);let A=a.useRef(!1);rq(()=>(A.current=!0,()=>{A.current=!1}),[]),rq(()=>{if(w&&(j.current=w),k&&(C.current=k),w&&k){if(E.current)return E.current(w,k,M);M()}},[w,k,M,E,S]);let P=a.useMemo(()=>({reference:j,floating:C,setReference:x,setFloating:y}),[x,y]),T=a.useMemo(()=>({reference:w,floating:k}),[w,k]),D=a.useMemo(()=>{let e={position:r,left:0,top:0};if(!T.floating)return e;let t=rG(T.floating,u.x),n=rG(T.floating,u.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...rU(T.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,T.floating,u.x,u.y]);return a.useMemo(()=>({...u,update:M,refs:P,elements:T,floatingStyles:D}),[u,M,P,T,D])}({strategy:"fixed",placement:p+("center"!==v?"-"+v:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:i=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=rR(e),u=a||i?[...d?rN(d):[],...rN(t)]:[];u.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let f=d&&l?function(e,t){let r,n=null,o=ra(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),a();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:p}=c;if(s||t(),!f||!p)return;let m=tB(u),h=tB(o.clientWidth-(d+f)),v={rootMargin:-m+"px "+-h+"px "+-tB(o.clientHeight-(u+p))+"px "+-tB(d)+"px",threshold:tI(0,tL(1,l))||1},g=!0;function b(t){let n=t[0].intersectionRatio;if(n!==l){if(!g)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||rW(c,e.getBoundingClientRect())||i(),g=!1}try{n=new IntersectionObserver(b,{...v,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(b,v)}n.observe(e)}(!0),a}(d,r):null,p=-1,m=null;s&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),d&&!c&&m.observe(d),m.observe(t));let h=c?rP(e):null;return c&&function t(){let n=rP(e);h&&!rW(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{a&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===N})},elements:{reference:R.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:a,placement:i,middlewareData:s}=t,l=await rt(t,e);return i===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:{...l,placement:i}}}}}(e),options:[e,t]}))({mainAxis:m+I,alignmentAxis:g}),y&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=tq(e,t),c={x:r,y:n},d=await t8(t,l),u=tJ(tK(o)),f=tG(u),p=c[f],m=c[u];if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+d[e],n=p-d[t];p=tI(r,tL(p,n))}if(i){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=m+d[e],n=m-d[t];m=tI(r,tL(m,n))}let h=s.fn({...t,[f]:p,[u]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:a,[u]:i}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===j?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:a,middlewareData:i}=t,{offset:s=0,mainAxis:l=!0,crossAxis:c=!0}=tq(e,t),d={x:r,y:n},u=tJ(o),f=tG(u),p=d[f],m=d[u],h=tq(s,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(l){let e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+v.mainAxis,r=a.reference[f]+a.reference[e]-v.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,b;let e="y"===f?"width":"height",t=re.has(tK(o)),r=a.reference[u]-a.floating[e]+(t&&(null==(g=i.offset)?void 0:g[u])||0)+(t?0:v.crossAxis),n=a.reference[u]+a.reference[e]+(t?0:(null==(b=i.offset)?void 0:b[u])||0)-(t?v.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[u]:m}}}}(e),options:[e,t]}))():void 0,...W}),y&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,a,i;let{placement:s,middlewareData:l,rects:c,initialPlacement:d,platform:u,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:b=!0,...x}=tq(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let y=tK(s),w=tJ(d),k=tK(d)===d,j=await (null==u.isRTL?void 0:u.isRTL(f.floating)),C=h||(k||!b?[t2(d)]:function(e){let t=t2(e);return[t$(e),t,t$(t)]}(d)),N="none"!==g;!h&&N&&C.push(...function(e,t,r,n){let o=tU(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?tQ:tZ;return t?tZ:tQ;case"left":case"right":return t?t0:t1;default:return[]}}(tK(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(t$)))),a}(d,b,g,j));let S=[d,...C],E=await t8(t,x),R=[],_=(null==(n=l.flip)?void 0:n.overflows)||[];if(p&&R.push(E[y]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=tU(e),o=tG(tJ(e)),a=tX(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=t2(i)),[i,t2(i)]}(s,c,j);R.push(E[e[0]],E[e[1]])}if(_=[..._,{placement:s,overflows:R}],!R.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&("alignment"!==m||w===tJ(t)||_.every(e=>tJ(e.placement)!==w||e.overflows[0]>0)))return{data:{index:e,overflows:_},reset:{placement:t}};let r=null==(a=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(v){case"bestFit":{let e=null==(i=_.filter(e=>{if(N){let t=tJ(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=d}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}))({...W}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a,{placement:i,rects:s,platform:l,elements:c}=t,{apply:d=()=>{},...u}=tq(e,t),f=await t8(t,u),p=tK(i),m=tU(i),h="y"===tJ(i),{width:v,height:g}=s.floating;"top"===p||"bottom"===p?(o=p,a=m===(await (null==l.isRTL?void 0:l.isRTL(c.floating))?"start":"end")?"left":"right"):(a=p,o="end"===m?"top":"bottom");let b=g-f.top-f.bottom,x=v-f.left-f.right,y=tL(g-f[o],b),w=tL(v-f[a],x),k=!t.middlewareData.shift,j=y,C=w;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(C=x),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(j=b),k&&!m){let e=tI(f.left,0),t=tI(f.right,0),r=tI(f.top,0),n=tI(f.bottom,0);h?C=v-2*(0!==e||0!==t?e+t:tI(f.left,f.right)):j=g-2*(0!==r||0!==n?r+n:tI(f.top,f.bottom))}await d({...t,availableWidth:C,availableHeight:j});let N=await l.getDimensions(c.floating);return v!==N.width||g!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...W,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:a,height:i}=r.reference,s=t.floating.style;s.setProperty("--radix-popper-available-width","".concat(n,"px")),s.setProperty("--radix-popper-available-height","".concat(o,"px")),s.setProperty("--radix-popper-anchor-width","".concat(a,"px")),s.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),P&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?rV({element:r.current,padding:n}).fn(t):{}:r?rV({element:r,padding:n}).fn(t):{}}}))(e),options:[e,t]}))({element:P,padding:x}),nr({arrowWidth:L,arrowHeight:I}),C&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=tq(e,t);switch(n){case"referenceHidden":{let e=t7(await t8(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:t9(e)}}}case"escaped":{let e=t7(await t8(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:t9(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...W})]}),[X,Y]=nn(K),J=z(S);h(()=>{U&&(null==J||J())},[U,J]);let Z=null==(r=G.arrow)?void 0:r.x,Q=null==(n=G.arrow)?void 0:n.y,ee=(null==(o=G.arrow)?void 0:o.centerOffset)!==0,[et,er]=a.useState();return h(()=>{_&&er(window.getComputedStyle(_).zIndex)},[_]),(0,f.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:U?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null==(i=G.transformOrigin)?void 0:i.x,null==(s=G.transformOrigin)?void 0:s.y].join(" "),...(null==(l=G.hide)?void 0:l.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,f.jsx)(r4,{scope:u,placedSide:X,onArrowChange:T,arrowX:Z,arrowY:Q,shouldHideArrow:ee,children:(0,f.jsx)(O.Primitive.div,{"data-side":X,"data-align":Y,...E,ref:A,style:{...E.style,animation:U?void 0:"none"}})})})});r8.displayName=r5;var r7="PopperArrow",r9={top:"bottom",right:"left",bottom:"top",left:"right"},ne=a.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=r6(r7,r),a=r9[o.placedSide];return(0,f.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,f.jsx)(rY,{...n,ref:t,style:{...n.style,display:"block"}})})});function nt(e){return null!==e}ne.displayName=r7;var nr=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,a,i;let{placement:s,rects:l,middlewareData:c}=t,d=(null==(r=c.arrow)?void 0:r.centerOffset)!==0,u=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,m]=nn(s),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(a=null==(n=c.arrow)?void 0:n.x)?a:0)+u/2,g=(null!=(i=null==(o=c.arrow)?void 0:o.y)?i:0)+f/2,b="",x="";return"bottom"===p?(b=d?h:"".concat(v,"px"),x="".concat(-f,"px")):"top"===p?(b=d?h:"".concat(v,"px"),x="".concat(l.floating.height+f,"px")):"right"===p?(b="".concat(-f,"px"),x=d?h:"".concat(g,"px")):"left"===p&&(b="".concat(l.floating.width+f,"px"),x=d?h:"".concat(g,"px")),{data:{x:b,y:x}}}});function nn(e){let[t,r="center"]=e.split("-");return[t,r]}var no=r1,na=r3,ni=r8,ns=ne;e.s(["Root",()=>nd,"VISUALLY_HIDDEN_STYLES",()=>nl],59411);var nl=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),nc=a.forwardRef((e,t)=>(0,f.jsx)(O.Primitive.span,{...e,ref:t,style:{...nl,...e.style}}));nc.displayName="VisuallyHidden";var nd=nc,[nu,nf]=m("Tooltip",[rZ]),np=rZ(),nm="TooltipProvider",nh="tooltip.open",[nv,ng]=nu(nm),nb=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:i}=e,s=a.useRef(!0),l=a.useRef(!1),c=a.useRef(0);return a.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(nv,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:a.useCallback(()=>{window.clearTimeout(c.current),s.current=!1},[]),onClose:a.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.current=!0,n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:a.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:i})};nb.displayName=nm;var nx="Tooltip",[ny,nw]=nu(nx),nk=e=>{let{__scopeTooltip:t,children:r,open:n,defaultOpen:o,onOpenChange:i,disableHoverableContent:s,delayDuration:l}=e,c=ng(nx,e.__scopeTooltip),d=np(t),[u,p]=a.useState(null),m=j(),h=a.useRef(0),v=null!=s?s:c.disableHoverableContent,b=null!=l?l:c.delayDuration,x=a.useRef(!1),[y,w]=g({prop:n,defaultProp:null!=o&&o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(nh))):c.onClose(),null==i||i(e)},caller:nx}),k=a.useMemo(()=>y?x.current?"delayed-open":"instant-open":"closed",[y]),C=a.useCallback(()=>{window.clearTimeout(h.current),h.current=0,x.current=!1,w(!0)},[w]),N=a.useCallback(()=>{window.clearTimeout(h.current),h.current=0,w(!1)},[w]),S=a.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{x.current=!0,w(!0),h.current=0},b)},[b,w]);return a.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),(0,f.jsx)(no,{...d,children:(0,f.jsx)(ny,{scope:t,contentId:m,open:y,stateAttribute:k,trigger:u,onTriggerChange:p,onTriggerEnter:a.useCallback(()=>{c.isOpenDelayedRef.current?S():C()},[c.isOpenDelayedRef,S,C]),onTriggerLeave:a.useCallback(()=>{v?N():(window.clearTimeout(h.current),h.current=0)},[N,v]),onOpen:C,onClose:N,disableHoverableContent:v,children:r})})};nk.displayName=nx;var nj="TooltipTrigger",nC=a.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=nw(nj,r),i=ng(nj,r),s=np(r),l=a.useRef(null),c=(0,b.useComposedRefs)(t,l,o.onTriggerChange),d=a.useRef(!1),p=a.useRef(!1),m=a.useCallback(()=>d.current=!1,[]);return a.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,f.jsx)(na,{asChild:!0,...s,children:(0,f.jsx)(O.Primitive.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:c,onPointerMove:u(e.onPointerMove,e=>{"touch"!==e.pointerType&&(p.current||i.isPointerInTransitRef.current||(o.onTriggerEnter(),p.current=!0))}),onPointerLeave:u(e.onPointerLeave,()=>{o.onTriggerLeave(),p.current=!1}),onPointerDown:u(e.onPointerDown,()=>{o.open&&o.onClose(),d.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:u(e.onFocus,()=>{d.current||o.onOpen()}),onBlur:u(e.onBlur,o.onClose),onClick:u(e.onClick,o.onClose)})})});nC.displayName=nj;var nN="TooltipPortal",[nS,nE]=nu(nN,{forceMount:void 0}),nR=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,a=nw(nN,t);return(0,f.jsx)(nS,{scope:t,forceMount:r,children:(0,f.jsx)(x,{present:r||a.open,children:(0,f.jsx)(Z,{asChild:!0,container:o,children:n})})})};nR.displayName=nN;var n_="TooltipContent",nM=a.forwardRef((e,t)=>{let r=nE(n_,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,i=nw(n_,e.__scopeTooltip);return(0,f.jsx)(x,{present:n||i.open,children:i.disableHoverableContent?(0,f.jsx)(nO,{side:o,...a,ref:t}):(0,f.jsx)(nA,{side:o,...a,ref:t})})}),nA=a.forwardRef((e,t)=>{let r=nw(n_,e.__scopeTooltip),n=ng(n_,e.__scopeTooltip),o=a.useRef(null),i=(0,b.useComposedRefs)(t,o),[s,l]=a.useState(null),{trigger:c,onClose:d}=r,u=o.current,{onPointerInTransitChange:p}=n,m=a.useCallback(()=>{l(null),p(!1)},[p]),h=a.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());l(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return a.useEffect(()=>()=>m(),[m]),a.useEffect(()=>{if(c&&u){let e=e=>h(e,u),t=e=>h(e,c);return c.addEventListener("pointerleave",e),u.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),u.removeEventListener("pointerleave",t)}}},[c,u,h,m]),a.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==u?void 0:u.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],s=t[a],l=i.x,c=i.y,d=s.x,u=s.y;c>n!=u>n&&r<(d-l)*(n-c)/(u-c)+l&&(o=!o)}return o}(r,s);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,u,s,d,m]),(0,f.jsx)(nO,{...e,ref:i})}),[nP,nT]=nu(nx,{isInside:!1}),nD=(0,C.createSlottable)("TooltipContent"),nO=a.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...l}=e,c=nw(n_,r),d=np(r),{onClose:u}=c;return a.useEffect(()=>(document.addEventListener(nh,u),()=>document.removeEventListener(nh,u)),[u]),a.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&u()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,u]),(0,f.jsx)(F,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:u,children:(0,f.jsxs)(ni,{"data-state":c.stateAttribute,...d,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(nD,{children:n}),(0,f.jsx)(nP,{scope:r,isInside:!0,children:(0,f.jsx)(nd,{id:c.contentId,role:"tooltip",children:o||n})})]})})});nM.displayName=n_;var nz="TooltipArrow",nL=a.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=np(r);return nT(nz,r).isInside?null:(0,f.jsx)(ns,{...o,...n,ref:t})});function nI(e){let{delayDuration:t=0,...r}=e;return(0,f.jsx)(nb,{"data-slot":"tooltip-provider",delayDuration:t,...r})}function nF(e){let{...t}=e;return(0,f.jsx)(nI,{children:(0,f.jsx)(nk,{"data-slot":"tooltip",...t})})}function nB(e){let{...t}=e;return(0,f.jsx)(nC,{"data-slot":"tooltip-trigger",...t})}function nH(e){let{className:t,sideOffset:r=0,children:n,...o}=e;return(0,f.jsx)(nR,{children:(0,f.jsxs)(nM,{"data-slot":"tooltip-content",sideOffset:r,className:(0,M.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...o,children:[n,(0,f.jsx)(nL,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}nL.displayName=nz;let nW=a.createContext(null);function nV(){let e=a.useContext(nW);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function nq(e){let{defaultOpen:t=!0,open:r,onOpenChange:n,className:o,style:i,children:s,...l}=e,c=function(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}(),[d,u]=a.useState(!1),[p,m]=a.useState(t),h=null!=r?r:p,v=a.useCallback(e=>{let t="function"==typeof e?e(h):e;n?n(t):m(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[n,h]),g=a.useCallback(()=>c?u(e=>!e):v(e=>!e),[c,v,u]);a.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let b=h?"expanded":"collapsed",x=a.useMemo(()=>({state:b,open:h,setOpen:v,isMobile:c,openMobile:d,setOpenMobile:u,toggleSidebar:g}),[b,h,v,c,d,u,g]);return(0,f.jsx)(nW.Provider,{value:x,children:(0,f.jsx)(nI,{delayDuration:0,children:(0,f.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,M.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",o),...l,children:s})})})}function nK(e){let{side:t="left",variant:r="sidebar",collapsible:n="offcanvas",className:o,children:a,...i}=e,{isMobile:s,state:l,openMobile:c,setOpenMobile:d}=nV();return"none"===n?(0,f.jsx)("div",{"data-slot":"sidebar",className:(0,M.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",o),...i,children:a}):s?(0,f.jsx)(tR,{open:c,onOpenChange:d,...i,children:(0,f.jsxs)(tA,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,f.jsxs)(tP,{className:"sr-only",children:[(0,f.jsx)(tT,{children:"Sidebar"}),(0,f.jsx)(tD,{children:"Displays the mobile sidebar."})]}),(0,f.jsx)("div",{className:"flex h-full w-full flex-col",children:a})]})}):(0,f.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?n:"","data-variant":r,"data-side":t,"data-slot":"sidebar",children:[(0,f.jsx)("div",{"data-slot":"sidebar-gap",className:(0,M.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===r||"inset"===r?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,f.jsx)("div",{"data-slot":"sidebar-container",className:(0,M.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===r||"inset"===r?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",o),...i,children:(0,f.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:a})})]})}function nU(e){let{className:t,onClick:r,...n}=e,{toggleSidebar:o}=nV();return(0,f.jsxs)(P,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,M.cn)("size-7",t),onClick:e=>{null==r||r(e),o()},...n,children:[(0,f.jsx)(_,{}),(0,f.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function nG(e){let{className:t,...r}=e,{toggleSidebar:n}=nV();return(0,f.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,M.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...r})}function nX(e){let{className:t,...r}=e;return(0,f.jsx)("main",{"data-slot":"sidebar-inset",className:(0,M.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...r})}function nY(e){let{className:t,...r}=e;return(0,f.jsx)(T,{"data-slot":"sidebar-input","data-sidebar":"input",className:(0,M.cn)("bg-background h-8 w-full shadow-none",t),...r})}function nJ(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,M.cn)("flex flex-col gap-2 p-2",t),...r})}function n$(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,M.cn)("flex flex-col gap-2 p-2",t),...r})}function nZ(e){let{className:t,...r}=e;return(0,f.jsx)(D.Separator,{"data-slot":"sidebar-separator","data-sidebar":"separator",className:(0,M.cn)("bg-sidebar-border mx-2 w-auto",t),...r})}function nQ(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,M.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...r})}function n0(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,M.cn)("relative flex w-full min-w-0 flex-col p-2",t),...r})}function n1(e){let{className:t,asChild:r=!1,...n}=e,o=r?C.Slot:"div";return(0,f.jsx)(o,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,M.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...n})}function n2(e){let{className:t,asChild:r=!1,...n}=e,o=r?C.Slot:"button";return(0,f.jsx)(o,{"data-slot":"sidebar-group-action","data-sidebar":"group-action",className:(0,M.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","group-data-[collapsible=icon]:hidden",t),...n})}function n3(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,M.cn)("w-full text-sm",t),...r})}function n5(e){let{className:t,...r}=e;return(0,f.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,M.cn)("flex w-full min-w-0 flex-col gap-1",t),...r})}function n4(e){let{className:t,...r}=e;return(0,f.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,M.cn)("group/menu-item relative",t),...r})}let n6=R("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function n8(e){let{asChild:t=!1,isActive:r=!1,variant:n="default",size:o="default",tooltip:a,className:i,...s}=e,l=t?C.Slot:"button",{isMobile:c,state:d}=nV(),u=(0,f.jsx)(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":o,"data-active":r,className:(0,M.cn)(n6({variant:n,size:o}),i),...s});return a?("string"==typeof a&&(a={children:a}),(0,f.jsxs)(nF,{children:[(0,f.jsx)(nB,{asChild:!0,children:u}),(0,f.jsx)(nH,{side:"right",align:"center",hidden:"collapsed"!==d||c,...a})]})):u}function n7(e){let{className:t,asChild:r=!1,showOnHover:n=!1,...o}=e,a=r?C.Slot:"button";return(0,f.jsx)(a,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:(0,M.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",t),...o})}function n9(e){let{className:t,...r}=e;return(0,f.jsx)("div",{"data-slot":"sidebar-menu-badge","data-sidebar":"menu-badge",className:(0,M.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...r})}function oe(e){let{className:t,showIcon:r=!1,...n}=e,o=a.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,f.jsxs)("div",{"data-slot":"sidebar-menu-skeleton","data-sidebar":"menu-skeleton",className:(0,M.cn)("flex h-8 items-center gap-2 rounded-md px-2",t),...n,children:[r&&(0,f.jsx)(tO,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,f.jsx)(tO,{className:"h-4 max-w-(--skeleton-width) flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})}function ot(e){let{className:t,...r}=e;return(0,f.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,M.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...r})}function or(e){let{className:t,...r}=e;return(0,f.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,M.cn)("group/menu-sub-item relative",t),...r})}function on(e){let{asChild:t=!1,size:r="md",isActive:n=!1,className:o,...a}=e,i=t?C.Slot:"a";return(0,f.jsx)(i,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":r,"data-active":n,className:(0,M.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===r&&"text-xs","md"===r&&"text-sm","group-data-[collapsible=icon]:hidden",o),...a})}},60313,63059,41071,75830,86318,78745,63415,7233,30374,10708,69638,73884,3116,e=>{"use strict";e.s(["AppSidebar",()=>rb],60313);var t,r,n=e.i(43476),o=e.i(71645),a=e.i(75254);let i=(0,a.default)("radar",[["path",{d:"M19.07 4.93A10 10 0 0 0 6.99 3.34",key:"z3du51"}],["path",{d:"M4 6h.01",key:"oypzma"}],["path",{d:"M2.29 9.62A10 10 0 1 0 21.31 8.35",key:"qzzz0"}],["path",{d:"M16.24 7.76A6 6 0 1 0 8.23 16.67",key:"1yjesh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M17.99 11.66A6 6 0 0 1 15.77 16.67",key:"1u2y91"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"m13.41 10.59 5.66-5.66",key:"mhq4k0"}]]),s=(0,a.default)("frame",[["line",{x1:"22",x2:"2",y1:"6",y2:"6",key:"15w7dq"}],["line",{x1:"22",x2:"2",y1:"18",y2:"18",key:"1ip48p"}],["line",{x1:"6",x2:"6",y1:"2",y2:"22",key:"a2lnyx"}],["line",{x1:"18",x2:"18",y1:"2",y2:"22",key:"8vb6jd"}]]),l=(0,a.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),c=(0,a.default)("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),d=(0,a.default)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),u=(0,a.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);e.s(["ChevronRight",()=>f],63059);let f=(0,a.default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var p=e.i(81140),m=e.i(30030),h=e.i(69340),v=e.i(34620),g=e.i(20783),b=e.i(48425),x=e.i(96626),y=e.i(10772),w="Collapsible",[k,j]=(0,m.createContextScope)(w),[C,N]=k(w),S=o.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:a,defaultOpen:i,disabled:s,onOpenChange:l,...c}=e,[d,u]=(0,h.useControllableState)({prop:a,defaultProp:null!=i&&i,onChange:l,caller:w});return(0,n.jsx)(C,{scope:r,disabled:s,contentId:(0,y.useId)(),open:d,onOpenToggle:o.useCallback(()=>u(e=>!e),[u]),children:(0,n.jsx)(b.Primitive.div,{"data-state":P(d),"data-disabled":s?"":void 0,...c,ref:t})})});S.displayName=w;var E="CollapsibleTrigger",R=o.forwardRef((e,t)=>{let{__scopeCollapsible:r,...o}=e,a=N(E,r);return(0,n.jsx)(b.Primitive.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":P(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...o,ref:t,onClick:(0,p.composeEventHandlers)(e.onClick,a.onOpenToggle)})});R.displayName=E;var _="CollapsibleContent",M=o.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=N(_,e.__scopeCollapsible);return(0,n.jsx)(x.Presence,{present:r||a.open,children:e=>{let{present:r}=e;return(0,n.jsx)(A,{...o,ref:t,present:r})}})});M.displayName=_;var A=o.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:a,children:i,...s}=e,l=N(_,r),[c,d]=o.useState(a),u=o.useRef(null),f=(0,g.useComposedRefs)(t,u),p=o.useRef(0),m=p.current,h=o.useRef(0),x=h.current,y=l.open||c,w=o.useRef(y),k=o.useRef(void 0);return o.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,v.useLayoutEffect)(()=>{let e=u.current;if(e){k.current=k.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,h.current=t.width,w.current||(e.style.transitionDuration=k.current.transitionDuration,e.style.animationName=k.current.animationName),d(a)}},[l.open,a]),(0,n.jsx)(b.Primitive.div,{"data-state":P(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!y,...s,ref:f,style:{"--radix-collapsible-content-height":m?"".concat(m,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...e.style},children:y&&i})});function P(e){return e?"open":"closed"}function T(e){let{...t}=e;return(0,n.jsx)(S,{"data-slot":"collapsible",...t})}function D(e){let{...t}=e;return(0,n.jsx)(R,{"data-slot":"collapsible-trigger",...t})}function O(e){let{...t}=e;return(0,n.jsx)(M,{"data-slot":"collapsible-content",...t})}var z=e.i(11522);function L(e){let{items:t,onNavigate:r}=e;return(0,n.jsxs)(z.SidebarGroup,{children:[(0,n.jsx)(z.SidebarGroupLabel,{children:"Platform"}),(0,n.jsx)(z.SidebarMenu,{children:t.map(e=>{var t;return e.items&&e.items.length>0?(0,n.jsx)(T,{asChild:!0,defaultOpen:e.isActive,className:"group/collapsible",children:(0,n.jsxs)(z.SidebarMenuItem,{children:[(0,n.jsx)(D,{asChild:!0,children:(0,n.jsxs)(z.SidebarMenuButton,{tooltip:e.title,children:[e.icon&&(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.title}),(0,n.jsx)(f,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,n.jsx)(O,{children:(0,n.jsx)(z.SidebarMenuSub,{children:null==(t=e.items)?void 0:t.map(e=>(0,n.jsx)(z.SidebarMenuSubItem,{children:(0,n.jsx)(z.SidebarMenuSubButton,{onClick:e.onClick,className:"cursor-pointer",children:(0,n.jsx)("span",{children:e.title})})},e.title))})})]})},e.title):(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(z.SidebarMenuButton,{onClick:e.onClick,tooltip:e.title,className:"cursor-pointer",children:[e.icon&&(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.title})]})},e.title)})})]})}let I=(0,a.default)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),F=(0,a.default)("forward",[["path",{d:"m15 17 5-5-5-5",key:"nf172w"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]]);e.s(["MoreHorizontal",()=>B],41071);let B=(0,a.default)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),H=(0,a.default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);function W(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function V(e,t){var r=W(e,t,"get");return r.get?r.get.call(e):r.value}function q(e,t,r){var n=W(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}e.s(["DropdownMenu",()=>tI,"DropdownMenuCheckboxItem",()=>tV,"DropdownMenuContent",()=>tB,"DropdownMenuGroup",()=>tH,"DropdownMenuItem",()=>tW,"DropdownMenuLabel",()=>tq,"DropdownMenuSeparator",()=>tK,"DropdownMenuShortcut",()=>tU,"DropdownMenuTrigger",()=>tF],63415),e.s(["createCollection",()=>U],75830);var K=e.i(91918);function U(e){let t=e+"CollectionProvider",[r,a]=(0,m.createContextScope)(t),[i,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,a=o.default.useRef(null),s=o.default.useRef(new Map).current;return(0,n.jsx)(i,{scope:t,itemMap:s,collectionRef:a,children:r})};l.displayName=t;let c=e+"CollectionSlot",d=(0,K.createSlot)(c),u=o.default.forwardRef((e,t)=>{let{scope:r,children:o}=e,a=s(c,r),i=(0,g.useComposedRefs)(t,a.collectionRef);return(0,n.jsx)(d,{ref:i,children:o})});u.displayName=c;let f=e+"CollectionItemSlot",p="data-radix-collection-item",h=(0,K.createSlot)(f),v=o.default.forwardRef((e,t)=>{let{scope:r,children:a,...i}=e,l=o.default.useRef(null),c=(0,g.useComposedRefs)(t,l),d=s(f,r);return o.default.useEffect(()=>(d.itemMap.set(l,{ref:l,...i}),()=>void d.itemMap.delete(l))),(0,n.jsx)(h,{...{[p]:""},ref:c,children:a})});return v.displayName=f,[{Provider:l,Slot:u,ItemSlot:v},function(t){let r=s(e+"CollectionConsumer",t);return o.default.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}var G=new WeakMap;function X(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=Y(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function Y(e){return e!=e||0===e?0:Math.trunc(e)}t=new WeakMap,(class e extends Map{set(e,r){return G.get(this)&&(this.has(e)?V(this,t)[V(this,t).indexOf(e)]=e:V(this,t).push(e)),super.set(e,r),this}insert(e,r,n){let o,a=this.has(r),i=V(this,t).length,s=Y(e),l=s>=0?s:i+s,c=l<0||l>=i?-1:l;if(c===this.size||a&&c===this.size-1||-1===c)return this.set(r,n),this;let d=this.size+ +!a;s<0&&l++;let u=[...V(this,t)],f=!1;for(let e=l;e<d;e++)if(l===e){let t=u[e];u[e]===r&&(t=u[e+1]),a&&this.delete(r),o=this.get(t),this.set(r,n)}else{f||u[e-1]!==r||(f=!0);let t=u[f?e:e-1],n=o;o=this.get(t),this.delete(t),this.set(t,n)}return this}with(t,r,n){let o=new e(this);return o.insert(t,r,n),o}before(e){let r=V(this,t).indexOf(e)-1;if(!(r<0))return this.entryAt(r)}setBefore(e,r,n){let o=V(this,t).indexOf(e);return -1===o?this:this.insert(o,r,n)}after(e){let r=V(this,t).indexOf(e);if(-1!==(r=-1===r||r===this.size-1?-1:r+1))return this.entryAt(r)}setAfter(e,r,n){let o=V(this,t).indexOf(e);return -1===o?this:this.insert(o+1,r,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return q(this,t,[]),super.clear()}delete(e){let r=super.delete(e);return r&&V(this,t).splice(V(this,t).indexOf(e),1),r}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let r=X(V(this,t),e);if(void 0!==r)return this.get(r)}entryAt(e){let r=X(V(this,t),e);if(void 0!==r)return[r,this.get(r)]}indexOf(e){return V(this,t).indexOf(e)}keyAt(e){return X(V(this,t),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],o=0;for(let e of this)Reflect.apply(t,r,[e,o,this])&&n.push(e),o++;return new e(n)}map(t,r){let n=[],o=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,o,this])]),o++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,o]=t,a=0,i=null!=o?o:this.at(0);for(let e of this)i=0===a&&1===t.length?e:Reflect.apply(n,this,[i,e,a,this]),a++;return i}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,o]=t,a=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);a=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[a,r,e,this])}return a}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let o=[...this.entries()];return o.splice(...r),new e(o)}slice(t,r){let n=new e,o=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(o=r-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,t,{writable:!0,value:void 0}),q(this,t,[...super.keys()]),G.set(this,!0)}}),e.s(["useDirection",()=>$],86318);var J=o.createContext(void 0);function $(e){let t=o.useContext(J);return e||t||"ltr"}var Z=e.i(26330),Q=e.i(3536),ee=e.i(65491),et=e.i(53660),er=e.i(74606),en=e.i(30207),eo="rovingFocusGroup.onEntryFocus",ea={bubbles:!1,cancelable:!0},ei="RovingFocusGroup",[es,el,ec]=U(ei),[ed,eu]=(0,m.createContextScope)(ei,[ec]),[ef,ep]=ed(ei),em=o.forwardRef((e,t)=>(0,n.jsx)(es.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(es.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(eh,{...e,ref:t})})}));em.displayName=ei;var eh=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:s,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...m}=e,v=o.useRef(null),x=(0,g.useComposedRefs)(t,v),y=$(s),[w,k]=(0,h.useControllableState)({prop:l,defaultProp:null!=c?c:null,onChange:d,caller:ei}),[j,C]=o.useState(!1),N=(0,en.useCallbackRef)(u),S=el(r),E=o.useRef(!1),[R,_]=o.useState(0);return o.useEffect(()=>{let e=v.current;if(e)return e.addEventListener(eo,N),()=>e.removeEventListener(eo,N)},[N]),(0,n.jsx)(ef,{scope:r,orientation:a,dir:y,loop:i,currentTabStopId:w,onItemFocus:o.useCallback(e=>k(e),[k]),onItemShiftTab:o.useCallback(()=>C(!0),[]),onFocusableItemAdd:o.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>_(e=>e-1),[]),children:(0,n.jsx)(b.Primitive.div,{tabIndex:j||0===R?-1:0,"data-orientation":a,...m,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,p.composeEventHandlers)(e.onMouseDown,()=>{E.current=!0}),onFocus:(0,p.composeEventHandlers)(e.onFocus,e=>{let t=!E.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(eo,ea);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);ex([e.find(e=>e.active),e.find(e=>e.id===w),...e].filter(Boolean).map(e=>e.ref.current),f)}}E.current=!1}),onBlur:(0,p.composeEventHandlers)(e.onBlur,()=>C(!1))})})}),ev="RovingFocusGroupItem",eg=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:s,children:l,...c}=e,d=(0,y.useId)(),u=s||d,f=ep(ev,r),m=f.currentTabStopId===u,h=el(r),{onFocusableItemAdd:v,onFocusableItemRemove:g,currentTabStopId:x}=f;return o.useEffect(()=>{if(a)return v(),()=>g()},[a,v,g]),(0,n.jsx)(es.ItemSlot,{scope:r,id:u,focusable:a,active:i,children:(0,n.jsx)(b.Primitive.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:(0,p.composeEventHandlers)(e.onMouseDown,e=>{a?f.onItemFocus(u):e.preventDefault()}),onFocus:(0,p.composeEventHandlers)(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return eb[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>ex(r))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=x}):l})})});eg.displayName=ev;var eb={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ex(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var ey=e.i(86312),ew=e.i(85369),ek=["Enter"," "],ej=["ArrowUp","PageDown","End"],eC=["ArrowDown","PageUp","Home",...ej],eN={ltr:[...ek,"ArrowRight"],rtl:[...ek,"ArrowLeft"]},eS={ltr:["ArrowLeft"],rtl:["ArrowRight"]},eE="Menu",[eR,e_,eM]=U(eE),[eA,eP]=(0,m.createContextScope)(eE,[eM,et.createPopperScope,eu]),eT=(0,et.createPopperScope)(),eD=eu(),[eO,ez]=eA(eE),[eL,eI]=eA(eE),eF=e=>{let{__scopeMenu:t,open:r=!1,children:a,dir:i,onOpenChange:s,modal:l=!0}=e,c=eT(t),[d,u]=o.useState(null),f=o.useRef(!1),p=(0,en.useCallbackRef)(s),m=$(i);return o.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,n.jsx)(et.Root,{...c,children:(0,n.jsx)(eO,{scope:t,open:r,onOpenChange:p,content:d,onContentChange:u,children:(0,n.jsx)(eL,{scope:t,onClose:o.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:a})})})};eF.displayName=eE;var eB=o.forwardRef((e,t)=>{let{__scopeMenu:r,...o}=e,a=eT(r);return(0,n.jsx)(et.Anchor,{...a,...o,ref:t})});eB.displayName="MenuAnchor";var eH="MenuPortal",[eW,eV]=eA(eH,{forceMount:void 0}),eq=e=>{let{__scopeMenu:t,forceMount:r,children:o,container:a}=e,i=ez(eH,t);return(0,n.jsx)(eW,{scope:t,forceMount:r,children:(0,n.jsx)(x.Presence,{present:r||i.open,children:(0,n.jsx)(er.Portal,{asChild:!0,container:a,children:o})})})};eq.displayName=eH;var eK="MenuContent",[eU,eG]=eA(eK),eX=o.forwardRef((e,t)=>{let r=eV(eK,e.__scopeMenu),{forceMount:o=r.forceMount,...a}=e,i=ez(eK,e.__scopeMenu),s=eI(eK,e.__scopeMenu);return(0,n.jsx)(eR.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(x.Presence,{present:o||i.open,children:(0,n.jsx)(eR.Slot,{scope:e.__scopeMenu,children:s.modal?(0,n.jsx)(eY,{...a,ref:t}):(0,n.jsx)(eJ,{...a,ref:t})})})})}),eY=o.forwardRef((e,t)=>{let r=ez(eK,e.__scopeMenu),a=o.useRef(null),i=(0,g.useComposedRefs)(t,a);return o.useEffect(()=>{let e=a.current;if(e)return(0,ey.hideOthers)(e)},[]),(0,n.jsx)(eZ,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,p.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),eJ=o.forwardRef((e,t)=>{let r=ez(eK,e.__scopeMenu);return(0,n.jsx)(eZ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),e$=(0,K.createSlot)("MenuContent.ScrollLock"),eZ=o.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:l,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:h,onDismiss:v,disableOutsideScroll:b,...x}=e,y=ez(eK,r),w=eI(eK,r),k=eT(r),j=eD(r),C=e_(r),[N,S]=o.useState(null),E=o.useRef(null),R=(0,g.useComposedRefs)(t,E,y.onContentChange),_=o.useRef(0),M=o.useRef(""),A=o.useRef(0),P=o.useRef(null),T=o.useRef("right"),D=o.useRef(0),O=b?ew.RemoveScroll:o.Fragment;o.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,Q.useFocusGuards)();let z=o.useCallback(e=>{var t,r;return T.current===(null==(t=P.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],s=t[a],l=i.x,c=i.y,d=s.x,u=s.y;c>n!=u>n&&r<(d-l)*(n-c)/(u-c)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=P.current)?void 0:r.area)},[]);return(0,n.jsx)(eU,{scope:r,searchRef:M,onItemEnter:o.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:o.useCallback(e=>{var t;z(e)||(null==(t=E.current)||t.focus(),S(null))},[z]),onTriggerLeave:o.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:A,onPointerGraceIntentChange:o.useCallback(e=>{P.current=e},[]),children:(0,n.jsx)(O,{...b?{as:e$,allowPinchZoom:!0}:void 0,children:(0,n.jsx)(ee.FocusScope,{asChild:!0,trapped:i,onMountAutoFocus:(0,p.composeEventHandlers)(s,e=>{var t;e.preventDefault(),null==(t=E.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,n.jsx)(Z.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:h,onDismiss:v,children:(0,n.jsx)(em,{asChild:!0,...j,dir:w.dir,orientation:"vertical",loop:a,currentTabStopId:N,onCurrentTabStopIdChange:S,onEntryFocus:(0,p.composeEventHandlers)(d,e=>{w.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,n.jsx)(et.Content,{role:"menu","aria-orientation":"vertical","data-state":tm(y.open),"data-radix-menu-content":"",dir:w.dir,...k,...x,ref:R,style:{outline:"none",...x.style},onKeyDown:(0,p.composeEventHandlers)(x.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&(e=>{var t,r;let n=M.current+e,o=C().filter(e=>!e.disabled),a=document.activeElement,i=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,s=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}(o.map(e=>e.textValue),n,i),l=null==(r=o.find(e=>e.textValue===s))?void 0:r.ref.current;!function e(t){M.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))}(n),l&&setTimeout(()=>l.focus())})(e.key));let o=E.current;if(e.target!==o||!eC.includes(e.key))return;e.preventDefault();let a=C().filter(e=>!e.disabled).map(e=>e.ref.current);ej.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,p.composeEventHandlers)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),M.current="")}),onPointerMove:(0,p.composeEventHandlers)(e.onPointerMove,tg(e=>{let t=e.target,r=D.current!==e.clientX;e.currentTarget.contains(t)&&r&&(T.current=e.clientX>D.current?"right":"left",D.current=e.clientX)}))})})})})})})});eX.displayName=eK;var eQ=o.forwardRef((e,t)=>{let{__scopeMenu:r,...o}=e;return(0,n.jsx)(b.Primitive.div,{role:"group",...o,ref:t})});eQ.displayName="MenuGroup";var e0=o.forwardRef((e,t)=>{let{__scopeMenu:r,...o}=e;return(0,n.jsx)(b.Primitive.div,{...o,ref:t})});e0.displayName="MenuLabel";var e1="MenuItem",e2="menu.itemSelect",e3=o.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...i}=e,s=o.useRef(null),l=eI(e1,e.__scopeMenu),c=eG(e1,e.__scopeMenu),d=(0,g.useComposedRefs)(t,s),u=o.useRef(!1);return(0,n.jsx)(e5,{...i,ref:d,disabled:r,onClick:(0,p.composeEventHandlers)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(e2,{bubbles:!0,cancelable:!0});e.addEventListener(e2,e=>null==a?void 0:a(e),{once:!0}),(0,b.dispatchDiscreteCustomEvent)(e,t),t.defaultPrevented?u.current=!1:l.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),u.current=!0},onPointerUp:(0,p.composeEventHandlers)(e.onPointerUp,e=>{var t;u.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||ek.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});e3.displayName=e1;var e5=o.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:i,...s}=e,l=eG(e1,r),c=eD(r),d=o.useRef(null),u=(0,g.useComposedRefs)(t,d),[f,m]=o.useState(!1),[h,v]=o.useState("");return o.useEffect(()=>{let e=d.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,n.jsx)(eR.ItemSlot,{scope:r,disabled:a,textValue:null!=i?i:h,children:(0,n.jsx)(eg,{asChild:!0,...c,focusable:!a,children:(0,n.jsx)(b.Primitive.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...s,ref:u,onPointerMove:(0,p.composeEventHandlers)(e.onPointerMove,tg(e=>{a?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,p.composeEventHandlers)(e.onPointerLeave,tg(e=>l.onItemLeave(e))),onFocus:(0,p.composeEventHandlers)(e.onFocus,()=>m(!0)),onBlur:(0,p.composeEventHandlers)(e.onBlur,()=>m(!1))})})})}),e4=o.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:o,...a}=e;return(0,n.jsx)(tn,{scope:e.__scopeMenu,checked:r,children:(0,n.jsx)(e3,{role:"menuitemcheckbox","aria-checked":th(r)?"mixed":r,...a,ref:t,"data-state":tv(r),onSelect:(0,p.composeEventHandlers)(a.onSelect,()=>null==o?void 0:o(!!th(r)||!r),{checkForDefaultPrevented:!1})})})});e4.displayName="MenuCheckboxItem";var e6="MenuRadioGroup",[e8,e7]=eA(e6,{value:void 0,onValueChange:()=>{}}),e9=o.forwardRef((e,t)=>{let{value:r,onValueChange:o,...a}=e,i=(0,en.useCallbackRef)(o);return(0,n.jsx)(e8,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,n.jsx)(eQ,{...a,ref:t})})});e9.displayName=e6;var te="MenuRadioItem",tt=o.forwardRef((e,t)=>{let{value:r,...o}=e,a=e7(te,e.__scopeMenu),i=r===a.value;return(0,n.jsx)(tn,{scope:e.__scopeMenu,checked:i,children:(0,n.jsx)(e3,{role:"menuitemradio","aria-checked":i,...o,ref:t,"data-state":tv(i),onSelect:(0,p.composeEventHandlers)(o.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});tt.displayName=te;var tr="MenuItemIndicator",[tn,to]=eA(tr,{checked:!1}),ta=o.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:o,...a}=e,i=to(tr,r);return(0,n.jsx)(x.Presence,{present:o||th(i.checked)||!0===i.checked,children:(0,n.jsx)(b.Primitive.span,{...a,ref:t,"data-state":tv(i.checked)})})});ta.displayName=tr;var ti=o.forwardRef((e,t)=>{let{__scopeMenu:r,...o}=e;return(0,n.jsx)(b.Primitive.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});ti.displayName="MenuSeparator";var ts=o.forwardRef((e,t)=>{let{__scopeMenu:r,...o}=e,a=eT(r);return(0,n.jsx)(et.Arrow,{...a,...o,ref:t})});ts.displayName="MenuArrow";var[tl,tc]=eA("MenuSub"),td="MenuSubTrigger",tu=o.forwardRef((e,t)=>{let r=ez(td,e.__scopeMenu),a=eI(td,e.__scopeMenu),i=tc(td,e.__scopeMenu),s=eG(td,e.__scopeMenu),l=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,u={__scopeMenu:e.__scopeMenu},f=o.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return o.useEffect(()=>f,[f]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,n.jsx)(eB,{asChild:!0,...u,children:(0,n.jsx)(e5,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":tm(r.open),...e,ref:(0,g.composeRefs)(t,i.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,p.composeEventHandlers)(e.onPointerMove,tg(t=>{s.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||l.current||(s.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,p.composeEventHandlers)(e.onPointerLeave,tg(e=>{var t,n;f();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,i=o[a?"left":"right"],l=o[a?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:i,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&eN[a.dir].includes(t.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),t.preventDefault()}})})})});tu.displayName=td;var tf="MenuSubContent",tp=o.forwardRef((e,t)=>{let r=eV(eK,e.__scopeMenu),{forceMount:a=r.forceMount,...i}=e,s=ez(eK,e.__scopeMenu),l=eI(eK,e.__scopeMenu),c=tc(tf,e.__scopeMenu),d=o.useRef(null),u=(0,g.useComposedRefs)(t,d);return(0,n.jsx)(eR.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(x.Presence,{present:a||s.open,children:(0,n.jsx)(eR.Slot,{scope:e.__scopeMenu,children:(0,n.jsx)(eZ,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:u,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;l.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,p.composeEventHandlers)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,p.composeEventHandlers)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=eS[l.dir].includes(e.key);if(t&&r){var n;s.onOpenChange(!1),null==(n=c.trigger)||n.focus(),e.preventDefault()}})})})})})});function tm(e){return e?"open":"closed"}function th(e){return"indeterminate"===e}function tv(e){return th(e)?"indeterminate":e?"checked":"unchecked"}function tg(e){return t=>"mouse"===t.pointerType?e(t):void 0}tp.displayName=tf;var tb="DropdownMenu",[tx,ty]=(0,m.createContextScope)(tb,[eP]),tw=eP(),[tk,tj]=tx(tb),tC=e=>{let{__scopeDropdownMenu:t,children:r,dir:a,open:i,defaultOpen:s,onOpenChange:l,modal:c=!0}=e,d=tw(t),u=o.useRef(null),[f,p]=(0,h.useControllableState)({prop:i,defaultProp:null!=s&&s,onChange:l,caller:tb});return(0,n.jsx)(tk,{scope:t,triggerId:(0,y.useId)(),triggerRef:u,contentId:(0,y.useId)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,n.jsx)(eF,{...d,open:f,onOpenChange:p,dir:a,modal:c,children:r})})};tC.displayName=tb;var tN="DropdownMenuTrigger",tS=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:o=!1,...a}=e,i=tj(tN,r),s=tw(r);return(0,n.jsx)(eB,{asChild:!0,...s,children:(0,n.jsx)(b.Primitive.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...a,ref:(0,g.composeRefs)(t,i.triggerRef),onPointerDown:(0,p.composeEventHandlers)(e.onPointerDown,e=>{!o&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,e=>{!o&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});tS.displayName=tN;var tE=e=>{let{__scopeDropdownMenu:t,...r}=e,o=tw(t);return(0,n.jsx)(eq,{...o,...r})};tE.displayName="DropdownMenuPortal";var tR="DropdownMenuContent",t_=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=tj(tR,r),s=tw(r),l=o.useRef(!1);return(0,n.jsx)(eX,{id:i.contentId,"aria-labelledby":i.triggerId,...s,...a,ref:t,onCloseAutoFocus:(0,p.composeEventHandlers)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=i.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,p.composeEventHandlers)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});t_.displayName=tR;var tM=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(eQ,{...a,...o,ref:t})});tM.displayName="DropdownMenuGroup";var tA=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(e0,{...a,...o,ref:t})});tA.displayName="DropdownMenuLabel";var tP=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(e3,{...a,...o,ref:t})});tP.displayName="DropdownMenuItem";var tT=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(e4,{...a,...o,ref:t})});tT.displayName="DropdownMenuCheckboxItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(e9,{...a,...o,ref:t})}).displayName="DropdownMenuRadioGroup",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(tt,{...a,...o,ref:t})}).displayName="DropdownMenuRadioItem";var tD=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(ta,{...a,...o,ref:t})});tD.displayName="DropdownMenuItemIndicator";var tO=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(ti,{...a,...o,ref:t})});tO.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(ts,{...a,...o,ref:t})}).displayName="DropdownMenuArrow",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(tu,{...a,...o,ref:t})}).displayName="DropdownMenuSubTrigger",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tw(r);return(0,n.jsx)(tp,{...a,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent",e.s(["default",()=>tz],78745);let tz=(0,a.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);(0,a.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var tL=e.i(47163);function tI(e){let{...t}=e;return(0,n.jsx)(tC,{"data-slot":"dropdown-menu",...t})}function tF(e){let{...t}=e;return(0,n.jsx)(tS,{"data-slot":"dropdown-menu-trigger",...t})}function tB(e){let{className:t,sideOffset:r=4,...o}=e;return(0,n.jsx)(tE,{children:(0,n.jsx)(t_,{"data-slot":"dropdown-menu-content",sideOffset:r,className:(0,tL.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...o})})}function tH(e){let{...t}=e;return(0,n.jsx)(tM,{"data-slot":"dropdown-menu-group",...t})}function tW(e){let{className:t,inset:r,variant:o="default",...a}=e;return(0,n.jsx)(tP,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":o,className:(0,tL.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function tV(e){let{className:t,children:r,checked:o,...a}=e;return(0,n.jsxs)(tT,{"data-slot":"dropdown-menu-checkbox-item",className:(0,tL.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...a,children:[(0,n.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(tD,{children:(0,n.jsx)(tz,{className:"size-4"})})}),r]})}function tq(e){let{className:t,inset:r,...o}=e;return(0,n.jsx)(tA,{"data-slot":"dropdown-menu-label","data-inset":r,className:(0,tL.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...o})}function tK(e){let{className:t,...r}=e;return(0,n.jsx)(tO,{"data-slot":"dropdown-menu-separator",className:(0,tL.cn)("bg-border -mx-1 my-1 h-px",t),...r})}function tU(e){let{className:t,...r}=e;return(0,n.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,tL.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...r})}function tG(e){let{projects:t}=e,{isMobile:r}=(0,z.useSidebar)();return(0,n.jsxs)(z.SidebarGroup,{className:"group-data-[collapsible=icon]:hidden",children:[(0,n.jsx)(z.SidebarGroupLabel,{children:"Projects"}),(0,n.jsxs)(z.SidebarMenu,{children:[t.map(e=>(0,n.jsxs)(z.SidebarMenuItem,{children:[(0,n.jsx)(z.SidebarMenuButton,{asChild:!0,children:(0,n.jsxs)("a",{href:e.url,children:[(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{children:e.name})]})}),(0,n.jsxs)(tI,{children:[(0,n.jsx)(tF,{asChild:!0,children:(0,n.jsxs)(z.SidebarMenuAction,{showOnHover:!0,children:[(0,n.jsx)(B,{}),(0,n.jsx)("span",{className:"sr-only",children:"More"})]})}),(0,n.jsxs)(tB,{className:"w-48 rounded-lg",side:r?"bottom":"right",align:r?"end":"start",children:[(0,n.jsxs)(tW,{children:[(0,n.jsx)(I,{className:"text-muted-foreground"}),(0,n.jsx)("span",{children:"View Project"})]}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(F,{className:"text-muted-foreground"}),(0,n.jsx)("span",{children:"Share Project"})]}),(0,n.jsx)(tK,{}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(H,{className:"text-muted-foreground"}),(0,n.jsx)("span",{children:"Delete Project"})]})]})]})]},e.name)),(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(z.SidebarMenuButton,{className:"text-sidebar-foreground/70",children:[(0,n.jsx)(B,{className:"text-sidebar-foreground/70"}),(0,n.jsx)("span",{children:"More"})]})})]})]})}let tX=(0,a.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),tY=(0,a.default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),tJ=(0,a.default)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),t$=(0,a.default)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),tZ=(0,a.default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);function tQ(){let{isMobile:e}=(0,z.useSidebar)();return(0,n.jsx)(z.SidebarMenu,{children:(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(tI,{children:[(0,n.jsx)(tF,{asChild:!0,children:(0,n.jsxs)(z.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",tooltip:"Settings",children:[(0,n.jsx)(tX,{className:"size-4"}),(0,n.jsx)("span",{className:"ml-2 truncate font-medium data-[collapsed]:hidden",children:"Settings"})]})}),(0,n.jsxs)(tB,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:e?"bottom":"right",align:"end",sideOffset:4,children:[(0,n.jsxs)(tH,{children:[(0,n.jsxs)(tW,{children:[(0,n.jsx)(tZ,{}),"Account"]}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(t$,{}),"Security"]}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(tY,{}),"Notifications"]}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(tJ,{}),"Appearance"]})]}),(0,n.jsx)(tK,{}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(tX,{}),"Settings"]})]})]})})})}let t0=(0,a.default)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);e.s(["Plus",()=>t1],7233);let t1=(0,a.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),t2=(0,a.default)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),t3=(0,a.default)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);var t5=e.i(65759);function t4(e){let{onAddBackend:t,onEditBackend:r,refreshTrigger:a}=e,{isMobile:i}=(0,z.useSidebar)(),[s,l]=o.useState([]),[c,d]=o.useState(null),[u,f]=o.useState(!0);o.useEffect(()=>{p()},[a]),o.useEffect(()=>{let e=setInterval(async()=>{try{let e=await (0,t5.GetBackends)();l(e.backends||[]),d(t=>{if(t){var r;return(null==(r=e.backends)?void 0:r.find(e=>e.id===t.id))||t}return t})}catch(e){console.debug("Failed to refresh latency data:",e)}},2e3);return()=>clearInterval(e)},[]);let p=async()=>{try{f(!0);let e=await (0,t5.GetBackends)();l(e.backends||[]);try{let e=await (0,t5.GetActiveBackend)();d(e)}catch(t){e.backends&&e.backends.length>0?d(e.backends[0]):d(null)}}catch(e){console.error("Failed to load backends:",e)}finally{f(!1)}},m=async e=>{try{await (0,t5.SetActiveBackend)(e.id),d(e)}catch(e){console.error("Failed to set active backend:",e)}},h=e=>{if("connected"===e.status&&e.latency>=0)if(e.latency<100)return{text:"".concat(e.latency,"ms"),color:"text-green-600"};else if(e.latency<500)return{text:"".concat(e.latency,"ms"),color:"text-yellow-600"};else return{text:"".concat(e.latency,"ms"),color:"text-red-600"};return"error"===e.status?{text:"Error",color:"text-red-600"}:{text:"Testing...",color:"text-gray-500"}};return u?(0,n.jsx)(z.SidebarMenu,{children:(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(z.SidebarMenuButton,{size:"lg",disabled:!0,children:[(0,n.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,n.jsx)(t2,{className:"size-4"})}),(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:"Loading..."}),(0,n.jsx)("span",{className:"truncate text-xs",children:"Please wait"})]})]})})}):c||0!==s.length?c?(0,n.jsx)(z.SidebarMenu,{children:(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(tI,{children:[(0,n.jsx)(tF,{asChild:!0,children:(0,n.jsxs)(z.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,n.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,n.jsx)(t2,{className:"size-4"})}),(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:c.name}),(0,n.jsx)("span",{className:"truncate text-xs ".concat(h(c).color),children:h(c).text})]}),(0,n.jsx)(t0,{className:"ml-auto"})]})}),(0,n.jsxs)(tB,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"start",side:i?"bottom":"right",sideOffset:4,children:[(0,n.jsx)(tq,{className:"text-muted-foreground text-xs",children:"Backends"}),s.map((e,t)=>(0,n.jsxs)(tW,{onClick:()=>m(e),className:"gap-2 p-2",children:[(0,n.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border",children:(0,n.jsx)(t2,{className:"size-3.5 shrink-0"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"text-xs ".concat(h(e).color),children:h(e).text})]}),(0,n.jsxs)(tU,{children:["⌘",t+1]})]},e.id)),(0,n.jsx)(tK,{}),c&&(0,n.jsxs)(tW,{className:"gap-2 p-2",onClick:()=>null==r?void 0:r(c),children:[(0,n.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,n.jsx)(t3,{className:"size-4"})}),(0,n.jsx)("div",{className:"text-muted-foreground font-medium",children:"Edit Current Backend"})]}),(0,n.jsxs)(tW,{className:"gap-2 p-2",onClick:t,children:[(0,n.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,n.jsx)(t1,{className:"size-4"})}),(0,n.jsx)("div",{className:"text-muted-foreground font-medium",children:"Add Backend"})]})]})]})})}):null:(0,n.jsx)(z.SidebarMenu,{children:(0,n.jsx)(z.SidebarMenuItem,{children:(0,n.jsxs)(z.SidebarMenuButton,{size:"lg",onClick:t,className:"cursor-pointer",children:[(0,n.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,n.jsx)(t1,{className:"size-4"})}),(0,n.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,n.jsx)("span",{className:"truncate font-medium",children:"Add Backend"}),(0,n.jsx)("span",{className:"truncate text-xs",children:"No backends configured"})]})]})})})}var t6=e.i(67881);e.s(["Dialog",()=>t9,"DialogContent",()=>rr,"DialogDescription",()=>ri,"DialogFooter",()=>ro,"DialogHeader",()=>rn,"DialogTitle",()=>ra],30374);var t8=e.i(26999),t7=e.i(95926);function t9(e){let{...t}=e;return(0,n.jsx)(t8.Root,{"data-slot":"dialog",...t})}function re(e){let{...t}=e;return(0,n.jsx)(t8.Portal,{"data-slot":"dialog-portal",...t})}function rt(e){let{className:t,...r}=e;return(0,n.jsx)(t8.Overlay,{"data-slot":"dialog-overlay",className:(0,tL.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function rr(e){let{className:t,children:r,showCloseButton:o=!0,...a}=e;return(0,n.jsxs)(re,{"data-slot":"dialog-portal",children:[(0,n.jsx)(rt,{}),(0,n.jsxs)(t8.Content,{"data-slot":"dialog-content",className:(0,tL.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a,children:[r,o&&(0,n.jsxs)(t8.Close,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(t7.XIcon,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function rn(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,tL.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function ro(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,tL.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function ra(e){let{className:t,...r}=e;return(0,n.jsx)(t8.Title,{"data-slot":"dialog-title",className:(0,tL.cn)("text-lg leading-none font-semibold",t),...r})}function ri(e){let{className:t,...r}=e;return(0,n.jsx)(t8.Description,{"data-slot":"dialog-description",className:(0,tL.cn)("text-muted-foreground text-sm",t),...r})}var rs=e.i(23750);e.s(["Label",()=>rc],10708);var rl=o.forwardRef((e,t)=>(0,n.jsx)(b.Primitive.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));function rc(e){let{className:t,...r}=e;return(0,n.jsx)(rl,{"data-slot":"label",className:(0,tL.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}rl.displayName="Label";let rd=(0,a.default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);e.s(["CheckCircle",()=>ru],69638);let ru=(0,a.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);e.s(["XCircle",()=>rf],73884);let rf=(0,a.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);e.s(["Clock",()=>rp],3116);let rp=(0,a.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function rm(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rh(t){let{formData:a,disabled:i=!1}=t,[s,l]=o.useState(!1),[c,d]=o.useState(null),[u,f]=o.useState({}),p=async()=>{if(!a.url||!a.api_key)return void f({url:a.url?"":"URL is required for testing",api_key:a.api_key?"":"API Key is required for testing"});l(!0),d(null),f({});try{let{TestBackendConnectionDirect:t}=await e.A(43988),r=await t({name:a.name||"Test",url:a.url,api_key:a.api_key});d(r)}catch(e){d(new r.BackendConnectionTest({success:!1,message:"Test failed: ".concat(e),status:0,latency:0,tested_at:new Date}))}finally{l(!1)}};return o.useEffect(()=>{d(null),f({})},[a.url,a.api_key]),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{children:"Connection Test"}),(0,n.jsx)(t6.Button,{type:"button",variant:"outline",size:"sm",onClick:p,disabled:i||s||!a.url||!a.api_key,children:s?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(rd,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing..."]}):"Test Connection"}),c&&(0,n.jsxs)("div",{className:"p-3 rounded-md border ".concat(c.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"),children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[c.success?(0,n.jsx)(ru,{className:"h-4 w-4"}):(0,n.jsx)(rf,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"font-medium",children:c.success?"Connection Successful":"Connection Failed"})]}),(0,n.jsx)("p",{className:"text-sm mt-1",children:c.message}),c.success&&(0,n.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(rp,{className:"h-3 w-3"}),(0,n.jsxs)("span",{children:["Latency: ",c.latency,"ms"]})]}),(0,n.jsxs)("div",{children:["Status: ",c.status]})]})]}),(u.url||u.api_key)&&(0,n.jsxs)("div",{className:"text-sm text-red-500",children:[u.url&&(0,n.jsx)("p",{children:u.url}),u.api_key&&(0,n.jsx)("p",{children:u.api_key})]})]})}function rv(e){let{open:t,onOpenChange:r,onBackendAdded:a}=e,[i,s]=o.useState({name:"",url:"",api_key:""}),[l,c]=o.useState(!1),[d,u]=o.useState({}),f=()=>{s({name:"",url:"",api_key:""}),u({})},p=(e,t)=>{s(r=>({...r,[e]:t})),d[e]&&u(t=>({...t,[e]:""}))},m=async e=>{if(e.preventDefault(),(()=>{let e={};if(i.name.trim()||(e.name="Backend name is required"),i.url.trim())try{let e=i.url.startsWith("http")?i.url:"https://".concat(i.url);new URL(e)}catch(t){e.url="Please enter a valid URL"}else e.url="Backend URL is required";return i.api_key.trim()||(e.api_key="API Key is required"),u(e),0===Object.keys(e).length})()){c(!0);try{await (0,t5.AddBackend)(i),null==a||a(),r(!1),f()}catch(e){u({submit:"Failed to add backend: ".concat(e)})}finally{c(!1)}}},h=e=>{e||f(),r(e)};return(0,n.jsx)(t9,{open:t,onOpenChange:h,children:(0,n.jsxs)(rr,{className:"sm:max-w-[500px]",children:[(0,n.jsxs)(rn,{children:[(0,n.jsx)(ra,{children:"Add New Backend"}),(0,n.jsx)(ri,{children:"Add a new Acunetix scanner backend. You can test the connection before saving."})]}),(0,n.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"name",children:"Backend Name"}),(0,n.jsx)(rs.Input,{id:"name",placeholder:"e.g., Production Scanner",value:i.name,onChange:e=>p("name",e.target.value),className:d.name?"border-red-500":""}),d.name&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:d.name})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"url",children:"Backend URL"}),(0,n.jsx)(rs.Input,{id:"url",placeholder:"https://scanner.example.com:3443",value:i.url,onChange:e=>p("url",e.target.value),className:d.url?"border-red-500":""}),d.url&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:d.url})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"api_key",children:"API Key"}),(0,n.jsx)(rs.Input,{id:"api_key",type:"password",placeholder:"Enter your API key",value:i.api_key,onChange:e=>p("api_key",e.target.value),className:d.api_key?"border-red-500":""}),d.api_key&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:d.api_key})]}),(0,n.jsx)(rh,{formData:i,disabled:l}),d.submit&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:d.submit})]}),(0,n.jsxs)(ro,{children:[(0,n.jsx)(t6.Button,{type:"button",variant:"outline",onClick:()=>h(!1),disabled:l,children:"Cancel"}),(0,n.jsx)(t6.Button,{type:"submit",onClick:m,disabled:l||!i.name||!i.url||!i.api_key,children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(rd,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add Backend"})]})]})})}function rg(e){let{open:t,onOpenChange:r,backend:a,onBackendUpdated:i,onBackendDeleted:s}=e,[l,c]=o.useState({name:"",url:"",api_key:""}),[d,u]=o.useState(!1),[f,p]=o.useState(!1),[m,h]=o.useState({});o.useEffect(()=>{a&&t&&(c({name:a.name||"",url:a.url||"",api_key:a.api_key||""}),h({}))},[a,t]);let v=(e,t)=>{c(r=>({...r,[e]:t})),m[e]&&h(t=>({...t,[e]:""}))},g=async e=>{if(e.preventDefault(),a&&(()=>{let e={};if(l.name.trim()||(e.name="Backend name is required"),l.url.trim())try{let e=l.url.startsWith("http")?l.url:"https://".concat(l.url);new URL(e)}catch(t){e.url="Please enter a valid URL"}else e.url="Backend URL is required";return l.api_key.trim()||(e.api_key="API Key is required"),h(e),0===Object.keys(e).length})()){u(!0);try{await (0,t5.UpdateBackend)(a.id,l),null==i||i(),r(!1)}catch(e){h({submit:"Failed to update backend: ".concat(e)})}finally{u(!1)}}},b=async()=>{if(a&&confirm("Are you sure you want to delete &quot;".concat(a.name,"&quot;? This action cannot be undone."))){p(!0);try{await (0,t5.DeleteBackend)(a.id),null==s||s(),r(!1)}catch(e){h({delete:"Failed to delete backend: ".concat(e)})}finally{p(!1)}}},x=e=>{(e||!f)&&(r(e),e||setTimeout(()=>{c({name:"",url:"",api_key:""}),h({})},100))};return a?(0,n.jsx)(t9,{open:t,onOpenChange:x,children:(0,n.jsxs)(rr,{className:"sm:max-w-[500px]",children:[(0,n.jsxs)(rn,{children:[(0,n.jsx)(ra,{children:"Edit Backend"}),(0,n.jsxs)(ri,{children:['Update the configuration for "',a.name,'". You can test the connection after making changes.']})]}),(0,n.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"edit-name",children:"Backend Name"}),(0,n.jsx)(rs.Input,{id:"edit-name",placeholder:"e.g., Production Scanner",value:l.name,onChange:e=>v("name",e.target.value),className:m.name?"border-red-500":""}),m.name&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:m.name})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"edit-url",children:"Backend URL"}),(0,n.jsx)(rs.Input,{id:"edit-url",placeholder:"https://scanner.example.com:3443",value:l.url,onChange:e=>v("url",e.target.value),className:m.url?"border-red-500":""}),m.url&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:m.url})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(rc,{htmlFor:"edit-api_key",children:"API Key"}),(0,n.jsx)(rs.Input,{id:"edit-api_key",type:"password",placeholder:"Enter your API key",value:l.api_key,onChange:e=>v("api_key",e.target.value),className:m.api_key?"border-red-500":""}),m.api_key&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:m.api_key})]}),(0,n.jsx)(rh,{formData:l,disabled:d||f}),m.submit&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:m.submit}),m.delete&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:m.delete})]}),(0,n.jsxs)(ro,{className:"flex justify-between",children:[(0,n.jsx)(t6.Button,{type:"button",variant:"destructive",onClick:b,disabled:d||f,className:"mr-auto",children:f?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(rd,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(H,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(t6.Button,{type:"button",variant:"outline",onClick:()=>x(!1),disabled:d||f,children:"Cancel"}),(0,n.jsx)(t6.Button,{type:"submit",onClick:g,disabled:d||f||!l.name||!l.url||!l.api_key,children:d?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(rd,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Backend"})]})]})]})}):null}function rb(e){let{onNavigate:t,...r}=e,[a,f]=o.useState(!1),[p,m]=o.useState(!1),[h,v]=o.useState(null),[g,b]=o.useState(0);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(z.Sidebar,{collapsible:"icon",...r,children:[(0,n.jsx)(z.SidebarHeader,{children:(0,n.jsx)(t4,{onAddBackend:()=>{f(!0)},onEditBackend:e=>{v(e),m(!0)},refreshTrigger:g})}),(0,n.jsxs)(z.SidebarContent,{children:[(0,n.jsx)(L,{items:[{title:"Dashboard",url:"#",icon:u,isActive:!0,onClick:()=>null==t?void 0:t("dashboard")},{title:"Targets",url:"#",icon:d,onClick:()=>null==t?void 0:t("targets"),items:[{title:"Manage Targets",url:"#",onClick:()=>null==t?void 0:t("targets")},{title:"Target Groups",url:"#"}]},{title:"Scans",url:"#",icon:i},{title:"Vulnerabilities",url:"#",icon:c},{title:"Reports",url:"#",icon:l}],onNavigate:t}),(0,n.jsx)(tG,{projects:[{name:"Project A",url:"#",icon:s},{name:"Project B",url:"#",icon:s},{name:"Project C",url:"#",icon:s}]})]}),(0,n.jsx)(z.SidebarFooter,{children:(0,n.jsx)(tQ,{})}),(0,n.jsx)(z.SidebarRail,{})]}),(0,n.jsx)(rv,{open:a,onOpenChange:f,onBackendAdded:()=>{b(e=>e+1)}}),(0,n.jsx)(rg,{open:p,onOpenChange:m,backend:h,onBackendUpdated:()=>{b(e=>e+1)},onBackendDeleted:()=>{b(e=>e+1)}})]})}!function(e){class t{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new t(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"id",void 0),rm(this,"name",void 0),rm(this,"url",void 0),rm(this,"api_key",void 0),rm(this,"is_active",void 0),rm(this,"created_at",void 0),rm(this,"updated_at",void 0),rm(this,"last_tested",void 0),rm(this,"status",void 0),rm(this,"latency",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.id=e.id,this.name=e.name,this.url=e.url,this.api_key=e.api_key,this.is_active=e.is_active,this.created_at=this.convertValues(e.created_at,null),this.updated_at=this.convertValues(e.updated_at,null),this.last_tested=this.convertValues(e.last_tested,null),this.status=e.status,this.latency=e.latency}}e.Backend=t;class r{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new r(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"success",void 0),rm(this,"message",void 0),rm(this,"status",void 0),rm(this,"latency",void 0),rm(this,"version",void 0),rm(this,"tested_at",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.success=e.success,this.message=e.message,this.status=e.status,this.latency=e.latency,this.version=e.version,this.tested_at=this.convertValues(e.tested_at,null)}}e.BackendConnectionTest=r;class n{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new n(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"backends",void 0),rm(this,"count",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.backends=this.convertValues(e.backends,t),this.count=e.count}}e.BackendList=n;class o{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new o(e)}constructor(e={}){rm(this,"auto_connect",void 0),rm(this,"check_interval",void 0),rm(this,"theme",void 0),rm(this,"language",void 0),rm(this,"log_level",void 0),rm(this,"max_retries",void 0),rm(this,"connection_timeout",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.auto_connect=e.auto_connect,this.check_interval=e.check_interval,this.theme=e.theme,this.language=e.language,this.log_level=e.log_level,this.max_retries=e.max_retries,this.connection_timeout=e.connection_timeout}}e.Settings=o;class a{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new a(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"version",void 0),rm(this,"backends",void 0),rm(this,"settings",void 0),rm(this,"last_save",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.version=e.version,this.backends=this.convertValues(e.backends,t),this.settings=this.convertValues(e.settings,o),this.last_save=this.convertValues(e.last_save,null)}}e.Config=a;class i{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new i(e)}constructor(e={}){rm(this,"auth",void 0),rm(this,"scan",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.auth=e.auth,this.scan=e.scan}}e.DefaultOverrides=i;class s{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new s(e)}constructor(e={}){rm(this,"rel",void 0),rm(this,"href",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.rel=e.rel,this.href=e.href}}e.Link=s;class l{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new l(e)}constructor(e={}){rm(this,"name",void 0),rm(this,"url",void 0),rm(this,"api_key",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.name=e.name,this.url=e.url,this.api_key=e.api_key}}e.NewBackendRequest=l;class c{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new c(e)}constructor(e={}){rm(this,"address",void 0),rm(this,"description",void 0),rm(this,"criticality",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.address=e.address,this.description=e.description,this.criticality=e.criticality}}e.NewTargetRequest=c;class d{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new d(e)}constructor(e={}){rm(this,"url",void 0),rm(this,"content",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.url=e.url,this.content=e.content}}e.ScanAuthorization=d;class u{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new u(e)}constructor(e={}){rm(this,"critical",void 0),rm(this,"high",void 0),rm(this,"medium",void 0),rm(this,"low",void 0),rm(this,"info",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.critical=e.critical,this.high=e.high,this.medium=e.medium,this.low=e.low,this.info=e.info}}e.SeverityCounts=u;class f{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new f(e)}constructor(e={}){rm(this,"agent_id",void 0),rm(this,"name",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.agent_id=e.agent_id,this.name=e.name}}e.TargetAgent=f;class p{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new p(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"address",void 0),rm(this,"description",void 0),rm(this,"type",void 0),rm(this,"criticality",void 0),rm(this,"fqdn_status",void 0),rm(this,"fqdn_tm_hash",void 0),rm(this,"deleted_at",void 0),rm(this,"fqdn",void 0),rm(this,"fqdn_hash",void 0),rm(this,"default_scanning_profile_id",void 0),rm(this,"agents",void 0),rm(this,"default_overrides",void 0),rm(this,"target_id",void 0),rm(this,"scan_authorization",void 0),rm(this,"continuous_mode",void 0),rm(this,"last_scan_date",void 0),rm(this,"last_scan_id",void 0),rm(this,"last_scan_session_id",void 0),rm(this,"last_scan_session_status",void 0),rm(this,"severity_counts",void 0),rm(this,"threat",void 0),rm(this,"links",void 0),rm(this,"manual_intervention",void 0),rm(this,"verification",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.address=e.address,this.description=e.description,this.type=e.type,this.criticality=e.criticality,this.fqdn_status=e.fqdn_status,this.fqdn_tm_hash=e.fqdn_tm_hash,this.deleted_at=e.deleted_at,this.fqdn=e.fqdn,this.fqdn_hash=e.fqdn_hash,this.default_scanning_profile_id=e.default_scanning_profile_id,this.agents=this.convertValues(e.agents,f),this.default_overrides=this.convertValues(e.default_overrides,i),this.target_id=e.target_id,this.scan_authorization=this.convertValues(e.scan_authorization,d),this.continuous_mode=e.continuous_mode,this.last_scan_date=e.last_scan_date,this.last_scan_id=e.last_scan_id,this.last_scan_session_id=e.last_scan_session_id,this.last_scan_session_status=e.last_scan_session_status,this.severity_counts=this.convertValues(e.severity_counts,u),this.threat=e.threat,this.links=this.convertValues(e.links,s),this.manual_intervention=e.manual_intervention,this.verification=e.verification}}e.TargetItemResponse=p;class m{static createFrom(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new m(e)}convertValues(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return e;if(e.slice&&e.map)return e.map(e=>this.convertValues(e,t));if("object"==typeof e){if(r){for(let r of Object.keys(e))e[r]=new t(e[r]);return e}return new t(e)}return e}constructor(e={}){rm(this,"targets",void 0),rm(this,"count",void 0),"string"==typeof e&&(e=JSON.parse(e)),this.targets=this.convertValues(e.targets,p),this.count=e.count}}e.TargetList=m}(r||(r={}))}]);