"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, Trash2 } from "lucide-react"
import { UpdateBackend, DeleteBackend } from "@/wailsjs/go/main/App"
import { main } from "@/wailsjs/go/models"
import { ConnectionTestResult } from "./connection-test-result"

interface EditBackendDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  backend: main.Backend | null
  onBackendUpdated?: () => void
  onBackendDeleted?: () => void
}

export function EditBackendDialog({ 
  open, 
  onOpenChange, 
  backend, 
  onBackendUpdated,
  onBackendDeleted 
}: EditBackendDialogProps) {
  const [formData, setFormData] = React.useState({
    name: "",
    url: "",
    api_key: "",
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [isDeleting, setIsDeleting] = React.useState(false)
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  // Update form data when backend changes or dialog opens
  React.useEffect(() => {
    if (backend && open) {
      setFormData({
        name: backend.name || "",
        url: backend.url || "",
        api_key: backend.api_key || "",
      })
      // Clear any previous errors
      setErrors({})
    }
  }, [backend, open])

  const resetForm = () => {
    setFormData({
      name: "",
      url: "",
      api_key: "",
    })
    setErrors({})
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Backend name is required"
    }
    
    if (!formData.url.trim()) {
      newErrors.url = "Backend URL is required"
    } else {
      // Basic URL validation
      try {
        const url = formData.url.startsWith('http') ? formData.url : `https://${formData.url}`
        new URL(url)
      } catch {
        newErrors.url = "Please enter a valid URL"
      }
    }
    
    if (!formData.api_key.trim()) {
      newErrors.api_key = "API Key is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!backend || !validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      await UpdateBackend(backend.id, formData)
      onBackendUpdated?.()
      onOpenChange(false)
    } catch (error) {
      setErrors({ submit: `Failed to update backend: ${error}` })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!backend) return

    if (!confirm(`Are you sure you want to delete &quot;${backend.name}&quot;? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)

    try {
      await DeleteBackend(backend.id)
      onBackendDeleted?.()
      onOpenChange(false)
    } catch (error) {
      setErrors({ delete: `Failed to delete backend: ${error}` })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    // Prevent closing dialog while deleting
    if (!newOpen && isDeleting) {
      return
    }

    onOpenChange(newOpen)
    // Only reset form when closing, and do it after the dialog closes
    if (!newOpen) {
      setTimeout(() => {
        resetForm()
      }, 100)
    }
  }

  if (!backend) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Backend</DialogTitle>
          <DialogDescription>
            Update the configuration for &quot;{backend.name}&quot;. You can test the connection after making changes.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="edit-name">Backend Name</Label>
            <Input
              id="edit-name"
              placeholder="e.g., Production Scanner"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-url">Backend URL</Label>
            <Input
              id="edit-url"
              placeholder="https://scanner.example.com:3443"
              value={formData.url}
              onChange={(e) => handleInputChange("url", e.target.value)}
              className={errors.url ? "border-red-500" : ""}
            />
            {errors.url && <p className="text-sm text-red-500">{errors.url}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-api_key">API Key</Label>
            <Input
              id="edit-api_key"
              type="password"
              placeholder="Enter your API key"
              value={formData.api_key}
              onChange={(e) => handleInputChange("api_key", e.target.value)}
              className={errors.api_key ? "border-red-500" : ""}
            />
            {errors.api_key && <p className="text-sm text-red-500">{errors.api_key}</p>}
          </div>



          {/* Test Connection Section */}
          <ConnectionTestResult
            formData={formData}
            disabled={isLoading || isDeleting}
          />

          {errors.submit && (
            <p className="text-sm text-red-500">{errors.submit}</p>
          )}
          {errors.delete && (
            <p className="text-sm text-red-500">{errors.delete}</p>
          )}
        </form>

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading || isDeleting}
            className="mr-auto"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </>
            )}
          </Button>
          
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isLoading || isDeleting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading || isDeleting || !formData.name || !formData.url || !formData.api_key}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Backend"
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
