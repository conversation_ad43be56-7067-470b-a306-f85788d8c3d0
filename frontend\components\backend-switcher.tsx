"use client"

import * as React from "react"
import { ChevronsUpDown, Plus, Server, Pencil} from "lucide-react"
import { GetBackends, GetActiveBackend, SetActiveBackend } from "@/wailsjs/go/main/App"
import { main } from "@/wailsjs/go/models"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

interface BackendSwitcherProps {
  onAddBackend?: () => void
  onEditBackend?: (backend: main.Backend) => void
  refreshTrigger?: number // Add this to trigger refresh from parent
}

export function BackendSwitcher({ onAddBackend, onEditBackend, refreshTrigger }: BackendSwitcherProps) {
  const { isMobile } = useSidebar()
  const [backends, setBackends] = React.useState<main.Backend[]>([])
  const [activeBackend, setActiveBackend] = React.useState<main.Backend | null>(null)
  const [loading, setLoading] = React.useState(true)

  // Load backends on component mount and when refreshTrigger changes
  React.useEffect(() => {
    loadBackends()
  }, [refreshTrigger])

  // Auto-refresh only latency data every 2 seconds
  React.useEffect(() => {
    const interval = setInterval(async () => {
      try {
        // Only refresh backend data, don't reload everything
        const backendList = await GetBackends()
        setBackends(backendList.backends || [])

        // Update active backend if it exists
        setActiveBackend(current => {
          if (current) {
            const updatedActiveBackend = backendList.backends?.find(b => b.id === current.id)
            return updatedActiveBackend || current
          }
          return current
        })
      } catch (error) {
        // Silently fail to avoid disrupting user experience
        console.debug("Failed to refresh latency data:", error)
      }
    }, 2000) // Refresh every 2 seconds

    return () => clearInterval(interval)
  }, []) // Empty dependency array - using functional updates to avoid dependencies

  const loadBackends = async () => {
    try {
      setLoading(true)
      const backendList = await GetBackends()
      setBackends(backendList.backends || [])

      // Try to get active backend
      try {
        const active = await GetActiveBackend()
        setActiveBackend(active)
      } catch {
        // No active backend set, use first one if available
        if (backendList.backends && backendList.backends.length > 0) {
          setActiveBackend(backendList.backends[0])
        } else {
          // No backends available, clear active backend
          setActiveBackend(null)
        }
      }
    } catch (error) {
      console.error("Failed to load backends:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleBackendSelect = async (backend: main.Backend) => {
    try {
      await SetActiveBackend(backend.id)
      setActiveBackend(backend)
    } catch (error) {
      console.error("Failed to set active backend:", error)
    }
  }

  const getStatusDisplay = (backend: main.Backend) => {
    if (backend.status === "connected" && backend.latency >= 0) {
      // Show latency with color coding
      if (backend.latency < 100) {
        return { text: `${backend.latency}ms`, color: "text-green-600" }
      } else if (backend.latency < 500) {
        return { text: `${backend.latency}ms`, color: "text-yellow-600" }
      } else {
        return { text: `${backend.latency}ms`, color: "text-red-600" }
      }
    } else if (backend.status === "error") {
      return { text: "Error", color: "text-red-600" }
    } else {
      return { text: "Testing...", color: "text-gray-500" }
    }
  }

  if (loading) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" disabled>
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <Server className="size-4" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">Loading...</span>
              <span className="truncate text-xs">Please wait</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  if (!activeBackend && backends.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            onClick={onAddBackend}
            className="cursor-pointer"
          >
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <Plus className="size-4" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">Add Backend</span>
              <span className="truncate text-xs">No backends configured</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  if (!activeBackend) {
    return null
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Server className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{activeBackend.name}</span>
                <span className={`truncate text-xs ${getStatusDisplay(activeBackend).color}`}>
                  {getStatusDisplay(activeBackend).text}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Backends
            </DropdownMenuLabel>
            {backends.map((backend, index) => (
              <DropdownMenuItem
                key={backend.id}
                onClick={() => handleBackendSelect(backend)}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  <Server className="size-3.5 shrink-0" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{backend.name}</div>
                  <div className={`text-xs ${getStatusDisplay(backend).color}`}>
                    {getStatusDisplay(backend).text}
                  </div>
                </div>
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            {activeBackend && (
              <DropdownMenuItem
                className="gap-2 p-2"
                onClick={() => onEditBackend?.(activeBackend)}
              >
                <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                  <Pencil className="size-4" />
                </div>
                <div className="text-muted-foreground font-medium">Edit Current Backend</div>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              className="gap-2 p-2"
              onClick={onAddBackend}
            >
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">Add Backend</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}