package main

import (
	"testing"
)

func TestNormalizeURL(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
		name     string
	}{
		{
			input:    "scanner.example.com:3443",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "Host with port only",
		},
		{
			input:    "https://scanner.example.com:3443",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "HTTPS host with port",
		},
		{
			input:    "http://scanner.example.com:3443",
			expected: "http://scanner.example.com:3443/api/v1",
			name:     "HTTP host with port",
		},
		{
			input:    "https://scanner.example.com:3443/",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "HTTPS host with trailing slash",
		},
		{
			input:    "https://scanner.example.com:3443/api/v1",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "Complete API URL",
		},
		{
			input:    "https://scanner.example.com:3443/api/v1/",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "Complete API URL with trailing slash",
		},
		{
			input:    "https://scanner.example.com:3443/api",
			expected: "https://scanner.example.com:3443/api/v1",
			name:     "API path without version",
		},
		{
			input:    "scanner.example.com",
			expected: "https://scanner.example.com/api/v1",
			name:     "Host only without port",
		},
		{
			input:    "https://scanner.example.com",
			expected: "https://scanner.example.com/api/v1",
			name:     "HTTPS host without port",
		},
		{
			input:    "*************:3443",
			expected: "https://*************:3443/api/v1",
			name:     "IP address with port",
		},
		{
			input:    "https://*************:3443/api/v1/targets",
			expected: "https://*************:3443/api/v1",
			name:     "URL with specific endpoint path",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := normalizeURL(tc.input)
			if result != tc.expected {
				t.Errorf("normalizeURL(%q) = %q, expected %q", tc.input, result, tc.expected)
			}
		})
	}
}

func TestBackendWithNormalizedURL(t *testing.T) {
	// Create a backend manager for testing
	bm := NewBackendManager()
	defer bm.StopPeriodicTesting() // Stop the periodic testing to avoid goroutine leaks

	testCases := []struct {
		inputURL    string
		expectedURL string
		name        string
	}{
		{
			inputURL:    "scanner.example.com:3443",
			expectedURL: "https://scanner.example.com:3443/api/v1",
			name:        "Host with port",
		},
		{
			inputURL:    "https://scanner.example.com:3443/api/v1/",
			expectedURL: "https://scanner.example.com:3443/api/v1",
			name:        "Complete URL with trailing slash",
		},
		{
			inputURL:    "http://*************:3443/api",
			expectedURL: "http://*************:3443/api/v1",
			name:        "HTTP with API path",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req := NewBackendRequest{
				Name:   "Test Backend",
				URL:    tc.inputURL,
				APIKey: "test-api-key",
			}

			backend, err := bm.AddBackend(req)
			if err != nil {
				t.Fatalf("Failed to add backend: %v", err)
			}

			if backend.URL != tc.expectedURL {
				t.Errorf("Backend URL normalization failed. Input: %q, Expected: %q, Got: %q",
					tc.inputURL, tc.expectedURL, backend.URL)
			}

			// Clean up
			err = bm.DeleteBackend(backend.ID)
			if err != nil {
				t.Errorf("Failed to clean up backend: %v", err)
			}
		})
	}
}

func TestBackendLatencyField(t *testing.T) {
	// Create a backend manager for testing
	bm := NewBackendManager()
	defer bm.StopPeriodicTesting()

	req := NewBackendRequest{
		Name:   "Latency Test Backend",
		URL:    "https://test.example.com:3443",
		APIKey: "test-api-key",
	}

	backend, err := bm.AddBackend(req)
	if err != nil {
		t.Fatalf("Failed to add backend: %v", err)
	}

	// Check initial latency value
	if backend.Latency != -1 {
		t.Errorf("Expected initial latency to be -1, got %d", backend.Latency)
	}

	// Test connection (this will fail but should update latency)
	test := bm.TestBackendConnection(backend)
	
	// Update backend status with test results
	err = bm.UpdateBackendStatus(backend.ID, test)
	if err != nil {
		t.Fatalf("Failed to update backend status: %v", err)
	}

	// Get updated backend
	updatedBackend, err := bm.GetBackend(backend.ID)
	if err != nil {
		t.Fatalf("Failed to get updated backend: %v", err)
	}

	// Check that latency was updated
	if updatedBackend.Latency == -1 {
		t.Error("Latency should have been updated from initial value of -1")
	}

	if updatedBackend.Latency != test.Latency {
		t.Errorf("Expected latency %d, got %d", test.Latency, updatedBackend.Latency)
	}

	t.Logf("Backend latency updated to: %dms", updatedBackend.Latency)

	// Clean up
	err = bm.DeleteBackend(backend.ID)
	if err != nil {
		t.Errorf("Failed to clean up backend: %v", err)
	}
}
