module.exports=[79168,(a,b,c)=>{b.exports=a.r(5515)},50852,(a,b,c)=>{"use strict";function d(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(d=function(a){return a?c:b})(a)}c._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=d(b);if(c&&c.has(a))return c.get(a);var e={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(e,g,h):e[g]=a[g]}return e.default=a,c&&c.set(a,e),e}},75974,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}});let d=a.r(27669),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},94002,(a,b,c)=>{"use strict";b.exports=a.r(1951).vendored.contexts.AmpContext},40175,(a,b,c)=>{"use strict";b.exports=a.r(1951).vendored.contexts.HeadManagerContext},13680,(a,b,c)=>{"use strict";function d(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isInAmpMode",{enumerable:!0,get:function(){return d}})},42939,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},11276,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return p},defaultHead:function(){return l}});let d=a.r(4550),e=a.r(50852),f=a.r(8171),g=e._(a.r(27669)),h=d._(a.r(75974)),i=a.r(94002),j=a.r(40175),k=a.r(13680);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}a.r(42939);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},23313,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{NEXT_REQUEST_META:function(){return d},addRequestMeta:function(){return g},getRequestMeta:function(){return e},removeRequestMeta:function(){return h},setRequestMeta:function(){return f}});let d=Symbol.for("NextInternalRequestMeta");function e(a,b){let c=a[d]||{};return"string"==typeof b?c[b]:c}function f(a,b){return a[d]=b,b}function g(a,b,c){let d=e(a);return d[b]=c,f(a,d)}function h(a,b){let c=e(a);return delete c[b],f(a,c)}},54162,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return k}});let d=a.r(4550),e=a.r(8171),f=d._(a.r(27669)),g=d._(a.r(11276)),h={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function i(b){let c,{req:d,res:e,err:f}=b,g=e&&e.statusCode?e.statusCode:f?f.statusCode:404;if(d){let{getRequestMeta:b}=a.r(23313),e=b(d,"initURL");e&&(c=new URL(e).hostname)}return{statusCode:g,hostname:c}}let j={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class k extends f.default.Component{render(){let{statusCode:a,withDarkMode:b=!0}=this.props,c=this.props.title||h[a]||"An unexpected error has occurred";return(0,e.jsxs)("div",{style:j.error,children:[(0,e.jsx)(g.default,{children:(0,e.jsx)("title",{children:a?a+": "+c:"Application error: a client-side exception has occurred"})}),(0,e.jsxs)("div",{style:j.desc,children:[(0,e.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(b?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),a?(0,e.jsx)("h1",{className:"next-error-h1",style:j.h1,children:a}):null,(0,e.jsx)("div",{style:j.wrap,children:(0,e.jsxs)("h2",{style:j.h2,children:[this.props.title||a?c:(0,e.jsxs)(e.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,e.jsxs)(e.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}k.displayName="ErrorPage",k.getInitialProps=i,k.origGetInitialProps=i,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},97696,(a,b,c)=>{b.exports=a.r(54162)},84046,(a,b,c)=>{"use strict";var d=Object.defineProperty,e=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,g=Object.prototype.hasOwnProperty,h={};function i(a){var b;let c=["path"in a&&a.path&&`Path=${a.path}`,"expires"in a&&(a.expires||0===a.expires)&&`Expires=${("number"==typeof a.expires?new Date(a.expires):a.expires).toUTCString()}`,"maxAge"in a&&"number"==typeof a.maxAge&&`Max-Age=${a.maxAge}`,"domain"in a&&a.domain&&`Domain=${a.domain}`,"secure"in a&&a.secure&&"Secure","httpOnly"in a&&a.httpOnly&&"HttpOnly","sameSite"in a&&a.sameSite&&`SameSite=${a.sameSite}`,"partitioned"in a&&a.partitioned&&"Partitioned","priority"in a&&a.priority&&`Priority=${a.priority}`].filter(Boolean),d=`${a.name}=${encodeURIComponent(null!=(b=a.value)?b:"")}`;return 0===c.length?d:`${d}; ${c.join("; ")}`}function j(a){let b=new Map;for(let c of a.split(/; */)){if(!c)continue;let a=c.indexOf("=");if(-1===a){b.set(c,"true");continue}let[d,e]=[c.slice(0,a),c.slice(a+1)];try{b.set(d,decodeURIComponent(null!=e?e:"true"))}catch{}}return b}function k(a){if(!a)return;let[[b,c],...d]=j(a),{domain:e,expires:f,httponly:g,maxage:h,path:i,samesite:k,secure:n,partitioned:o,priority:p}=Object.fromEntries(d.map(([a,b])=>[a.toLowerCase().replace(/-/g,""),b]));{var q,r,s={name:b,value:decodeURIComponent(c),domain:e,...f&&{expires:new Date(f)},...g&&{httpOnly:!0},..."string"==typeof h&&{maxAge:Number(h)},path:i,...k&&{sameSite:l.includes(q=(q=k).toLowerCase())?q:void 0},...n&&{secure:!0},...p&&{priority:m.includes(r=(r=p).toLowerCase())?r:void 0},...o&&{partitioned:!0}};let a={};for(let b in s)s[b]&&(a[b]=s[b]);return a}}((a,b)=>{for(var c in b)d(a,c,{get:b[c],enumerable:!0})})(h,{RequestCookies:()=>n,ResponseCookies:()=>o,parseCookie:()=>j,parseSetCookie:()=>k,stringifyCookie:()=>i}),b.exports=((a,b,c,h)=>{if(b&&"object"==typeof b||"function"==typeof b)for(let i of f(b))g.call(a,i)||i===c||d(a,i,{get:()=>b[i],enumerable:!(h=e(b,i))||h.enumerable});return a})(d({},"__esModule",{value:!0}),h);var l=["strict","lax","none"],m=["low","medium","high"],n=class{constructor(a){this._parsed=new Map,this._headers=a;let b=a.get("cookie");if(b)for(let[a,c]of j(b))this._parsed.set(a,{name:a,value:c})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed);if(!a.length)return c.map(([a,b])=>b);let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(([a])=>a===d).map(([a,b])=>b)}has(a){return this._parsed.has(a)}set(...a){let[b,c]=1===a.length?[a[0].name,a[0].value]:a,d=this._parsed;return d.set(b,{name:b,value:c}),this._headers.set("cookie",Array.from(d).map(([a,b])=>i(b)).join("; ")),this}delete(a){let b=this._parsed,c=Array.isArray(a)?a.map(a=>b.delete(a)):b.delete(a);return this._headers.set("cookie",Array.from(b).map(([a,b])=>i(b)).join("; ")),c}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a=>`${a.name}=${encodeURIComponent(a.value)}`).join("; ")}},o=class{constructor(a){var b,c,d;this._parsed=new Map,this._headers=a;let e=null!=(d=null!=(c=null==(b=a.getSetCookie)?void 0:b.call(a))?c:a.get("set-cookie"))?d:[];for(let a of Array.isArray(e)?e:function(a){if(!a)return[];var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}(e)){let b=k(a);b&&this._parsed.set(b.name,b)}}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed.values());if(!a.length)return c;let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(a=>a.name===d)}has(a){return this._parsed.has(a)}set(...a){let[b,c,d]=1===a.length?[a[0].name,a[0].value,a[0]]:a,e=this._parsed;return e.set(b,function(a={name:"",value:""}){return"number"==typeof a.expires&&(a.expires=new Date(a.expires)),a.maxAge&&(a.expires=new Date(Date.now()+1e3*a.maxAge)),(null===a.path||void 0===a.path)&&(a.path="/"),a}({name:b,value:c,...d})),function(a,b){for(let[,c]of(b.delete("set-cookie"),a)){let a=i(c);b.append("set-cookie",a)}}(e,this._headers),this}delete(...a){let[b,c]="string"==typeof a[0]?[a[0]]:[a[0].name,a[0]];return this.set({...c,name:b,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},92396,(a,b,c)=>{(()=>{"use strict";var a={695:a=>{var b=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;function c(a){var b=a&&Date.parse(a);return"number"==typeof b?b:NaN}a.exports=function(a,d){var e=a["if-modified-since"],f=a["if-none-match"];if(!e&&!f)return!1;var g=a["cache-control"];if(g&&b.test(g))return!1;if(f&&"*"!==f){var h=d.etag;if(!h)return!1;for(var i=!0,j=function(a){for(var b=0,c=[],d=0,e=0,f=a.length;e<f;e++)switch(a.charCodeAt(e)){case 32:d===b&&(d=b=e+1);break;case 44:c.push(a.substring(d,b)),d=b=e+1;break;default:b=e+1}return c.push(a.substring(d,b)),c}(f),k=0;k<j.length;k++){var l=j[k];if(l===h||l==="W/"+h||"W/"+l===h){i=!1;break}}if(i)return!1}if(e){var m=d["last-modified"];if(!m||!(c(m)<=c(e)))return!1}return!0}}},c={};function d(b){var e=c[b];if(void 0!==e)return e.exports;var f=c[b]={exports:{}},g=!0;try{a[b](f,f.exports,d),g=!1}finally{g&&delete c[b]}return f.exports}d.ab="/ROOT/node_modules/next/dist/compiled/fresh/",b.exports=d(695)})()},26644,a=>{"use strict";let b;a.s(["config",()=>aF,"default",()=>aB,"getServerSideProps",()=>aE,"getStaticPaths",()=>aD,"getStaticProps",()=>aC,"handler",()=>aN,"reportWebVitals",()=>aG,"routeModule",()=>aM,"unstable_getServerProps",()=>aK,"unstable_getServerSideProps",()=>aL,"unstable_getStaticParams",()=>aJ,"unstable_getStaticPaths",()=>aI,"unstable_getStaticProps",()=>aH],26644);var c=a.i(79168),d=function(a){return a.PAGES="PAGES",a.PAGES_API="PAGES_API",a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.IMAGE="IMAGE",a}({});function e(a,b){return b in a?a[b]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,b)):"function"==typeof a&&"default"===b?a:void 0}var f=a.i(39141),g=a.i(6555),h=a.i(97696),i=function(a){return a.handleRequest="BaseServer.handleRequest",a.run="BaseServer.run",a.pipe="BaseServer.pipe",a.getStaticHTML="BaseServer.getStaticHTML",a.render="BaseServer.render",a.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",a.renderToResponse="BaseServer.renderToResponse",a.renderToHTML="BaseServer.renderToHTML",a.renderError="BaseServer.renderError",a.renderErrorToResponse="BaseServer.renderErrorToResponse",a.renderErrorToHTML="BaseServer.renderErrorToHTML",a.render404="BaseServer.render404",a}(i||{}),j=function(a){return a.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",a.loadComponents="LoadComponents.loadComponents",a}(j||{}),k=function(a){return a.getRequestHandler="NextServer.getRequestHandler",a.getServer="NextServer.getServer",a.getServerRequestHandler="NextServer.getServerRequestHandler",a.createServer="createServer.createServer",a}(k||{}),l=function(a){return a.compression="NextNodeServer.compression",a.getBuildId="NextNodeServer.getBuildId",a.createComponentTree="NextNodeServer.createComponentTree",a.clientComponentLoading="NextNodeServer.clientComponentLoading",a.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",a.generateStaticRoutes="NextNodeServer.generateStaticRoutes",a.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",a.generatePublicRoutes="NextNodeServer.generatePublicRoutes",a.generateImageRoutes="NextNodeServer.generateImageRoutes.route",a.sendRenderResult="NextNodeServer.sendRenderResult",a.proxyRequest="NextNodeServer.proxyRequest",a.runApi="NextNodeServer.runApi",a.render="NextNodeServer.render",a.renderHTML="NextNodeServer.renderHTML",a.imageOptimizer="NextNodeServer.imageOptimizer",a.getPagePath="NextNodeServer.getPagePath",a.getRoutesManifest="NextNodeServer.getRoutesManifest",a.findPageComponents="NextNodeServer.findPageComponents",a.getFontManifest="NextNodeServer.getFontManifest",a.getServerComponentManifest="NextNodeServer.getServerComponentManifest",a.getRequestHandler="NextNodeServer.getRequestHandler",a.renderToHTML="NextNodeServer.renderToHTML",a.renderError="NextNodeServer.renderError",a.renderErrorToHTML="NextNodeServer.renderErrorToHTML",a.render404="NextNodeServer.render404",a.startResponse="NextNodeServer.startResponse",a.route="route",a.onProxyReq="onProxyReq",a.apiResolver="apiResolver",a.internalFetch="internalFetch",a}(l||{}),m=function(a){return a.startServer="startServer.startServer",a}(m||{}),n=function(a){return a.getServerSideProps="Render.getServerSideProps",a.getStaticProps="Render.getStaticProps",a.renderToString="Render.renderToString",a.renderDocument="Render.renderDocument",a.createBodyResult="Render.createBodyResult",a}(n||{}),o=function(a){return a.renderToString="AppRender.renderToString",a.renderToReadableStream="AppRender.renderToReadableStream",a.getBodyResult="AppRender.getBodyResult",a.fetch="AppRender.fetch",a}(o||{}),p=function(a){return a.executeRoute="Router.executeRoute",a}(p||{}),q=function(a){return a.runHandler="Node.runHandler",a}(q||{}),r=function(a){return a.runHandler="AppRouteRouteHandlers.runHandler",a}(r||{}),s=function(a){return a.generateMetadata="ResolveMetadata.generateMetadata",a.generateViewport="ResolveMetadata.generateViewport",a}(s||{}),t=function(a){return a.execute="Middleware.execute",a}(t||{});let u=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],v=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];try{b=a.r(70406)}catch(c){b=a.r(87739)}let{context:w,propagation:x,trace:y,SpanStatusCode:z,SpanKind:A,ROOT_CONTEXT:B}=b;class C extends Error{constructor(a,b){super(),this.bubble=a,this.result=b}}let D=(a,b)=>{(function(a){return"object"==typeof a&&null!==a&&a instanceof C})(b)&&b.bubble?a.setAttribute("next.bubble",!0):(b&&(a.recordException(b),a.setAttribute("error.type",b.name)),a.setStatus({code:z.ERROR,message:null==b?void 0:b.message})),a.end()},E=new Map,F=b.createContextKey("next.rootSpanId"),G=0,H={set(a,b,c){a.push({key:b,value:c})}};class I{getTracerInstance(){return y.getTracer("next.js","0.0.1")}getContext(){return w}getTracePropagationData(){let a=w.active(),b=[];return x.inject(a,b,H),b}getActiveScopeSpan(){return y.getSpan(null==w?void 0:w.active())}withPropagatedContext(a,b,c){let d=w.active();if(y.getSpanContext(d))return b();let e=x.extract(d,a,c);return w.with(e,b)}trace(...a){var b;let[c,d,e]=a,{fn:f,options:g}="function"==typeof d?{fn:d,options:{}}:{fn:e,options:{...d}},h=g.spanName??c;if(!u.includes(c)&&"1"!==process.env.NEXT_OTEL_VERBOSE||g.hideSpan)return f();let i=this.getSpanContext((null==g?void 0:g.parentSpan)??this.getActiveScopeSpan()),j=!1;i?(null==(b=y.getSpanContext(i))?void 0:b.isRemote)&&(j=!0):(i=(null==w?void 0:w.active())??B,j=!0);let k=G++;return g.attributes={"next.span_name":h,"next.span_type":c,...g.attributes},w.with(i.setValue(F,k),()=>this.getTracerInstance().startActiveSpan(h,g,a=>{let b="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,d=()=>{E.delete(k),b&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&v.includes(c||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(c.split(".").pop()||"").replace(/[A-Z]/g,a=>"-"+a.toLowerCase())}`,{start:b,end:performance.now()})};j&&E.set(k,new Map(Object.entries(g.attributes??{})));try{if(f.length>1)return f(a,b=>D(a,b));let b=f(a);if(null!==b&&"object"==typeof b&&"then"in b&&"function"==typeof b.then)return b.then(b=>(a.end(),b)).catch(b=>{throw D(a,b),b}).finally(d);return a.end(),d(),b}catch(b){throw D(a,b),d(),b}}))}wrap(...a){let b=this,[c,d,e]=3===a.length?a:[a[0],{},a[1]];return u.includes(c)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let a=d;"function"==typeof a&&"function"==typeof e&&(a=a.apply(this,arguments));let f=arguments.length-1,g=arguments[f];if("function"!=typeof g)return b.trace(c,a,()=>e.apply(this,arguments));{let d=b.getContext().bind(w.active(),g);return b.trace(c,a,(a,b)=>(arguments[f]=function(a){return null==b||b(a),d.apply(this,arguments)},e.apply(this,arguments)))}}:e}startSpan(...a){let[b,c]=a,d=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(b,c,d)}getSpanContext(a){return a?y.setSpan(w.active(),a):void 0}getRootSpanAttributes(){let a=w.active().getValue(F);return E.get(a)}setRootSpanAttribute(a,b){let c=w.active().getValue(F),d=E.get(c);d&&d.set(a,b)}}let J=(()=>{let a=new I;return()=>a})();function K(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}let L=/https?|ftp|gopher|file/;function M(a){let{auth:b,hostname:c}=a,d=a.protocol||"",e=a.pathname||"",f=a.hash||"",g=a.query||"",h=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?h=b+a.host:c&&(h=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(h+=":"+a.port)),g&&"object"==typeof g&&(g=String(function(a){let b=new URLSearchParams;for(let[c,d]of Object.entries(a))if(Array.isArray(d))for(let a of d)b.append(c,K(a));else b.set(c,K(d));return b}(g)));let i=a.search||g&&"?"+g||"";return d&&!d.endsWith(":")&&(d+=":"),a.slashes||(!d||L.test(d))&&!1!==h?(h="//"+(h||""),e&&"/"!==e[0]&&(e="/"+e)):h||(h=""),f&&"#"!==f[0]&&(f="#"+f),i&&"?"!==i[0]&&(i="?"+i),""+d+h+(e=e.replace(/[?#]/g,encodeURIComponent))+(i=i.replace("#","%23"))+f}let N=Symbol.for("NextInternalRequestMeta");function O(a,b){let c=a[N]||{};return"string"==typeof b?c[b]:c}function P(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}function Q(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}function R(a,b){if("string"!=typeof a)return!1;let{pathname:c}=Q(a);return c===b||c.startsWith(b+"/")}class S{constructor(){let a,b;this.promise=new Promise((c,d)=>{a=c,b=d}),this.resolve=a,this.reject=b}}var T=function(a){return a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.PAGES="PAGES",a.FETCH="FETCH",a.REDIRECT="REDIRECT",a.IMAGE="IMAGE",a}({});function U(){}new Uint8Array([60,104,116,109,108]),new Uint8Array([60,98,111,100,121]),new Uint8Array([60,47,104,101,97,100,62]),new Uint8Array([60,47,98,111,100,121,62]),new Uint8Array([60,47,104,116,109,108,62]),new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62]),new Uint8Array([60,109,101,116,97,32,110,97,109,101,61,34,194,171,110,120,116,45,105,99,111,110,194,187,34]);let V=new TextEncoder;function W(a){return new ReadableStream({start(b){b.enqueue(V.encode(a)),b.close()}})}function X(a){return new ReadableStream({start(b){b.enqueue(a),b.close()}})}async function Y(a,b){let c=new TextDecoder("utf-8",{fatal:!0}),d="";for await(let e of a){if(null==b?void 0:b.aborted)return d;d+=c.decode(e,{stream:!0})}return d+c.decode()}let Z="text/html; charset=utf-8",$="application/json; charset=utf-8",_={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function aa(a){return a.replace(/\/$/,"")||"/"}function ab(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=Q(a);return""+b+c+d+e}function ac(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=Q(a);return""+c+b+d+e}({..._,GROUP:{builtinReact:[_.reactServerComponents,_.actionBrowser],serverOnly:[_.reactServerComponents,_.actionBrowser,_.instrument,_.middleware],neutralTarget:[_.apiNode,_.apiEdge],clientOnly:[_.serverSideRendering,_.appPagesBrowser],bundled:[_.reactServerComponents,_.actionBrowser,_.serverSideRendering,_.appPagesBrowser,_.shared,_.instrument,_.middleware],appPages:[_.reactServerComponents,_.serverSideRendering,_.appPagesBrowser,_.actionBrowser]}});let ad=new WeakMap;function ae(a,b){let c;if(!b)return{pathname:a};let d=ad.get(b);d||(d=b.map(a=>a.toLowerCase()),ad.set(b,d));let e=a.split("/",2);if(!e[1])return{pathname:a};let f=e[1].toLowerCase(),g=d.indexOf(f);return g<0?{pathname:a}:(c=b[g],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}let af=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function ag(a,b){return new URL(String(a).replace(af,"localhost"),b&&String(b).replace(af,"localhost"))}let ah=Symbol("NextURLInternal");class ai{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[ah]={url:ag(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,d,e;let f=function(a,b){var c,d;let{basePath:e,i18n:f,trailingSlash:g}=null!=(c=b.nextConfig)?c:{},h={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):g};e&&R(h.pathname,e)&&(h.pathname=function(a,b){if(!R(a,b))return a;let c=a.slice(b.length);return c.startsWith("/")?c:"/"+c}(h.pathname,e),h.basePath=e);let i=h.pathname;if(h.pathname.startsWith("/_next/data/")&&h.pathname.endsWith(".json")){let a=h.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");h.buildId=a[0],i="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(h.pathname=i)}if(f){let a=b.i18nProvider?b.i18nProvider.analyze(h.pathname):ae(h.pathname,f.locales);h.locale=a.detectedLocale,h.pathname=null!=(d=a.pathname)?d:h.pathname,!a.detectedLocale&&h.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(i):ae(i,f.locales)).detectedLocale&&(h.locale=a.detectedLocale)}return h}(this[ah].url.pathname,{nextConfig:this[ah].options.nextConfig,parseData:!0,i18nProvider:this[ah].options.i18nProvider}),g=function(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}(this[ah].url,this[ah].options.headers);this[ah].domainLocale=this[ah].options.i18nProvider?this[ah].options.i18nProvider.detectDomainLocale(g):function(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}(null==(b=this[ah].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,g);let h=(null==(c=this[ah].domainLocale)?void 0:c.defaultLocale)||(null==(e=this[ah].options.nextConfig)||null==(d=e.i18n)?void 0:d.defaultLocale);this[ah].url.pathname=f.pathname,this[ah].defaultLocale=h,this[ah].basePath=f.basePath??"",this[ah].buildId=f.buildId,this[ah].locale=f.locale??h,this[ah].trailingSlash=f.trailingSlash}formatPathname(){var a;let b;return b=function(a,b,c,d){if(!b||b===c)return a;let e=a.toLowerCase();return!d&&(R(e,"/api")||R(e,"/"+b.toLowerCase()))?a:ab(a,"/"+b)}((a={basePath:this[ah].basePath,buildId:this[ah].buildId,defaultLocale:this[ah].options.forceLocale?void 0:this[ah].defaultLocale,locale:this[ah].locale,pathname:this[ah].url.pathname,trailingSlash:this[ah].trailingSlash}).pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix),(a.buildId||!a.trailingSlash)&&(b=aa(b)),a.buildId&&(b=ac(ab(b,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),b=ab(b,a.basePath),!a.buildId&&a.trailingSlash?b.endsWith("/")?b:ac(b,"/"):aa(b)}formatSearch(){return this[ah].url.search}get buildId(){return this[ah].buildId}set buildId(a){this[ah].buildId=a}get locale(){return this[ah].locale??""}set locale(a){var b,c;if(!this[ah].locale||!(null==(c=this[ah].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[ah].locale=a}get defaultLocale(){return this[ah].defaultLocale}get domainLocale(){return this[ah].domainLocale}get searchParams(){return this[ah].url.searchParams}get host(){return this[ah].url.host}set host(a){this[ah].url.host=a}get hostname(){return this[ah].url.hostname}set hostname(a){this[ah].url.hostname=a}get port(){return this[ah].url.port}set port(a){this[ah].url.port=a}get protocol(){return this[ah].url.protocol}set protocol(a){this[ah].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[ah].url=ag(a),this.analyze()}get origin(){return this[ah].url.origin}get pathname(){return this[ah].url.pathname}set pathname(a){this[ah].url.pathname=a}get hash(){return this[ah].url.hash}set hash(a){this[ah].url.hash=a}get search(){return this[ah].url.search}set search(a){this[ah].url.search=a}get password(){return this[ah].url.password}set password(a){this[ah].url.password=a}get username(){return this[ah].url.username}set username(a){this[ah].url.username=a}get basePath(){return this[ah].basePath}set basePath(a){this[ah].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new ai(String(this),this[ah].options)}}a.i(84046),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let aj="ResponseAborted";class ak extends Error{constructor(...a){super(...a),this.name=aj}}let al=0,am=0,an=0;function ao(a){return(null==a?void 0:a.name)==="AbortError"||(null==a?void 0:a.name)===aj}async function ap(a,b,c){try{let{errored:d,destroyed:e}=b;if(d||e)return;let f=function(a){let b=new AbortController;return a.once("close",()=>{a.writableFinished||b.abort(new ak)}),b}(b),g=function(a,b){let c=!1,d=new S;function e(){d.resolve()}a.on("drain",e),a.once("close",()=>{a.off("drain",e),d.resolve()});let f=new S;return a.once("finish",()=>{f.resolve()}),new WritableStream({write:async b=>{if(!c){if(c=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let a=function(a={}){let b=0===al?void 0:{clientComponentLoadStart:al,clientComponentLoadTimes:am,clientComponentLoadCount:an};return a.reset&&(al=0,am=0,an=0),b}();a&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:a.clientComponentLoadStart,end:a.clientComponentLoadStart+a.clientComponentLoadTimes})}a.flushHeaders(),J().trace(l.startResponse,{spanName:"start response"},()=>void 0)}try{let c=a.write(b);"flush"in a&&"function"==typeof a.flush&&a.flush(),c||(await d.promise,d=new S)}catch(b){throw a.end(),Object.defineProperty(Error("failed to write chunk to response",{cause:b}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:!1,configurable:!0})}},abort:b=>{a.writableFinished||a.destroy(b)},close:async()=>{if(b&&await b,!a.writableFinished)return a.end(),f.promise}})}(b,c);await a.pipeTo(g,{signal:f.signal})}catch(a){if(ao(a))return;throw Object.defineProperty(Error("failed to pipe response",{cause:a}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:!1,configurable:!0})}}class aq extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}class ar{static #a=this.EMPTY=new ar(null,{metadata:{},contentType:null});static fromStatic(a,b){return new ar(a,{metadata:{},contentType:b})}constructor(a,{contentType:b,waitUntil:c,metadata:d}){this.response=a,this.contentType=b,this.metadata=d,this.waitUntil=c}assignMetadata(a){Object.assign(this.metadata,a)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(a=!1){if(null===this.response)return"";if("string"!=typeof this.response){if(!a)throw Object.defineProperty(new aq("dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E732",enumerable:!1,configurable:!0});return Y(this.readable)}return this.response}get readable(){return null===this.response?new ReadableStream({start(a){a.close()}}):"string"==typeof this.response?W(this.response):Buffer.isBuffer(this.response)?X(this.response):Array.isArray(this.response)?function(...a){if(0===a.length)return new ReadableStream({start(a){a.close()}});if(1===a.length)return a[0];let{readable:b,writable:c}=new TransformStream,d=a[0].pipeTo(c,{preventClose:!0}),e=1;for(;e<a.length-1;e++){let b=a[e];d=d.then(()=>b.pipeTo(c,{preventClose:!0}))}let f=a[e];return(d=d.then(()=>f.pipeTo(c))).catch(U),b}(...this.response):this.response}coerce(){return null===this.response?[]:"string"==typeof this.response?[W(this.response)]:Array.isArray(this.response)?this.response:Buffer.isBuffer(this.response)?[X(this.response)]:[this.response]}unshift(a){this.response=this.coerce(),this.response.unshift(a)}push(a){this.response=this.coerce(),this.response.push(a)}async pipeTo(a){try{await this.readable.pipeTo(a,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await a.close()}catch(b){if(ao(b))return void await a.abort(b);throw b}}async pipeToNodeResponse(a){await ap(this.readable,a,this.waitUntil)}}async function as(a){var b,c;return a?{isMiss:a.isMiss,isStale:a.isStale,cacheControl:a.cacheControl,value:(null==(b=a.value)?void 0:b.kind)===T.PAGES?{kind:T.PAGES,html:ar.fromStatic(a.value.html,Z),pageData:a.value.pageData,headers:a.value.headers,status:a.value.status}:(null==(c=a.value)?void 0:c.kind)===T.APP_PAGE?{kind:T.APP_PAGE,html:ar.fromStatic(a.value.html,Z),rscData:a.value.rscData,headers:a.value.headers,status:a.value.status,postponed:a.value.postponed,segmentData:a.value.segmentData}:a.value}:null}function at({revalidate:a,expire:b}){let c="number"==typeof a&&void 0!==b&&a<b?`, stale-while-revalidate=${b-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${c}`:`s-maxage=31536000${c}`}"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);var au=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({}),av=a.i(14747),aw=a.i(92396);async function ax({req:a,res:b,result:c,generateEtags:d,poweredByHeader:e,cacheControl:f}){if(b.finished||b.headersSent)return;e&&c.contentType===Z&&b.setHeader("X-Powered-By","Next.js"),f&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",at(f));let g=c.isDynamic?null:c.toUnchunkedString();if(d&&null!==g){let c=((a,b=!1)=>(b?'W/"':'"')+(a=>{let b=a.length,c=0,d=0,e=8997,f=0,g=33826,h=0,i=40164,j=0,k=52210;for(;c<b;)e^=a.charCodeAt(c++),d=435*e,f=435*g,h=435*i,j=435*k,h+=e<<8,j+=g<<8,f+=d>>>16,e=65535&d,h+=f>>>16,g=65535&f,k=j+(h>>>16)&65535,i=65535&h;return(15&k)*0x1000000000000+0x100000000*i+65536*g+(e^k>>4)})(a).toString(36)+a.length.toString(36)+'"')(g);if(c&&b.setHeader("ETag",c),(0,aw.default)(a.headers,{etag:c})&&(b.statusCode=304,b.end(),1))return}return(!b.getHeader("Content-Type")&&c.contentType&&b.setHeader("Content-Type",c.contentType),g&&b.setHeader("Content-Length",Buffer.byteLength(g)),"HEAD"===a.method)?void b.end(null):null!==g?void b.end(g):void await c.pipeToNodeResponse(b)}var ay=a.i(93695);let az=/[\w-]+-Google|Google-[\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i,aA=/Googlebot(?!-)|Googlebot$/i;az.source;let aB=e(h,"default"),aC=e(h,"getStaticProps"),aD=e(h,"getStaticPaths"),aE=e(h,"getServerSideProps"),aF=e(h,"config"),aG=e(h,"reportWebVitals"),aH=e(h,"unstable_getStaticProps"),aI=e(h,"unstable_getStaticPaths"),aJ=e(h,"unstable_getStaticParams"),aK=e(h,"unstable_getServerProps"),aL=e(h,"unstable_getServerSideProps"),aM=new c.PagesRouteModule({definition:{kind:d.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:g.default,Document:f.default},userland:h}),aN=(({srcPage:a,config:b,userland:c,routeModule:e,isFallbackError:f,getStaticPaths:g,getStaticProps:h,getServerSideProps:j})=>async function(k,l,m){var n,o,p,q;let r=a;r=r.replace(/\/index$/,"")||"/";let s=await e.prepare(k,l,{srcPage:r,multiZoneDraftMode:!1});if(!s){l.statusCode=400,l.end("Bad Request"),null==m.waitUntil||m.waitUntil.call(m,Promise.resolve());return}let{buildId:t,query:u,params:v,parsedUrl:w,originalQuery:x,originalPathname:y,buildManifest:z,fallbackBuildManifest:B,nextFontManifest:C,serverFilesManifest:D,reactLoadableManifest:E,prerenderManifest:F,isDraftMode:G,isOnDemandRevalidate:H,revalidateOnlyGenerated:I,locale:K,locales:L,defaultLocale:Q,routerServerContext:S,nextConfig:U,resolvedPathname:V}=s,W=null==D||null==(o=D.config)||null==(n=o.experimental)?void 0:n.isExperimentalCompile,X=!!j,Y=!!h,_=!!g,ac=!!(c.default||c).getInitialProps,ad=u.amp&&(null==b?void 0:b.amp),ae=null,af=!1,ag=s.isNextDataRequest&&(Y||X),ah="/404"===r,ai="/500"===r,aj="/_error"===r;if(e.isDev||G||!Y||(ae=`${K?`/${K}`:""}${("/"===r||"/"===V)&&K?"":V}${ad?".amp":""}`,(ah||ai||aj)&&(ae=`${K?`/${K}`:""}${r}${ad?".amp":""}`),ae="/index"===ae?"/":ae),_&&!G){let a=aa(K?ab(V,`/${K}`):V),b=!!F.routes[a]||F.notFoundRoutes.includes(a),c=F.dynamicRoutes[r];if(c){if(!1===c.fallback&&!b)throw new ay.NoFallbackError;"string"!=typeof c.fallback||b||ag||(af=!0)}}(af&&(q=p=k.headers["user-agent"]||"",aA.test(q)||az.test(p))||O(k,"minimalMode"))&&(af=!1);let ak=J(),al=ak.getActiveScopeSpan();try{let a=k.method||"GET",n=M({pathname:U.trailingSlash?w.pathname:aa(w.pathname||"/"),query:Y?{}:x}),o=(null==S?void 0:S.publicRuntimeConfig)||U.publicRuntimeConfig,p=async p=>{var q,s;let A,D=async({previousCacheEntry:q})=>{var s;let w=async()=>{try{var d,m,s,w;return await e.render(k,l,{query:Y&&!W?{...v,...ad?{amp:u.amp}:{}}:{...u,...v},params:v,page:r,renderContext:{isDraftMode:G,isFallback:af,developmentNotFoundSourcePage:O(k,"developmentNotFoundSourcePage")},sharedContext:{buildId:t,customServer:!!(null==S?void 0:S.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:v,routeModule:e,page:r,pageConfig:b||{},Component:c.default||c,ComponentMod:c,getStaticProps:h,getStaticPaths:g,getServerSideProps:j,supportsDynamicResponse:!Y,buildManifest:f?B:z,nextFontManifest:C,reactLoadableManifest:E,assetPrefix:U.assetPrefix,previewProps:F.preview,images:U.images,nextConfigOutput:U.output,optimizeCss:!!U.experimental.optimizeCss,nextScriptWorkers:!!U.experimental.nextScriptWorkers,domainLocales:null==(d=U.i18n)?void 0:d.domains,crossOrigin:U.crossOrigin,multiZoneDraftMode:!1,basePath:U.basePath,canonicalBase:U.amp.canonicalBase||"",ampOptimizerConfig:null==(m=U.experimental.amp)?void 0:m.optimizer,disableOptimizedLoading:U.experimental.disableOptimizedLoading,largePageDataBytes:U.experimental.largePageDataBytes,runtimeConfig:Object.keys(o).length>0?o:void 0,isExperimentalCompile:W,experimental:{clientTraceMetadata:U.experimental.clientTraceMetadata||[]},locale:K,locales:L,defaultLocale:Q,setIsrStatus:null==S?void 0:S.setIsrStatus,isNextDataRequest:ag&&(X||Y),resolvedUrl:n,resolvedAsPath:X||ac?M({pathname:ag?(w=y,R(w||"/","/_next/data")&&"/index"===(w=w.replace(/\/_next\/data\/[^/]{1,}/,"").replace(/\.json$/,""))?"/":w):y,query:x}):n,isOnDemandRevalidate:H,ErrorDebug:O(k,"PagesErrorDebug"),err:O(k,"invokeError"),dev:e.isDev,distDir:av.default.join(process.cwd(),e.relativeProjectDir,e.distDir),ampSkipValidation:null==(s=U.experimental.amp)?void 0:s.skipValidation,ampValidator:O(k,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:T.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:T.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!p)return;p.setAttributes({"http.status_code":l.statusCode,"next.rsc":!1});let b=ak.getRootSpanAttributes();if(!b)return;if(b.get("next.span_type")!==i.handleRequest)return void console.warn(`Unexpected root span type '${b.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let c=b.get("next.route");if(c){let b=`${a} ${c}`;p.setAttributes({"next.route":c,"http.route":c,"next.span_name":b}),p.updateName(b)}else p.updateName(`${a} ${k.url}`)})}catch(a){throw(null==q?void 0:q.isStale)&&await e.onRequestError(k,a,{routerKind:"Pages Router",routePath:r,routeType:"render",revalidateReason:P({isRevalidate:Y,isOnDemandRevalidate:H})},S),a}};if(q&&(af=!1),af){let a=await e.getResponseCache(k).get(e.isDev?null:K?`/${K}${r}`:r,async({previousCacheEntry:a=null})=>e.isDev?w():as(a),{routeKind:d.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await e.getIncrementalCache(k,U,F),waitUntil:m.waitUntil});if(a)return delete a.cacheControl,a.isMiss=!0,a}return!O(k,"minimalMode")&&H&&I&&!q?(l.statusCode=404,l.setHeader("x-nextjs-cache","REVALIDATED"),l.end("This page could not be found"),null):af&&(null==q||null==(s=q.value)?void 0:s.kind)===T.PAGES?{value:{kind:T.PAGES,html:new ar(Buffer.from(q.value.html),{contentType:Z,metadata:{statusCode:q.value.status,headers:q.value.headers}}),pageData:{},status:q.value.status,headers:q.value.headers},cacheControl:{revalidate:0,expire:void 0}}:w()},J=await e.handleResponse({cacheKey:ae,req:k,nextConfig:U,routeKind:d.PAGES,isOnDemandRevalidate:H,revalidateOnlyGenerated:I,waitUntil:m.waitUntil,responseGenerator:D,prerenderManifest:F});if(!af||(null==J?void 0:J.isMiss)||(af=!1),J){if(Y&&!O(k,"minimalMode")&&l.setHeader("x-nextjs-cache",H?"REVALIDATED":J.isMiss?"MISS":J.isStale?"STALE":"HIT"),!Y||af)l.getHeader("Cache-Control")||(A={revalidate:0,expire:void 0});else if(ah){let a=O(k,"notFoundRevalidate");A={revalidate:void 0===a?0:a,expire:void 0}}else if(ai)A={revalidate:0,expire:void 0};else if(J.cacheControl)if("number"==typeof J.cacheControl.revalidate){if(J.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${J.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});A={revalidate:J.cacheControl.revalidate,expire:(null==(q=J.cacheControl)?void 0:q.expire)??U.expireTime}}else A={revalidate:31536e3,expire:void 0};if(A&&!l.getHeader("Cache-Control")&&l.setHeader("Cache-Control",at(A)),!J.value)return(!function(a,b,c){let d=O(a);d[b]=c,a[N]=d}(k,"notFoundRevalidate",null==(s=J.cacheControl)?void 0:s.revalidate),l.statusCode=404,ag)?void l.end('{"notFound":true}'):void((null==S?void 0:S.render404)?await S.render404(k,l,w,!1):l.end("This page could not be found"));if(J.value.kind===T.REDIRECT)if(!ag)return await (a=>{let b={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},c=b.statusCode||(b.permanent?au.PermanentRedirect:au.TemporaryRedirect),{basePath:d}=U;d&&!1!==b.basePath&&b.destination.startsWith("/")&&(b.destination=`${d}${b.destination}`),b.destination.startsWith("/")&&(b.destination=function(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}(b.destination)),l.statusCode=c,l.setHeader("Location",b.destination),c===au.PermanentRedirect&&l.setHeader("Refresh",`0;url=${b.destination}`),l.end(b.destination)})(J.value.props),null;else{l.setHeader("content-type",$),l.end(JSON.stringify(J.value.props));return}if(J.value.kind!==T.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(e.isDev&&l.setHeader("Cache-Control","no-store, must-revalidate"),G&&l.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),O(k,"customErrorRender")||aj&&O(k,"minimalMode")&&500===l.statusCode)return null;await ax({req:k,res:l,result:!ag||aj||ai?J.value.html:new ar(Buffer.from(JSON.stringify(J.value.pageData)),{contentType:$,metadata:J.value.html.metadata}),generateEtags:U.generateEtags,poweredByHeader:U.poweredByHeader,cacheControl:e.isDev?void 0:A})}};al?await p():await ak.withPropagatedContext(k.headers,()=>ak.trace(i.handleRequest,{spanName:`${a} ${k.url}`,kind:A.SERVER,attributes:{"http.method":a,"http.target":k.url}},p))}catch(a){throw a instanceof ay.NoFallbackError||await e.onRequestError(k,a,{routerKind:"Pages Router",routePath:r,routeType:"render",revalidateReason:P({isRevalidate:Y,isOnDemandRevalidate:H})},S),a}})({srcPage:"/_error",config:aF,userland:h,routeModule:aM,getStaticPaths:aD,getStaticProps:aC,getServerSideProps:aE})}];

//# sourceMappingURL=node_modules_b235d39a._.js.map