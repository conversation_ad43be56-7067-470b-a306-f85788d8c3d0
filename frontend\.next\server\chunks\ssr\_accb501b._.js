module.exports=[70121,11011,30553,98621,97895,7379,a=>{"use strict";a.s(["composeRefs",()=>d,"useComposedRefs",()=>e],70121);var b=a.i(72131);function c(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function d(...a){return b=>{let d=!1,e=a.map(a=>{let e=c(a,b);return d||"function"!=typeof e||(d=!0),e});if(d)return()=>{for(let b=0;b<e.length;b++){let d=e[b];"function"==typeof d?d():c(a[b],null)}}}}function e(...a){return b.useCallback(d(...a),a)}a.s(["Primitive",()=>m,"dispatchDiscreteCustomEvent",()=>n],30553);var f=a.i(35112);a.s(["Slot",()=>i,"createSlot",()=>h,"createSlottable",()=>k],11011);var g=a.i(87924);function h(a){let c=function(a){let c=b.forwardRef((a,c)=>{let{children:e,...f}=a;if(b.isValidElement(e)){var g;let a,h,i=(g=e,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,e.props);return e.type!==b.Fragment&&(j.ref=c?d(c,i):i),b.cloneElement(e,j)}return b.Children.count(e)>1?b.Children.only(null):null});return c.displayName=`${a}.SlotClone`,c}(a),e=b.forwardRef((a,d)=>{let{children:e,...f}=a,h=b.Children.toArray(e),i=h.find(l);if(i){let a=i.props.children,e=h.map(c=>c!==i?c:b.Children.count(a)>1?b.Children.only(null):b.isValidElement(a)?a.props.children:null);return(0,g.jsx)(c,{...f,ref:d,children:b.isValidElement(a)?b.cloneElement(a,void 0,e):null})}return(0,g.jsx)(c,{...f,ref:d,children:e})});return e.displayName=`${a}.Slot`,e}var i=h("Slot"),j=Symbol("radix.slottable");function k(a){let b=({children:a})=>(0,g.jsx)(g.Fragment,{children:a});return b.displayName=`${a}.Slottable`,b.__radixId=j,b}function l(a){return b.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===j}var m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let d=h(`Primitive.${c}`),e=b.forwardRef((a,b)=>{let{asChild:e,...f}=a;return(0,g.jsx)(e?d:c,{...f,ref:b})});return e.displayName=`Primitive.${c}`,{...a,[c]:e}},{});function n(a,b){a&&f.flushSync(()=>a.dispatchEvent(b))}function o(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}a.s(["clsx",()=>o],98621),a.s(["cn",()=>an],97895);let p=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?p(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},q=/^\[(.+)\]$/,r=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:s(b,a)).classGroupId=c;return}if("function"==typeof a)return t(a)?void r(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{r(e,s(b,a),c,d)})})},s=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},t=a=>a.isThemeGetter,u=/\s+/;function v(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=w(a))&&(d&&(d+=" "),d+=b);return d}let w=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=w(a[d]))&&(c&&(c+=" "),c+=b);return c},x=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},y=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,z=/^\((?:(\w[\w-]*):)?(.+)\)$/i,A=/^\d+\/\d+$/,B=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,D=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,F=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,G=a=>A.test(a),H=a=>!!a&&!Number.isNaN(Number(a)),I=a=>!!a&&Number.isInteger(Number(a)),J=a=>a.endsWith("%")&&H(a.slice(0,-1)),K=a=>B.test(a),L=()=>!0,M=a=>C.test(a)&&!D.test(a),N=()=>!1,O=a=>E.test(a),P=a=>F.test(a),Q=a=>!S(a)&&!Y(a),R=a=>ad(a,ah,N),S=a=>y.test(a),T=a=>ad(a,ai,M),U=a=>ad(a,aj,H),V=a=>ad(a,af,N),W=a=>ad(a,ag,P),X=a=>ad(a,al,O),Y=a=>z.test(a),Z=a=>ae(a,ai),$=a=>ae(a,ak),_=a=>ae(a,af),aa=a=>ae(a,ah),ab=a=>ae(a,ag),ac=a=>ae(a,al,!0),ad=(a,b,c)=>{let d=y.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},ae=(a,b,c=!1)=>{let d=z.exec(a);return!!d&&(d[1]?b(d[1]):c)},af=a=>"position"===a||"percentage"===a,ag=a=>"image"===a||"url"===a,ah=a=>"length"===a||"size"===a||"bg-size"===a,ai=a=>"length"===a,aj=a=>"number"===a,ak=a=>"family-name"===a,al=a=>"shadow"===a;Symbol.toStringTag;let am=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)r(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),p(c,b)||(a=>{if(q.test(a)){let b=q.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(u),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(v.apply(null,arguments))}}(()=>{let a=x("color"),b=x("font"),c=x("text"),d=x("font-weight"),e=x("tracking"),f=x("leading"),g=x("breakpoint"),h=x("container"),i=x("spacing"),j=x("radius"),k=x("shadow"),l=x("inset-shadow"),m=x("text-shadow"),n=x("drop-shadow"),o=x("blur"),p=x("perspective"),q=x("aspect"),r=x("ease"),s=x("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),Y,S],w=()=>["auto","hidden","clip","visible","scroll"],y=()=>["auto","contain","none"],z=()=>[Y,S,i],A=()=>[G,"full","auto",...z()],B=()=>[I,"none","subgrid",Y,S],C=()=>["auto",{span:["full",I,Y,S]},I,Y,S],D=()=>[I,"auto",Y,S],E=()=>["auto","min","max","fr",Y,S],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],M=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...z()],O=()=>[G,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...z()],P=()=>[a,Y,S],ad=()=>[...u(),_,V,{position:[Y,S]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",aa,R,{size:[Y,S]}],ag=()=>[J,Z,T],ah=()=>["","none","full",j,Y,S],ai=()=>["",H,Z,T],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[H,J,_,V],am=()=>["","none",o,Y,S],an=()=>["none",H,Y,S],ao=()=>["none",H,Y,S],ap=()=>[H,Y,S],aq=()=>[G,"full",...z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[K],breakpoint:[K],color:[L],container:[K],"drop-shadow":[K],ease:["in","out","in-out"],font:[Q],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[K],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[K],shadow:[K],spacing:["px",H],text:[K],"text-shadow":[K],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",G,S,Y,q]}],container:["container"],columns:[{columns:[H,S,Y,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:y()}],"overscroll-x":[{"overscroll-x":y()}],"overscroll-y":[{"overscroll-y":y()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",Y,S]}],basis:[{basis:[G,"full","auto",h,...z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[H,G,"auto","initial","none",S]}],grow:[{grow:["",H,Y,S]}],shrink:[{shrink:["",H,Y,S]}],order:[{order:[I,"first","last","none",Y,S]}],"grid-cols":[{"grid-cols":B()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":B()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:z()}],"gap-x":[{"gap-x":z()}],"gap-y":[{"gap-y":z()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...M(),"normal"]}],"justify-self":[{"justify-self":["auto",...M()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...M(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...M(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...M(),"baseline"]}],"place-self":[{"place-self":["auto",...M()]}],p:[{p:z()}],px:[{px:z()}],py:[{py:z()}],ps:[{ps:z()}],pe:[{pe:z()}],pt:[{pt:z()}],pr:[{pr:z()}],pb:[{pb:z()}],pl:[{pl:z()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":z()}],"space-y-reverse":["space-y-reverse"],size:[{size:O()}],w:[{w:[h,"screen",...O()]}],"min-w":[{"min-w":[h,"screen","none",...O()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...O()]}],h:[{h:["screen","lh",...O()]}],"min-h":[{"min-h":["screen","lh","none",...O()]}],"max-h":[{"max-h":["screen","lh",...O()]}],"font-size":[{text:["base",c,Z,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,Y,U]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",J,S]}],"font-family":[{font:[$,S,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,Y,S]}],"line-clamp":[{"line-clamp":[H,"none",Y,U]}],leading:[{leading:[f,...z()]}],"list-image":[{"list-image":["none",Y,S]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,S]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[H,"from-font","auto",Y,T]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[H,"auto",Y,S]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,S]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,S]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,Y,S],radial:["",Y,S],conic:[I,Y,S]},ab,W]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[H,Y,S]}],"outline-w":[{outline:["",H,Z,T]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",k,ac,X]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",l,ac,X]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[H,T]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",m,ac,X]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[H,Y,S]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[H]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[Y,S]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[H]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,S]}],filter:[{filter:["","none",Y,S]}],blur:[{blur:am()}],brightness:[{brightness:[H,Y,S]}],contrast:[{contrast:[H,Y,S]}],"drop-shadow":[{"drop-shadow":["","none",n,ac,X]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",H,Y,S]}],"hue-rotate":[{"hue-rotate":[H,Y,S]}],invert:[{invert:["",H,Y,S]}],saturate:[{saturate:[H,Y,S]}],sepia:[{sepia:["",H,Y,S]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,S]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[H,Y,S]}],"backdrop-contrast":[{"backdrop-contrast":[H,Y,S]}],"backdrop-grayscale":[{"backdrop-grayscale":["",H,Y,S]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[H,Y,S]}],"backdrop-invert":[{"backdrop-invert":["",H,Y,S]}],"backdrop-opacity":[{"backdrop-opacity":[H,Y,S]}],"backdrop-saturate":[{"backdrop-saturate":[H,Y,S]}],"backdrop-sepia":[{"backdrop-sepia":["",H,Y,S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":z()}],"border-spacing-x":[{"border-spacing-x":z()}],"border-spacing-y":[{"border-spacing-y":z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,S]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[H,"initial",Y,S]}],ease:[{ease:["linear","initial",r,Y,S]}],delay:[{delay:[H,Y,S]}],animate:[{animate:["none",s,Y,S]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,Y,S]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[Y,S,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,S]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,S]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[H,Z,T,U]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function an(...a){return am(o(a))}a.s(["Separator",()=>ar],7379);var ao="horizontal",ap=["horizontal","vertical"],aq=b.forwardRef((a,b)=>{var c;let{decorative:d,orientation:e=ao,...f}=a,h=(c=e,ap.includes(c))?e:ao;return(0,g.jsx)(m.div,{"data-orientation":h,...d?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...f,ref:b})});function ar({className:a,orientation:b="horizontal",decorative:c=!0,...d}){return(0,g.jsx)(aq,{"data-slot":"separator",decorative:c,orientation:b,className:an("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...d})}aq.displayName="Separator"},70106,7554,50104,72752,25152,77192,92843,187,40695,5522,46872,96743,22297,92616,86228,52081,41852,97942,22262,50463,4691,28094,14727,a=>{"use strict";a.s(["default",()=>j],70106);var b,c,d,e=a.i(72131);let f=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},g=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,e.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:f="",children:i,iconNode:j,...k},l)=>(0,e.createElement)("svg",{ref:l,...h,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:g("lucide",f),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,e.createElement)(a,b)),...Array.isArray(i)?i:[i]])),j=(a,b)=>{let c=(0,e.forwardRef)(({className:c,...d},h)=>(0,e.createElement)(i,{ref:h,iconNode:b,className:g(`lucide-${f(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=f(a),c};function k(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}a.s(["composeEventHandlers",()=>k],7554),a.s(["createContext",()=>m,"createContextScope",()=>n],50104);var l=a.i(87924);function m(a,b){let c=e.createContext(b),d=a=>{let{children:b,...d}=a,f=e.useMemo(()=>d,Object.values(d));return(0,l.jsx)(c.Provider,{value:f,children:b})};return d.displayName=a+"Provider",[d,function(d){let f=e.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${d}\` must be used within \`${a}\``)}]}function n(a,b=[]){let c=[],d=()=>{let b=c.map(a=>e.createContext(a));return function(c){let d=c?.[a]||b;return e.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let f=e.createContext(d),g=c.length;c=[...c,d];let h=b=>{let{scope:c,children:d,...h}=b,i=c?.[a]?.[g]||f,j=e.useMemo(()=>h,Object.values(h));return(0,l.jsx)(i.Provider,{value:j,children:d})};return h.displayName=b+"Provider",[h,function(c,h){let i=h?.[a]?.[g]||f,j=e.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return e.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}a.s(["useControllableState",()=>q],25152),a.s(["useLayoutEffect",()=>o],72752);var o=globalThis?.document?e.useLayoutEffect:()=>{};e[" useEffectEvent ".trim().toString()],e[" useInsertionEffect ".trim().toString()];var p=e[" useInsertionEffect ".trim().toString()]||o;function q({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,g,h]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),g=e.useRef(b);return p(()=>{g.current=b},[b]),e.useEffect(()=>{f.current!==c&&(g.current?.(c),f.current=c)},[c,f]),[c,d,g]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,e.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else g(b)},[i,a,g,h])]}Symbol("RADIX:SYNC_STATE"),a.s(["Presence",()=>s],77192);var r=a.i(70121),s=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,f]=e.useState(),g=e.useRef(null),h=e.useRef(a),i=e.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},e.useReducer((a,b)=>c[a][b]??a,b));return e.useEffect(()=>{let a=t(g.current);i.current="mounted"===j?a:"none"},[j]),o(()=>{let b=g.current,c=h.current;if(c!==a){let d=i.current,e=t(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),h.current=a}},[a,k]),o(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=t(g.current).includes(CSS.escape(c.animationName));if(c.target===d&&e&&(k("ANIMATION_END"),!h.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(i.current=t(g.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:e.useCallback(a=>{g.current=a?getComputedStyle(a):null,f(a)},[])}}(b),f="function"==typeof c?c({present:d.isPresent}):e.Children.only(c),g=(0,r.useComposedRefs)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(f));return"function"==typeof c||d.isPresent?e.cloneElement(f,{ref:g}):null};function t(a){return a?.animationName||"none"}s.displayName="Presence",a.s(["useId",()=>w],92843);var u=e[" useId ".trim().toString()]||(()=>void 0),v=0;function w(a){let[b,c]=e.useState(u());return o(()=>{a||c(a=>a??String(v++))},[a]),a||(b?`radix-${b}`:"")}a.s(["Sidebar",()=>dH,"SidebarContent",()=>dP,"SidebarFooter",()=>dN,"SidebarGroup",()=>dQ,"SidebarGroupAction",()=>dS,"SidebarGroupContent",()=>dT,"SidebarGroupLabel",()=>dR,"SidebarHeader",()=>dM,"SidebarInput",()=>dL,"SidebarInset",()=>dK,"SidebarMenu",()=>dU,"SidebarMenuAction",()=>dY,"SidebarMenuBadge",()=>dZ,"SidebarMenuButton",()=>dX,"SidebarMenuItem",()=>dV,"SidebarMenuSkeleton",()=>d$,"SidebarMenuSub",()=>d_,"SidebarMenuSubButton",()=>d1,"SidebarMenuSubItem",()=>d0,"SidebarProvider",()=>dG,"SidebarRail",()=>dJ,"SidebarSeparator",()=>dO,"SidebarTrigger",()=>dI,"useSidebar",()=>dF],14727);var x=a.i(11011);a.s(["cva",()=>B],187);var y=a.i(98621);let z=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,A=y.clsx,B=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return A(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=z(b)||z(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return A(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)},C=j("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var D=a.i(97895);a.s(["Button",()=>F],40695);let E=B("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function F({className:a,variant:b,size:c,asChild:d=!1,...e}){let f=d?x.Slot:"button";return(0,l.jsx)(f,{"data-slot":"button",className:(0,D.cn)(E({variant:b,size:c,className:a})),...e})}function G({className:a,type:b,...c}){return(0,l.jsx)("input",{type:b,"data-slot":"input",className:(0,D.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}a.s(["Input",()=>G],5522);var H=a.i(7379);a.s(["Close",()=>bt,"Content",()=>bq,"Description",()=>bs,"Overlay",()=>bp,"Portal",()=>bo,"Root",()=>bm,"Title",()=>br,"Trigger",()=>bn],97942),a.s(["DismissableLayer",()=>M],96743);var I=a.i(30553);function J(a){let b=e.useRef(a);return e.useEffect(()=>{b.current=a}),e.useMemo(()=>(...a)=>b.current?.(...a),[])}a.s(["useCallbackRef",()=>J],46872);var K="dismissableLayer.update",L=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),M=e.forwardRef((a,b)=>{let{disableOutsidePointerEvents:d=!1,onEscapeKeyDown:f,onPointerDownOutside:g,onFocusOutside:h,onInteractOutside:i,onDismiss:j,...m}=a,n=e.useContext(L),[o,p]=e.useState(null),q=o?.ownerDocument??globalThis?.document,[,s]=e.useState({}),t=(0,r.useComposedRefs)(b,a=>p(a)),u=Array.from(n.layers),[v]=[...n.layersWithOutsidePointerEventsDisabled].slice(-1),w=u.indexOf(v),x=o?u.indexOf(o):-1,y=n.layersWithOutsidePointerEventsDisabled.size>0,z=x>=w,A=function(a,b=globalThis?.document){let c=J(a),d=e.useRef(!1),f=e.useRef(()=>{});return e.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){O("dismissableLayer.pointerDownOutside",c,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=d,b.addEventListener("click",f.current,{once:!0})):d()}else b.removeEventListener("click",f.current);d.current=!1},e=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(e),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...n.branches].some(a=>a.contains(b));z&&!c&&(g?.(a),i?.(a),a.defaultPrevented||j?.())},q),B=function(a,b=globalThis?.document){let c=J(a),d=e.useRef(!1);return e.useEffect(()=>{let a=a=>{a.target&&!d.current&&O("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...n.branches].some(a=>a.contains(b))&&(h?.(a),i?.(a),a.defaultPrevented||j?.())},q);return!function(a,b=globalThis?.document){let c=J(a);e.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{x===n.layers.size-1&&(f?.(a),!a.defaultPrevented&&j&&(a.preventDefault(),j()))},q),e.useEffect(()=>{if(o)return d&&(0===n.layersWithOutsidePointerEventsDisabled.size&&(c=q.body.style.pointerEvents,q.body.style.pointerEvents="none"),n.layersWithOutsidePointerEventsDisabled.add(o)),n.layers.add(o),N(),()=>{d&&1===n.layersWithOutsidePointerEventsDisabled.size&&(q.body.style.pointerEvents=c)}},[o,q,d,n]),e.useEffect(()=>()=>{o&&(n.layers.delete(o),n.layersWithOutsidePointerEventsDisabled.delete(o),N())},[o,n]),e.useEffect(()=>{let a=()=>s({});return document.addEventListener(K,a),()=>document.removeEventListener(K,a)},[]),(0,l.jsx)(I.Primitive.div,{...m,ref:t,style:{pointerEvents:y?z?"auto":"none":void 0,...a.style},onFocusCapture:k(a.onFocusCapture,B.onFocusCapture),onBlurCapture:k(a.onBlurCapture,B.onBlurCapture),onPointerDownCapture:k(a.onPointerDownCapture,A.onPointerDownCapture)})});function N(){let a=new CustomEvent(K);document.dispatchEvent(a)}function O(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,I.dispatchDiscreteCustomEvent)(e,f):e.dispatchEvent(f)}M.displayName="DismissableLayer",e.forwardRef((a,b)=>{let c=e.useContext(L),d=e.useRef(null),f=(0,r.useComposedRefs)(b,d);return e.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,l.jsx)(I.Primitive.div,{...a,ref:f})}).displayName="DismissableLayerBranch",a.s(["FocusScope",()=>S],22297);var P="focusScope.autoFocusOnMount",Q="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},S=e.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:f,onUnmountAutoFocus:g,...h}=a,[i,j]=e.useState(null),k=J(f),m=J(g),n=e.useRef(null),o=(0,r.useComposedRefs)(b,a=>j(a)),p=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect(()=>{if(d){let a=function(a){if(p.paused||!i)return;let b=a.target;i.contains(b)?n.current=b:V(n.current,{select:!0})},b=function(a){if(p.paused||!i)return;let b=a.relatedTarget;null!==b&&(i.contains(b)||V(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&V(i)});return i&&c.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,i,p.paused]),e.useEffect(()=>{if(i){W.add(p);let a=document.activeElement;if(!i.contains(a)){let b=new CustomEvent(P,R);i.addEventListener(P,k),i.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(V(d,{select:b}),document.activeElement!==c)return}(T(i).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&V(i))}return()=>{i.removeEventListener(P,k),setTimeout(()=>{let b=new CustomEvent(Q,R);i.addEventListener(Q,m),i.dispatchEvent(b),b.defaultPrevented||V(a??document.body,{select:!0}),i.removeEventListener(Q,m),W.remove(p)},0)}}},[i,k,m,p]);let q=e.useCallback(a=>{if(!c&&!d||p.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=T(a);return[U(b,a),U(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&V(f,{select:!0})):(a.preventDefault(),c&&V(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,p.paused]);return(0,l.jsx)(I.Primitive.div,{tabIndex:-1,...h,ref:o,onKeyDown:q})});function T(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function U(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function V(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}S.displayName="FocusScope";var W=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=X(a,b)).unshift(b)},remove(b){a=X(a,b),a[0]?.resume()}}}();function X(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}a.s(["Portal",()=>Z],92616);var Y=a.i(35112),Z=e.forwardRef((a,b)=>{let{container:c,...d}=a,[f,g]=e.useState(!1);o(()=>g(!0),[]);let h=c||f&&globalThis?.document?.body;return h?Y.default.createPortal((0,l.jsx)(I.Primitive.div,{...d,ref:b}),h):null});Z.displayName="Portal",a.s(["useFocusGuards",()=>_],86228);var $=0;function _(){e.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??aa()),document.body.insertAdjacentElement("beforeend",a[1]??aa()),$++,()=>{1===$&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),$--}},[])}function aa(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}a.s(["RemoveScroll",()=>aJ],52081);var ab=function(){return(ab=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function ac(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var ad=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),ae="width-before-scroll-bar";function af(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var ag=e.useEffect,ah=new WeakMap;function ai(a){return a}var aj=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=ai),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=ab({async:!0,ssr:!1},a),e}(),ak=function(){},al=e.forwardRef(function(a,b){var c,d,f,g,h=e.useRef(null),i=e.useState({onScrollCapture:ak,onWheelCapture:ak,onTouchMoveCapture:ak}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=ac(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return af(b,a)})},(f=(0,e.useState)(function(){return{value:null,callback:d,facade:{get current(){return f.value},set current(value){var a=f.value;a!==value&&(f.value=value,f.callback(value,a))}}}})[0]).callback=d,g=f.facade,ag(function(){var a=ah.get(g);if(a){var b=new Set(a),d=new Set(c),e=g.current;b.forEach(function(a){d.has(a)||af(a,null)}),d.forEach(function(a){b.has(a)||af(a,e)})}ah.set(g,c)},[c]),g),A=ab(ab({},y),j);return e.createElement(e.Fragment,null,p&&e.createElement(r,{sideCar:aj,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?e.cloneElement(e.Children.only(m),ab(ab({},A),{ref:z})):e.createElement(void 0===w?"div":w,ab({},A,{className:n,ref:z}),m))});al.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},al.classNames={fullWidth:ae,zeroRight:ad};var am=function(a){var b=a.sideCar,c=ac(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return e.createElement(d,ab({},c))};am.isSideCarExport=!0;var an=function(){var a=0,b=null;return{add:function(c){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=d||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return b&&a.setAttribute("nonce",b),a}())){var e,f;(e=b).styleSheet?e.styleSheet.cssText=c:e.appendChild(document.createTextNode(c)),f=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(f)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},ao=function(){var a=an();return function(b,c){e.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},ap=function(){var a=ao();return function(b){return a(b.styles,b.dynamic),null}},aq={left:0,top:0,right:0,gap:0},ar=ap(),as="data-scroll-locked",at=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(as,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(ad," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(ae," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(ad," .").concat(ad," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(ae," .").concat(ae," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(as,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},au=function(){var a=parseInt(document.body.getAttribute(as)||"0",10);return isFinite(a)?a:0},av=function(){e.useEffect(function(){return document.body.setAttribute(as,(au()+1).toString()),function(){var a=au()-1;a<=0?document.body.removeAttribute(as):document.body.setAttribute(as,a.toString())}},[])},aw=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,f=void 0===d?"margin":d;av();var g=e.useMemo(function(){return void 0===f,aq},[f]);return e.createElement(ar,{styles:at(g,!b,f,c?"":"!important")})},ax=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},ay=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),az(a,d)){var e=aA(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body)return!1},az=function(a,b){return"v"===a?ax(b,"overflowY"):ax(b,"overflowX")},aA=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},aB=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=aA(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&az(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i))return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},aC=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},aD=function(a){return[a.deltaX,a.deltaY]},aE=function(a){return a&&"current"in a?a.current:a},aF=0,aG=[];let aH=(b=function(a){var b=e.useRef([]),c=e.useRef([0,0]),d=e.useRef(),f=e.useState(aF++)[0],g=e.useState(ap)[0],h=e.useRef(a);e.useEffect(function(){h.current=a},[a]),e.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(f));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(aE),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(f))}),function(){document.body.classList.remove("block-interactivity-".concat(f)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(f))})}}},[a.inert,a.lockRef.current,a.shards]);var i=e.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=aC(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=ay(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=ay(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return aB(n,b,a,"h"===n?i:j,!0)},[]),j=e.useCallback(function(a){if(aG.length&&aG[aG.length-1]===g){var c="deltaY"in a?aD(a):aC(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(aE).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=e.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=e.useCallback(function(a){c.current=aC(a),d.current=void 0},[]),m=e.useCallback(function(b){k(b.type,aD(b),b.target,i(b,a.lockRef.current))},[]),n=e.useCallback(function(b){k(b.type,aC(b),b.target,i(b,a.lockRef.current))},[]);e.useEffect(function(){return aG.push(g),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,!1),document.addEventListener("touchmove",j,!1),document.addEventListener("touchstart",l,!1),function(){aG=aG.filter(function(a){return a!==g}),document.removeEventListener("wheel",j,!1),document.removeEventListener("touchmove",j,!1),document.removeEventListener("touchstart",l,!1)}},[]);var o=a.removeScrollBar,p=a.inert;return e.createElement(e.Fragment,null,p?e.createElement(g,{styles:"\n  .block-interactivity-".concat(f," {pointer-events: none;}\n  .allow-interactivity-").concat(f," {pointer-events: all;}\n")}):null,o?e.createElement(aw,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},aj.useMedium(b),am);var aI=e.forwardRef(function(a,b){return e.createElement(al,ab({},a,{ref:b,sideCar:aH}))});aI.classNames=al.classNames;let aJ=aI;a.s(["hideOthers",()=>aQ],41852);var aK=new WeakMap,aL=new WeakMap,aM={},aN=0,aO=function(a){return a&&(a.host||aO(a.parentNode))},aP=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=aO(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});aM[c]||(aM[c]=new WeakMap);var f=aM[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(aK.get(a)||0)+1,j=(f.get(a)||0)+1;aK.set(a,i),f.set(a,j),g.push(a),1===i&&e&&aL.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),aN++,function(){g.forEach(function(a){var b=aK.get(a)-1,e=f.get(a)-1;aK.set(a,b),f.set(a,e),b||(aL.has(a)||a.removeAttribute(d),aL.delete(a)),e||a.removeAttribute(c)}),--aN||(aK=new WeakMap,aK=new WeakMap,aL=new WeakMap,aM={})}},aQ=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),aP(d,e,c,"aria-hidden")):function(){return null}},aR="Dialog",[aS,aT]=n(aR),[aU,aV]=aS(aR),aW=a=>{let{__scopeDialog:b,children:c,open:d,defaultOpen:f,onOpenChange:g,modal:h=!0}=a,i=e.useRef(null),j=e.useRef(null),[k,m]=q({prop:d,defaultProp:f??!1,onChange:g,caller:aR});return(0,l.jsx)(aU,{scope:b,triggerRef:i,contentRef:j,contentId:w(),titleId:w(),descriptionId:w(),open:k,onOpenChange:m,onOpenToggle:e.useCallback(()=>m(a=>!a),[m]),modal:h,children:c})};aW.displayName=aR;var aX="DialogTrigger",aY=e.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aV(aX,c),f=(0,r.useComposedRefs)(b,e.triggerRef);return(0,l.jsx)(I.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":e.open,"aria-controls":e.contentId,"data-state":bg(e.open),...d,ref:f,onClick:k(a.onClick,e.onOpenToggle)})});aY.displayName=aX;var aZ="DialogPortal",[a$,a_]=aS(aZ,{forceMount:void 0}),a0=a=>{let{__scopeDialog:b,forceMount:c,children:d,container:f}=a,g=aV(aZ,b);return(0,l.jsx)(a$,{scope:b,forceMount:c,children:e.Children.map(d,a=>(0,l.jsx)(s,{present:c||g.open,children:(0,l.jsx)(Z,{asChild:!0,container:f,children:a})}))})};a0.displayName=aZ;var a1="DialogOverlay",a2=e.forwardRef((a,b)=>{let c=a_(a1,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aV(a1,a.__scopeDialog);return f.modal?(0,l.jsx)(s,{present:d||f.open,children:(0,l.jsx)(a4,{...e,ref:b})}):null});a2.displayName=a1;var a3=(0,x.createSlot)("DialogOverlay.RemoveScroll"),a4=e.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aV(a1,c);return(0,l.jsx)(aJ,{as:a3,allowPinchZoom:!0,shards:[e.contentRef],children:(0,l.jsx)(I.Primitive.div,{"data-state":bg(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),a5="DialogContent",a6=e.forwardRef((a,b)=>{let c=a_(a5,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aV(a5,a.__scopeDialog);return(0,l.jsx)(s,{present:d||f.open,children:f.modal?(0,l.jsx)(a7,{...e,ref:b}):(0,l.jsx)(a8,{...e,ref:b})})});a6.displayName=a5;var a7=e.forwardRef((a,b)=>{let c=aV(a5,a.__scopeDialog),d=e.useRef(null),f=(0,r.useComposedRefs)(b,c.contentRef,d);return e.useEffect(()=>{let a=d.current;if(a)return aQ(a)},[]),(0,l.jsx)(a9,{...a,ref:f,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:k(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:k(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:k(a.onFocusOutside,a=>a.preventDefault())})}),a8=e.forwardRef((a,b)=>{let c=aV(a5,a.__scopeDialog),d=e.useRef(!1),f=e.useRef(!1);return(0,l.jsx)(a9,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(d.current||c.triggerRef.current?.focus(),b.preventDefault()),d.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(d.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let e=b.target;c.triggerRef.current?.contains(e)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),a9=e.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:d,onOpenAutoFocus:f,onCloseAutoFocus:g,...h}=a,i=aV(a5,c),j=e.useRef(null),k=(0,r.useComposedRefs)(b,j);return _(),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(S,{asChild:!0,loop:!0,trapped:d,onMountAutoFocus:f,onUnmountAutoFocus:g,children:(0,l.jsx)(M,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":bg(i.open),...h,ref:k,onDismiss:()=>i.onOpenChange(!1)})}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(bk,{titleId:i.titleId}),(0,l.jsx)(bl,{contentRef:j,descriptionId:i.descriptionId})]})]})}),ba="DialogTitle",bb=e.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aV(ba,c);return(0,l.jsx)(I.Primitive.h2,{id:e.titleId,...d,ref:b})});bb.displayName=ba;var bc="DialogDescription",bd=e.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aV(bc,c);return(0,l.jsx)(I.Primitive.p,{id:e.descriptionId,...d,ref:b})});bd.displayName=bc;var be="DialogClose",bf=e.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aV(be,c);return(0,l.jsx)(I.Primitive.button,{type:"button",...d,ref:b,onClick:k(a.onClick,()=>e.onOpenChange(!1))})});function bg(a){return a?"open":"closed"}bf.displayName=be;var bh="DialogTitleWarning",[bi,bj]=m(bh,{contentName:a5,titleName:ba,docsSlug:"dialog"}),bk=({titleId:a})=>{let b=bj(bh),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return e.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},bl=({contentRef:a,descriptionId:b})=>{let c=bj("DialogDescriptionWarning"),d=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return e.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(d))},[d,a,b]),null},bm=aW,bn=aY,bo=a0,bp=a2,bq=a6,br=bb,bs=bd,bt=bf;a.s(["XIcon",()=>bu],22262);let bu=j("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function bv({...a}){return(0,l.jsx)(bm,{"data-slot":"sheet",...a})}function bw({...a}){return(0,l.jsx)(bo,{"data-slot":"sheet-portal",...a})}function bx({className:a,...b}){return(0,l.jsx)(bp,{"data-slot":"sheet-overlay",className:(0,D.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function by({className:a,children:b,side:c="right",...d}){return(0,l.jsxs)(bw,{children:[(0,l.jsx)(bx,{}),(0,l.jsxs)(bq,{"data-slot":"sheet-content",className:(0,D.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...d,children:[b,(0,l.jsxs)(bt,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,l.jsx)(bu,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function bz({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sheet-header",className:(0,D.cn)("flex flex-col gap-1.5 p-4",a),...b})}function bA({className:a,...b}){return(0,l.jsx)(br,{"data-slot":"sheet-title",className:(0,D.cn)("text-foreground font-semibold",a),...b})}function bB({className:a,...b}){return(0,l.jsx)(bs,{"data-slot":"sheet-description",className:(0,D.cn)("text-muted-foreground text-sm",a),...b})}function bC({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"skeleton",className:(0,D.cn)("bg-accent animate-pulse rounded-md",a),...b})}a.s(["Skeleton",()=>bC],50463),a.s(["Anchor",()=>c2,"Arrow",()=>c4,"Content",()=>c3,"Root",()=>c1,"createPopperScope",()=>cN],4691);let bD=["top","right","bottom","left"],bE=Math.min,bF=Math.max,bG=Math.round,bH=Math.floor,bI=a=>({x:a,y:a}),bJ={left:"right",right:"left",bottom:"top",top:"bottom"},bK={start:"end",end:"start"};function bL(a,b){return"function"==typeof a?a(b):a}function bM(a){return a.split("-")[0]}function bN(a){return a.split("-")[1]}function bO(a){return"x"===a?"y":"x"}function bP(a){return"y"===a?"height":"width"}let bQ=new Set(["top","bottom"]);function bR(a){return bQ.has(bM(a))?"y":"x"}function bS(a){return a.replace(/start|end/g,a=>bK[a])}let bT=["left","right"],bU=["right","left"],bV=["top","bottom"],bW=["bottom","top"];function bX(a){return a.replace(/left|right|bottom|top/g,a=>bJ[a])}function bY(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function bZ(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function b$(a,b,c){let d,{reference:e,floating:f}=a,g=bR(b),h=bO(bR(b)),i=bP(h),j=bM(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(bN(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let b_=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=b$(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=b$(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function b0(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=bL(b,a),o=bY(n),p=h[m?"floating"===l?"reference":"floating":l],q=bZ(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=bZ(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function b1(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function b2(a){return bD.some(b=>a[b]>=0)}let b3=new Set(["left","top"]);async function b4(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=bM(c),h=bN(c),i="y"===bR(c),j=b3.has(g)?-1:1,k=f&&i?-1:1,l=bL(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function b5(a){return function(a){return!1}(a)?(a.nodeName||"").toLowerCase():"#document"}function b6(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function b7(a){var b;return null==(b=(function(a){return!1}(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function b8(a){return!1}let b9=new Set(["inline","contents"]);function ca(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=cl(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!b9.has(e)}let cb=new Set(["table","td","th"]),cc=[":popover-open",":modal"];function cd(a){return cc.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ce=["transform","translate","scale","rotate","perspective"],cf=["transform","translate","scale","rotate","perspective","filter"],cg=["paint","layout","strict","content"];function ch(a){let b=ci(),c=a;return ce.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||cf.some(a=>(c.willChange||"").includes(a))||cg.some(a=>(c.contain||"").includes(a))}function ci(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let cj=new Set(["html","body","#document"]);function ck(a){return cj.has(b5(a))}function cl(a){return b6(a).getComputedStyle(a)}function cm(a){return{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function cn(a){if("html"===b5(a))return a;let b=a.assignedSlot||a.parentNode||!1||b7(a);return b}function co(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=cn(b);return ck(c)?b.ownerDocument?b.ownerDocument.body:b.body:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=b6(e);if(f){let a=cp(g);return b.concat(g,g.visualViewport||[],ca(e)?e:[],a&&c?co(a):[])}return b.concat(e,co(e,[],c))}function cp(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function cq(a){let b=cl(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=!1,f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=bG(c)!==f||bG(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function cr(a){return 0,a.contextElement}function cs(a){let b=cr(a);1;return bI(1)}let ct=bI(0);function cu(a){let b=b6(a);return ci()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ct}function cv(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=cr(a),h=bI(1);b&&(d||(h=cs(a)));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===b6(g))&&e)?cu(g):bI(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=b6(g),b=d,c=a,e=cp(c);for(;e&&d&&b!==c;){let a=cs(e),b=e.getBoundingClientRect(),d=cl(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=cp(c=b6(e))}}return bZ({width:l,height:m,x:j,y:k})}function cw(a,b){let c=cm(a).scrollLeft;return b?b.left+c:cv(b7(a)).left+c}function cx(a,b){let c=a.getBoundingClientRect();return{x:c.left+b.scrollLeft-cw(a,c),y:c.top+b.scrollTop}}function cy(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=b6(a),d=b7(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=ci();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}let j=cw(d);if(j<=0){let a=d.ownerDocument,b=a.body,c=getComputedStyle(b),e="CSS1Compat"===a.compatMode&&parseFloat(c.marginLeft)+parseFloat(c.marginRight)||0,g=Math.abs(d.clientWidth-b.clientWidth-e);g<=25&&(f-=g)}else j<=25&&(f+=j);return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=b7(a),c=cm(a),d=a.ownerDocument.body,e=bF(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=bF(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+cw(a),h=-c.scrollTop;return"rtl"===cl(d).direction&&(g+=bF(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(b7(a));else{1;{let c=cu(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}}return bZ(d)}function cz(a){return"static"===cl(a).position}function cA(a,b){1;return null}function cB(a,b){var c;let d=b6(a);if(cd(a))return d;1;{let b=cn(a);for(;b&&!ck(b);){0;b=cn(b)}return d}}let cC=async function(a){let b=this.getOffsetParent||cB,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){var d;let e=(d=0,!1),f=b7(b),g="fixed"===c,h=cv(a,!0,g,b),i={scrollLeft:0,scrollTop:0},j=bI(0);if(e||!e&&!g)if(("body"!==b5(b)||ca(f))&&(i=cm(b)),e){let a=cv(b,!0,g,b);j.x=a.x+b.clientLeft,j.y=a.y+b.clientTop}else f&&(j.x=cw(f));g&&!e&&f&&(j.x=cw(f));let k=!f||e||g?bI(0):cx(f,i);return{x:h.left+i.scrollLeft-j.x-k.x,y:h.top+i.scrollTop-j.y-k.y,width:h.width,height:h.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},cD={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){var b,c;let{elements:d,rect:e,offsetParent:f,strategy:g}=a,h="fixed"===g,i=b7(f),j=!!d&&cd(d.floating);if(f===i||j&&h)return e;let k={scrollLeft:0,scrollTop:0},l=bI(1),m=bI(0),n=(b=0,!1);(n||!n&&!h)&&(("body"!==b5(f)||ca(i))&&(k=cm(f)),c=0,0);let o=!i||n||h?bI(0):cx(i,k);return{width:e.width*l.x,height:e.height*l.y,x:e.x*l.x-k.scrollLeft*l.x+m.x+o.x,y:e.y*l.y-k.scrollTop*l.y+m.y+o.y}},getDocumentElement:b7,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?cd(b)?[]:function(a,b){var c;let d=b.get(a);if(d)return d;let e=co(a,[],!1).filter(a=>{var b;return b=0,!1}),f="fixed"===cl(a).position,g=f?cn(a):a;return c=0,b.set(a,e),e}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=cy(b,c,e);return a.top=bF(d.top,a.top),a.right=bE(d.right,a.right),a.bottom=bE(d.bottom,a.bottom),a.left=bF(d.left,a.left),a},cy(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:cB,getElementRects:cC,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=cq(a);return{width:b,height:c}},getScale:cs,isElement:b8,isRTL:function(a){return"rtl"===cl(a).direction}};function cE(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let cF=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=bL(a,b)||{};if(null==j)return{};let l=bY(k),m={x:c,y:d},n=bO(bR(e)),o=bP(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=bE(l[q?"top":"left"],w),y=bE(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=bF(x,bE(A,z)),C=!i.arrow&&null!=bN(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var cG="undefined"!=typeof document?e.useLayoutEffect:function(){};function cH(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!cH(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!cH(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function cI(a,b){let c=1;return Math.round(b*c)/c}function cJ(a){let b=e.useRef(a);return cG(()=>{b.current=a}),b}var cK=e.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,l.jsx)(I.Primitive.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});cK.displayName="Arrow";var cL="Popper",[cM,cN]=n(cL),[cO,cP]=cM(cL),cQ=a=>{let{__scopePopper:b,children:c}=a,[d,f]=e.useState(null);return(0,l.jsx)(cO,{scope:b,anchor:d,onAnchorChange:f,children:c})};cQ.displayName=cL;var cR="PopperAnchor",cS=e.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...f}=a,g=cP(cR,c),h=e.useRef(null),i=(0,r.useComposedRefs)(b,h),j=e.useRef(null);return e.useEffect(()=>{let a=j.current;j.current=d?.current||h.current,a!==j.current&&g.onAnchorChange(j.current)}),d?null:(0,l.jsx)(I.Primitive.div,{...f,ref:i})});cS.displayName=cR;var cT="PopperContent",[cU,cV]=cM(cT),cW=e.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:f=0,align:g="center",alignOffset:h=0,arrowPadding:i=0,avoidCollisions:j=!0,collisionBoundary:k=[],collisionPadding:m=0,sticky:n="partial",hideWhenDetached:p=!1,updatePositionStrategy:q="optimized",onPlaced:s,...t}=a,u=cP(cT,c),[v,w]=e.useState(null),x=(0,r.useComposedRefs)(b,a=>w(a)),[y,z]=e.useState(null),A=function(a){let[b,c]=e.useState(void 0);return o(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(y),B=A?.width??0,C=A?.height??0,D="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},E=Array.isArray(k)?k:[k],F=E.length>0,G={padding:D,boundary:E.filter(c$),altBoundary:F},{refs:H,floatingStyles:K,placement:L,isPositioned:M,middlewareData:N}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=e.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=e.useState(d);cH(n,d)||o(d);let[p,q]=e.useState(null),[r,s]=e.useState(null),t=e.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=e.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=e.useRef(null),y=e.useRef(null),z=e.useRef(l),A=null!=j,B=cJ(j),C=cJ(f),D=cJ(k),E=e.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:cD,...c},f={...e.platform,_c:d};return b_(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!cH(z.current,b)&&(z.current=b,Y.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);cG(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=e.useRef(!1);cG(()=>(F.current=!0,()=>{F.current=!1}),[]),cG(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=e.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=e.useMemo(()=>({reference:v,floating:w}),[v,w]),I=e.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=cI(H.floating,l.x),d=cI(H.floating,l.y);if(i)return{...a,transform:"translate("+b+"px, "+d+"px)",...(H.floating,false)};return{position:c,left:b,top:d}},[c,i,H.floating,l.x,l.y]);return e.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:d+("center"!==g?"-"+g:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=cr(a),l=f||g?[...k?co(k):[],...co(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=b7(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=bH(l),p=bH(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-bH(e.clientHeight-(l+n))+"px "+-bH(k)+"px",threshold:bF(0,bE(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||cE(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?cv(a):null;return j&&function b(){let d=cv(a);p&&!cE(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===q}),elements:{reference:u.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await b4(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:f+C,alignmentAxis:h}),j&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=bL(a,b),j={x:c,y:d},k=await b0(b,i),l=bR(bM(e)),m=bO(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=bF(c,bE(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=bF(c,bE(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===n?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=bL(a,b),k={x:c,y:d},l=bR(e),m=bO(l),n=k[m],o=k[l],p=bL(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=b3.has(bM(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...G}),j&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=bL(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=bM(h),v=bR(k),w=bM(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[bX(k)]:function(a){let b=bX(a);return[bS(a),b,bS(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=bN(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?bU:bT;return b?bT:bU;case"left":case"right":return b?bV:bW;default:return[]}}(bM(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(bS)))),f}(k,s,r,x));let A=[k,...y],B=await b0(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=bN(a),e=bO(bR(a)),f=bP(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=bX(g)),[g,bX(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===bR(b)||D.every(a=>bR(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=bR(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...G}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=bL(a,b),m=await b0(b,l),n=bM(g),o=bN(g),p="y"===bR(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=bE(r-m[e],s),v=bE(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=bF(m.left,0),b=bF(m.right,0),c=bF(m.top,0),d=bF(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:bF(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:bF(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...G,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),y&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?cF({element:c.current,padding:d}).fn(b):{}:c?cF({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:y,padding:i}),c_({arrowWidth:B,arrowHeight:C}),p&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=bL(a,b);switch(d){case"referenceHidden":{let a=b1(await b0(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:b2(a)}}}case"escaped":{let a=b1(await b0(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:b2(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...G})]}),[O,P]=c0(L),Q=J(s);o(()=>{M&&Q?.()},[M,Q]);let R=N.arrow?.x,S=N.arrow?.y,T=N.arrow?.centerOffset!==0,[U,V]=e.useState();return o(()=>{v&&V(window.getComputedStyle(v).zIndex)},[v]),(0,l.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:M?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,l.jsx)(cU,{scope:c,placedSide:O,onArrowChange:z,arrowX:R,arrowY:S,shouldHideArrow:T,children:(0,l.jsx)(I.Primitive.div,{"data-side":O,"data-align":P,...t,ref:x,style:{...t.style,animation:M?void 0:"none"}})})})});cW.displayName=cT;var cX="PopperArrow",cY={top:"bottom",right:"left",bottom:"top",left:"right"},cZ=e.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=cV(cX,c),f=cY[e.placedSide];return(0,l.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(cK,{...d,ref:b,style:{...d.style,display:"block"}})})});function c$(a){return null!==a}cZ.displayName=cX;var c_=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=c0(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function c0(a){let[b,c="center"]=a.split("-");return[b,c]}var c1=cQ,c2=cS,c3=cW,c4=cZ;a.s(["Root",()=>c7,"VISUALLY_HIDDEN_STYLES",()=>c5],28094);var c5=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),c6=e.forwardRef((a,b)=>(0,l.jsx)(I.Primitive.span,{...a,ref:b,style:{...c5,...a.style}}));c6.displayName="VisuallyHidden";var c7=c6,[c8,c9]=n("Tooltip",[cN]),da=cN(),db="TooltipProvider",dc="tooltip.open",[dd,de]=c8(db),df=a=>{let{__scopeTooltip:b,delayDuration:c=700,skipDelayDuration:d=300,disableHoverableContent:f=!1,children:g}=a,h=e.useRef(!0),i=e.useRef(!1),j=e.useRef(0);return e.useEffect(()=>{let a=j.current;return()=>window.clearTimeout(a)},[]),(0,l.jsx)(dd,{scope:b,isOpenDelayedRef:h,delayDuration:c,onOpen:e.useCallback(()=>{window.clearTimeout(j.current),h.current=!1},[]),onClose:e.useCallback(()=>{window.clearTimeout(j.current),j.current=window.setTimeout(()=>h.current=!0,d)},[d]),isPointerInTransitRef:i,onPointerInTransitChange:e.useCallback(a=>{i.current=a},[]),disableHoverableContent:f,children:g})};df.displayName=db;var dg="Tooltip",[dh,di]=c8(dg),dj=a=>{let{__scopeTooltip:b,children:c,open:d,defaultOpen:f,onOpenChange:g,disableHoverableContent:h,delayDuration:i}=a,j=de(dg,a.__scopeTooltip),k=da(b),[m,n]=e.useState(null),o=w(),p=e.useRef(0),r=h??j.disableHoverableContent,s=i??j.delayDuration,t=e.useRef(!1),[u,v]=q({prop:d,defaultProp:f??!1,onChange:a=>{a?(j.onOpen(),document.dispatchEvent(new CustomEvent(dc))):j.onClose(),g?.(a)},caller:dg}),x=e.useMemo(()=>u?t.current?"delayed-open":"instant-open":"closed",[u]),y=e.useCallback(()=>{window.clearTimeout(p.current),p.current=0,t.current=!1,v(!0)},[v]),z=e.useCallback(()=>{window.clearTimeout(p.current),p.current=0,v(!1)},[v]),A=e.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>{t.current=!0,v(!0),p.current=0},s)},[s,v]);return e.useEffect(()=>()=>{p.current&&(window.clearTimeout(p.current),p.current=0)},[]),(0,l.jsx)(c1,{...k,children:(0,l.jsx)(dh,{scope:b,contentId:o,open:u,stateAttribute:x,trigger:m,onTriggerChange:n,onTriggerEnter:e.useCallback(()=>{j.isOpenDelayedRef.current?A():y()},[j.isOpenDelayedRef,A,y]),onTriggerLeave:e.useCallback(()=>{r?z():(window.clearTimeout(p.current),p.current=0)},[z,r]),onOpen:y,onClose:z,disableHoverableContent:r,children:c})})};dj.displayName=dg;var dk="TooltipTrigger",dl=e.forwardRef((a,b)=>{let{__scopeTooltip:c,...d}=a,f=di(dk,c),g=de(dk,c),h=da(c),i=e.useRef(null),j=(0,r.useComposedRefs)(b,i,f.onTriggerChange),m=e.useRef(!1),n=e.useRef(!1),o=e.useCallback(()=>m.current=!1,[]);return e.useEffect(()=>()=>document.removeEventListener("pointerup",o),[o]),(0,l.jsx)(c2,{asChild:!0,...h,children:(0,l.jsx)(I.Primitive.button,{"aria-describedby":f.open?f.contentId:void 0,"data-state":f.stateAttribute,...d,ref:j,onPointerMove:k(a.onPointerMove,a=>{"touch"!==a.pointerType&&(n.current||g.isPointerInTransitRef.current||(f.onTriggerEnter(),n.current=!0))}),onPointerLeave:k(a.onPointerLeave,()=>{f.onTriggerLeave(),n.current=!1}),onPointerDown:k(a.onPointerDown,()=>{f.open&&f.onClose(),m.current=!0,document.addEventListener("pointerup",o,{once:!0})}),onFocus:k(a.onFocus,()=>{m.current||f.onOpen()}),onBlur:k(a.onBlur,f.onClose),onClick:k(a.onClick,f.onClose)})})});dl.displayName=dk;var dm="TooltipPortal",[dn,dp]=c8(dm,{forceMount:void 0}),dq=a=>{let{__scopeTooltip:b,forceMount:c,children:d,container:e}=a,f=di(dm,b);return(0,l.jsx)(dn,{scope:b,forceMount:c,children:(0,l.jsx)(s,{present:c||f.open,children:(0,l.jsx)(Z,{asChild:!0,container:e,children:d})})})};dq.displayName=dm;var dr="TooltipContent",ds=e.forwardRef((a,b)=>{let c=dp(dr,a.__scopeTooltip),{forceMount:d=c.forceMount,side:e="top",...f}=a,g=di(dr,a.__scopeTooltip);return(0,l.jsx)(s,{present:d||g.open,children:g.disableHoverableContent?(0,l.jsx)(dx,{side:e,...f,ref:b}):(0,l.jsx)(dt,{side:e,...f,ref:b})})}),dt=e.forwardRef((a,b)=>{let c=di(dr,a.__scopeTooltip),d=de(dr,a.__scopeTooltip),f=e.useRef(null),g=(0,r.useComposedRefs)(b,f),[h,i]=e.useState(null),{trigger:j,onClose:k}=c,m=f.current,{onPointerInTransitChange:n}=d,o=e.useCallback(()=>{i(null),n(!1)},[n]),p=e.useCallback((a,b)=>{let c=a.currentTarget,d={x:a.clientX,y:a.clientY},e=function(a,b){let c=Math.abs(b.top-a.y),d=Math.abs(b.bottom-a.y),e=Math.abs(b.right-a.x),f=Math.abs(b.left-a.x);switch(Math.min(c,d,e,f)){case f:return"left";case e:return"right";case c:return"top";case d:return"bottom";default:throw Error("unreachable")}}(d,c.getBoundingClientRect());i(function(a){let b=a.slice();return b.sort((a,b)=>a.x<b.x?-1:a.x>b.x?1:a.y<b.y?-1:1*!!(a.y>b.y)),function(a){if(a.length<=1)return a.slice();let b=[];for(let c=0;c<a.length;c++){let d=a[c];for(;b.length>=2;){let a=b[b.length-1],c=b[b.length-2];if((a.x-c.x)*(d.y-c.y)>=(a.y-c.y)*(d.x-c.x))b.pop();else break}b.push(d)}b.pop();let c=[];for(let b=a.length-1;b>=0;b--){let d=a[b];for(;c.length>=2;){let a=c[c.length-1],b=c[c.length-2];if((a.x-b.x)*(d.y-b.y)>=(a.y-b.y)*(d.x-b.x))c.pop();else break}c.push(d)}return(c.pop(),1===b.length&&1===c.length&&b[0].x===c[0].x&&b[0].y===c[0].y)?b:b.concat(c)}(b)}([...function(a,b,c=5){let d=[];switch(b){case"top":d.push({x:a.x-c,y:a.y+c},{x:a.x+c,y:a.y+c});break;case"bottom":d.push({x:a.x-c,y:a.y-c},{x:a.x+c,y:a.y-c});break;case"left":d.push({x:a.x+c,y:a.y-c},{x:a.x+c,y:a.y+c});break;case"right":d.push({x:a.x-c,y:a.y-c},{x:a.x-c,y:a.y+c})}return d}(d,e),...function(a){let{top:b,right:c,bottom:d,left:e}=a;return[{x:e,y:b},{x:c,y:b},{x:c,y:d},{x:e,y:d}]}(b.getBoundingClientRect())])),n(!0)},[n]);return e.useEffect(()=>()=>o(),[o]),e.useEffect(()=>{if(j&&m){let a=a=>p(a,m),b=a=>p(a,j);return j.addEventListener("pointerleave",a),m.addEventListener("pointerleave",b),()=>{j.removeEventListener("pointerleave",a),m.removeEventListener("pointerleave",b)}}},[j,m,p,o]),e.useEffect(()=>{if(h){let a=a=>{let b=a.target,c={x:a.clientX,y:a.clientY},d=j?.contains(b)||m?.contains(b),e=!function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}(c,h);d?o():e&&(o(),k())};return document.addEventListener("pointermove",a),()=>document.removeEventListener("pointermove",a)}},[j,m,h,k,o]),(0,l.jsx)(dx,{...a,ref:g})}),[du,dv]=c8(dg,{isInside:!1}),dw=(0,x.createSlottable)("TooltipContent"),dx=e.forwardRef((a,b)=>{let{__scopeTooltip:c,children:d,"aria-label":f,onEscapeKeyDown:g,onPointerDownOutside:h,...i}=a,j=di(dr,c),k=da(c),{onClose:m}=j;return e.useEffect(()=>(document.addEventListener(dc,m),()=>document.removeEventListener(dc,m)),[m]),e.useEffect(()=>{if(j.trigger){let a=a=>{let b=a.target;b?.contains(j.trigger)&&m()};return window.addEventListener("scroll",a,{capture:!0}),()=>window.removeEventListener("scroll",a,{capture:!0})}},[j.trigger,m]),(0,l.jsx)(M,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:a=>a.preventDefault(),onDismiss:m,children:(0,l.jsxs)(c3,{"data-state":j.stateAttribute,...k,...i,ref:b,style:{...i.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,l.jsx)(dw,{children:d}),(0,l.jsx)(du,{scope:c,isInside:!0,children:(0,l.jsx)(c7,{id:j.contentId,role:"tooltip",children:f||d})})]})})});ds.displayName=dr;var dy="TooltipArrow",dz=e.forwardRef((a,b)=>{let{__scopeTooltip:c,...d}=a,e=da(c);return dv(dy,c).isInside?null:(0,l.jsx)(c4,{...e,...d,ref:b})});function dA({delayDuration:a=0,...b}){return(0,l.jsx)(df,{"data-slot":"tooltip-provider",delayDuration:a,...b})}function dB({...a}){return(0,l.jsx)(dA,{children:(0,l.jsx)(dj,{"data-slot":"tooltip",...a})})}function dC({...a}){return(0,l.jsx)(dl,{"data-slot":"tooltip-trigger",...a})}function dD({className:a,sideOffset:b=0,children:c,...d}){return(0,l.jsx)(dq,{children:(0,l.jsxs)(ds,{"data-slot":"tooltip-content",sideOffset:b,className:(0,D.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...d,children:[c,(0,l.jsx)(dz,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}dz.displayName=dy;let dE=e.createContext(null);function dF(){let a=e.useContext(dE);if(!a)throw Error("useSidebar must be used within a SidebarProvider.");return a}function dG({defaultOpen:a=!0,open:b,onOpenChange:c,className:d,style:f,children:g,...h}){let i=function(){let[a,b]=e.useState(void 0);return e.useEffect(()=>{let a=window.matchMedia("(max-width: 767px)"),c=()=>{b(window.innerWidth<768)};return a.addEventListener("change",c),b(window.innerWidth<768),()=>a.removeEventListener("change",c)},[]),!!a}(),[j,k]=e.useState(!1),[m,n]=e.useState(a),o=b??m,p=e.useCallback(a=>{let b="function"==typeof a?a(o):a;c?c(b):n(b),document.cookie=`sidebar_state=${b}; path=/; max-age=604800`},[c,o]),q=e.useCallback(()=>i?k(a=>!a):p(a=>!a),[i,p,k]);e.useEffect(()=>{let a=a=>{"b"===a.key&&(a.metaKey||a.ctrlKey)&&(a.preventDefault(),q())};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[q]);let r=o?"expanded":"collapsed",s=e.useMemo(()=>({state:r,open:o,setOpen:p,isMobile:i,openMobile:j,setOpenMobile:k,toggleSidebar:q}),[r,o,p,i,j,k,q]);return(0,l.jsx)(dE.Provider,{value:s,children:(0,l.jsx)(dA,{delayDuration:0,children:(0,l.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...f},className:(0,D.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",d),...h,children:g})})})}function dH({side:a="left",variant:b="sidebar",collapsible:c="offcanvas",className:d,children:e,...f}){let{isMobile:g,state:h,openMobile:i,setOpenMobile:j}=dF();return"none"===c?(0,l.jsx)("div",{"data-slot":"sidebar",className:(0,D.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",d),...f,children:e}):g?(0,l.jsx)(bv,{open:i,onOpenChange:j,...f,children:(0,l.jsxs)(by,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:a,children:[(0,l.jsxs)(bz,{className:"sr-only",children:[(0,l.jsx)(bA,{children:"Sidebar"}),(0,l.jsx)(bB,{children:"Displays the mobile sidebar."})]}),(0,l.jsx)("div",{className:"flex h-full w-full flex-col",children:e})]})}):(0,l.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":h,"data-collapsible":"collapsed"===h?c:"","data-variant":b,"data-side":a,"data-slot":"sidebar",children:[(0,l.jsx)("div",{"data-slot":"sidebar-gap",className:(0,D.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===b||"inset"===b?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,l.jsx)("div",{"data-slot":"sidebar-container",className:(0,D.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===a?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===b||"inset"===b?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",d),...f,children:(0,l.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:e})})]})}function dI({className:a,onClick:b,...c}){let{toggleSidebar:d}=dF();return(0,l.jsxs)(F,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,D.cn)("size-7",a),onClick:a=>{b?.(a),d()},...c,children:[(0,l.jsx)(C,{}),(0,l.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function dJ({className:a,...b}){let{toggleSidebar:c}=dF();return(0,l.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:c,title:"Toggle Sidebar",className:(0,D.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",a),...b})}function dK({className:a,...b}){return(0,l.jsx)("main",{"data-slot":"sidebar-inset",className:(0,D.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",a),...b})}function dL({className:a,...b}){return(0,l.jsx)(G,{"data-slot":"sidebar-input","data-sidebar":"input",className:(0,D.cn)("bg-background h-8 w-full shadow-none",a),...b})}function dM({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,D.cn)("flex flex-col gap-2 p-2",a),...b})}function dN({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,D.cn)("flex flex-col gap-2 p-2",a),...b})}function dO({className:a,...b}){return(0,l.jsx)(H.Separator,{"data-slot":"sidebar-separator","data-sidebar":"separator",className:(0,D.cn)("bg-sidebar-border mx-2 w-auto",a),...b})}function dP({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,D.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",a),...b})}function dQ({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,D.cn)("relative flex w-full min-w-0 flex-col p-2",a),...b})}function dR({className:a,asChild:b=!1,...c}){let d=b?x.Slot:"div";return(0,l.jsx)(d,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,D.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",a),...c})}function dS({className:a,asChild:b=!1,...c}){let d=b?x.Slot:"button";return(0,l.jsx)(d,{"data-slot":"sidebar-group-action","data-sidebar":"group-action",className:(0,D.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","group-data-[collapsible=icon]:hidden",a),...c})}function dT({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,D.cn)("w-full text-sm",a),...b})}function dU({className:a,...b}){return(0,l.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,D.cn)("flex w-full min-w-0 flex-col gap-1",a),...b})}function dV({className:a,...b}){return(0,l.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,D.cn)("group/menu-item relative",a),...b})}let dW=B("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function dX({asChild:a=!1,isActive:b=!1,variant:c="default",size:d="default",tooltip:e,className:f,...g}){let h=a?x.Slot:"button",{isMobile:i,state:j}=dF(),k=(0,l.jsx)(h,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":d,"data-active":b,className:(0,D.cn)(dW({variant:c,size:d}),f),...g});return e?("string"==typeof e&&(e={children:e}),(0,l.jsxs)(dB,{children:[(0,l.jsx)(dC,{asChild:!0,children:k}),(0,l.jsx)(dD,{side:"right",align:"center",hidden:"collapsed"!==j||i,...e})]})):k}function dY({className:a,asChild:b=!1,showOnHover:c=!1,...d}){let e=b?x.Slot:"button";return(0,l.jsx)(e,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:(0,D.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",c&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",a),...d})}function dZ({className:a,...b}){return(0,l.jsx)("div",{"data-slot":"sidebar-menu-badge","data-sidebar":"menu-badge",className:(0,D.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",a),...b})}function d$({className:a,showIcon:b=!1,...c}){let d=e.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,l.jsxs)("div",{"data-slot":"sidebar-menu-skeleton","data-sidebar":"menu-skeleton",className:(0,D.cn)("flex h-8 items-center gap-2 rounded-md px-2",a),...c,children:[b&&(0,l.jsx)(bC,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,l.jsx)(bC,{className:"h-4 max-w-(--skeleton-width) flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}function d_({className:a,...b}){return(0,l.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,D.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",a),...b})}function d0({className:a,...b}){return(0,l.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,D.cn)("group/menu-sub-item relative",a),...b})}function d1({asChild:a=!1,size:b="md",isActive:c=!1,className:d,...e}){let f=a?x.Slot:"a";return(0,l.jsx)(f,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":b,"data-active":c,className:(0,D.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===b&&"text-xs","md"===b&&"text-sm","group-data-[collapsible=icon]:hidden",d),...e})}},39793,50522,17755,37738,7827,34157,76504,15618,65733,17171,16201,62722,41710,a=>{"use strict";a.s(["AppSidebar",()=>cn],39793);var b,c=a.i(87924),d=a.i(72131),e=a.i(70106);let f=(0,e.default)("radar",[["path",{d:"M19.07 4.93A10 10 0 0 0 6.99 3.34",key:"z3du51"}],["path",{d:"M4 6h.01",key:"oypzma"}],["path",{d:"M2.29 9.62A10 10 0 1 0 21.31 8.35",key:"qzzz0"}],["path",{d:"M16.24 7.76A6 6 0 1 0 8.23 16.67",key:"1yjesh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M17.99 11.66A6 6 0 0 1 15.77 16.67",key:"1u2y91"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"m13.41 10.59 5.66-5.66",key:"mhq4k0"}]]),g=(0,e.default)("frame",[["line",{x1:"22",x2:"2",y1:"6",y2:"6",key:"15w7dq"}],["line",{x1:"22",x2:"2",y1:"18",y2:"18",key:"1ip48p"}],["line",{x1:"6",x2:"6",y1:"2",y2:"22",key:"a2lnyx"}],["line",{x1:"18",x2:"18",y1:"2",y2:"22",key:"8vb6jd"}]]),h=(0,e.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),i=(0,e.default)("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),j=(0,e.default)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),k=(0,e.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);a.s(["ChevronRight",()=>l],50522);let l=(0,e.default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var m=a.i(7554),n=a.i(50104),o=a.i(25152),p=a.i(72752),q=a.i(70121),r=a.i(30553),s=a.i(77192),t=a.i(92843),u="Collapsible",[v,w]=(0,n.createContextScope)(u),[x,y]=v(u),z=d.forwardRef((a,b)=>{let{__scopeCollapsible:e,open:f,defaultOpen:g,disabled:h,onOpenChange:i,...j}=a,[k,l]=(0,o.useControllableState)({prop:f,defaultProp:g??!1,onChange:i,caller:u});return(0,c.jsx)(x,{scope:e,disabled:h,contentId:(0,t.useId)(),open:k,onOpenToggle:d.useCallback(()=>l(a=>!a),[l]),children:(0,c.jsx)(r.Primitive.div,{"data-state":F(k),"data-disabled":h?"":void 0,...j,ref:b})})});z.displayName=u;var A="CollapsibleTrigger",B=d.forwardRef((a,b)=>{let{__scopeCollapsible:d,...e}=a,f=y(A,d);return(0,c.jsx)(r.Primitive.button,{type:"button","aria-controls":f.contentId,"aria-expanded":f.open||!1,"data-state":F(f.open),"data-disabled":f.disabled?"":void 0,disabled:f.disabled,...e,ref:b,onClick:(0,m.composeEventHandlers)(a.onClick,f.onOpenToggle)})});B.displayName=A;var C="CollapsibleContent",D=d.forwardRef((a,b)=>{let{forceMount:d,...e}=a,f=y(C,a.__scopeCollapsible);return(0,c.jsx)(s.Presence,{present:d||f.open,children:({present:a})=>(0,c.jsx)(E,{...e,ref:b,present:a})})});D.displayName=C;var E=d.forwardRef((a,b)=>{let{__scopeCollapsible:e,present:f,children:g,...h}=a,i=y(C,e),[j,k]=d.useState(f),l=d.useRef(null),m=(0,q.useComposedRefs)(b,l),n=d.useRef(0),o=n.current,s=d.useRef(0),t=s.current,u=i.open||j,v=d.useRef(u),w=d.useRef(void 0);return d.useEffect(()=>{let a=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,p.useLayoutEffect)(()=>{let a=l.current;if(a){w.current=w.current||{transitionDuration:a.style.transitionDuration,animationName:a.style.animationName},a.style.transitionDuration="0s",a.style.animationName="none";let b=a.getBoundingClientRect();n.current=b.height,s.current=b.width,v.current||(a.style.transitionDuration=w.current.transitionDuration,a.style.animationName=w.current.animationName),k(f)}},[i.open,f]),(0,c.jsx)(r.Primitive.div,{"data-state":F(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!u,...h,ref:m,style:{"--radix-collapsible-content-height":o?`${o}px`:void 0,"--radix-collapsible-content-width":t?`${t}px`:void 0,...a.style},children:u&&g})});function F(a){return a?"open":"closed"}function G({...a}){return(0,c.jsx)(z,{"data-slot":"collapsible",...a})}function H({...a}){return(0,c.jsx)(B,{"data-slot":"collapsible-trigger",...a})}function I({...a}){return(0,c.jsx)(D,{"data-slot":"collapsible-content",...a})}var J=a.i(14727);function K({items:a,onNavigate:b}){return(0,c.jsxs)(J.SidebarGroup,{children:[(0,c.jsx)(J.SidebarGroupLabel,{children:"Platform"}),(0,c.jsx)(J.SidebarMenu,{children:a.map(a=>a.items&&a.items.length>0?(0,c.jsx)(G,{asChild:!0,defaultOpen:a.isActive,className:"group/collapsible",children:(0,c.jsxs)(J.SidebarMenuItem,{children:[(0,c.jsx)(H,{asChild:!0,children:(0,c.jsxs)(J.SidebarMenuButton,{tooltip:a.title,children:[a.icon&&(0,c.jsx)(a.icon,{}),(0,c.jsx)("span",{children:a.title}),(0,c.jsx)(l,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,c.jsx)(I,{children:(0,c.jsx)(J.SidebarMenuSub,{children:a.items?.map(a=>(0,c.jsx)(J.SidebarMenuSubItem,{children:(0,c.jsx)(J.SidebarMenuSubButton,{onClick:a.onClick,className:"cursor-pointer",children:(0,c.jsx)("span",{children:a.title})})},a.title))})})]})},a.title):(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(J.SidebarMenuButton,{onClick:a.onClick,tooltip:a.title,className:"cursor-pointer",children:[a.icon&&(0,c.jsx)(a.icon,{}),(0,c.jsx)("span",{children:a.title})]})},a.title))})]})}let L=(0,e.default)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),M=(0,e.default)("forward",[["path",{d:"m15 17 5-5-5-5",key:"nf172w"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]]);a.s(["MoreHorizontal",()=>N],17755);let N=(0,e.default)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),O=(0,e.default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);a.s(["DropdownMenu",()=>bI,"DropdownMenuCheckboxItem",()=>bN,"DropdownMenuContent",()=>bK,"DropdownMenuGroup",()=>bL,"DropdownMenuItem",()=>bM,"DropdownMenuLabel",()=>bO,"DropdownMenuSeparator",()=>bP,"DropdownMenuShortcut",()=>bQ,"DropdownMenuTrigger",()=>bJ],76504),a.s(["createCollection",()=>Q],37738);var P=a.i(11011);function Q(a){let b=a+"CollectionProvider",[e,f]=(0,n.createContextScope)(b),[g,h]=e(b,{collectionRef:{current:null},itemMap:new Map}),i=a=>{let{scope:b,children:e}=a,f=d.default.useRef(null),h=d.default.useRef(new Map).current;return(0,c.jsx)(g,{scope:b,itemMap:h,collectionRef:f,children:e})};i.displayName=b;let j=a+"CollectionSlot",k=(0,P.createSlot)(j),l=d.default.forwardRef((a,b)=>{let{scope:d,children:e}=a,f=h(j,d),g=(0,q.useComposedRefs)(b,f.collectionRef);return(0,c.jsx)(k,{ref:g,children:e})});l.displayName=j;let m=a+"CollectionItemSlot",o="data-radix-collection-item",p=(0,P.createSlot)(m),r=d.default.forwardRef((a,b)=>{let{scope:e,children:f,...g}=a,i=d.default.useRef(null),j=(0,q.useComposedRefs)(b,i),k=h(m,e);return d.default.useEffect(()=>(k.itemMap.set(i,{ref:i,...g}),()=>void k.itemMap.delete(i))),(0,c.jsx)(p,{...{[o]:""},ref:j,children:f})});return r.displayName=m,[{Provider:i,Slot:l,ItemSlot:r},function(b){let c=h(a+"CollectionConsumer",b);return d.default.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${o}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},f]}var R=new WeakMap;function S(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=T(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function T(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],R.set(this,!0)}set(a,b){return R.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=T(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=S(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=S(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return S(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}}),a.s(["useDirection",()=>V],7827);var U=d.createContext(void 0);function V(a){let b=d.useContext(U);return a||b||"ltr"}var W=a.i(96743),X=a.i(86228),Y=a.i(22297),Z=a.i(4691),$=a.i(92616),_=a.i(46872),aa="rovingFocusGroup.onEntryFocus",ab={bubbles:!1,cancelable:!0},ac="RovingFocusGroup",[ad,ae,af]=Q(ac),[ag,ah]=(0,n.createContextScope)(ac,[af]),[ai,aj]=ag(ac),ak=d.forwardRef((a,b)=>(0,c.jsx)(ad.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,c.jsx)(ad.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,c.jsx)(al,{...a,ref:b})})}));ak.displayName=ac;var al=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:e,orientation:f,loop:g=!1,dir:h,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:n=!1,...p}=a,s=d.useRef(null),t=(0,q.useComposedRefs)(b,s),u=V(h),[v,w]=(0,o.useControllableState)({prop:i,defaultProp:j??null,onChange:k,caller:ac}),[x,y]=d.useState(!1),z=(0,_.useCallbackRef)(l),A=ae(e),B=d.useRef(!1),[C,D]=d.useState(0);return d.useEffect(()=>{let a=s.current;if(a)return a.addEventListener(aa,z),()=>a.removeEventListener(aa,z)},[z]),(0,c.jsx)(ai,{scope:e,orientation:f,dir:u,loop:g,currentTabStopId:v,onItemFocus:d.useCallback(a=>w(a),[w]),onItemShiftTab:d.useCallback(()=>y(!0),[]),onFocusableItemAdd:d.useCallback(()=>D(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>D(a=>a-1),[]),children:(0,c.jsx)(r.Primitive.div,{tabIndex:x||0===C?-1:0,"data-orientation":f,...p,ref:t,style:{outline:"none",...a.style},onMouseDown:(0,m.composeEventHandlers)(a.onMouseDown,()=>{B.current=!0}),onFocus:(0,m.composeEventHandlers)(a.onFocus,a=>{let b=!B.current;if(a.target===a.currentTarget&&b&&!x){let b=new CustomEvent(aa,ab);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=A().filter(a=>a.focusable);ap([a.find(a=>a.active),a.find(a=>a.id===v),...a].filter(Boolean).map(a=>a.ref.current),n)}}B.current=!1}),onBlur:(0,m.composeEventHandlers)(a.onBlur,()=>y(!1))})})}),am="RovingFocusGroupItem",an=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:e,focusable:f=!0,active:g=!1,tabStopId:h,children:i,...j}=a,k=(0,t.useId)(),l=h||k,n=aj(am,e),o=n.currentTabStopId===l,p=ae(e),{onFocusableItemAdd:q,onFocusableItemRemove:s,currentTabStopId:u}=n;return d.useEffect(()=>{if(f)return q(),()=>s()},[f,q,s]),(0,c.jsx)(ad.ItemSlot,{scope:e,id:l,focusable:f,active:g,children:(0,c.jsx)(r.Primitive.span,{tabIndex:o?0:-1,"data-orientation":n.orientation,...j,ref:b,onMouseDown:(0,m.composeEventHandlers)(a.onMouseDown,a=>{f?n.onItemFocus(l):a.preventDefault()}),onFocus:(0,m.composeEventHandlers)(a.onFocus,()=>n.onItemFocus(l)),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void n.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return ao[e]}(a,n.orientation,n.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=p().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=n.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>ap(c))}}),children:"function"==typeof i?i({isCurrentTabStop:o,hasTabStop:null!=u}):i})})});an.displayName=am;var ao={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ap(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var aq=a.i(41852),ar=a.i(52081),as=["Enter"," "],at=["ArrowUp","PageDown","End"],au=["ArrowDown","PageUp","Home",...at],av={ltr:[...as,"ArrowRight"],rtl:[...as,"ArrowLeft"]},aw={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ax="Menu",[ay,az,aA]=Q(ax),[aB,aC]=(0,n.createContextScope)(ax,[aA,Z.createPopperScope,ah]),aD=(0,Z.createPopperScope)(),aE=ah(),[aF,aG]=aB(ax),[aH,aI]=aB(ax),aJ=a=>{let{__scopeMenu:b,open:e=!1,children:f,dir:g,onOpenChange:h,modal:i=!0}=a,j=aD(b),[k,l]=d.useState(null),m=d.useRef(!1),n=(0,_.useCallbackRef)(h),o=V(g);return d.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,c.jsx)(Z.Root,{...j,children:(0,c.jsx)(aF,{scope:b,open:e,onOpenChange:n,content:k,onContentChange:l,children:(0,c.jsx)(aH,{scope:b,onClose:d.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:i,children:f})})})};aJ.displayName=ax;var aK=d.forwardRef((a,b)=>{let{__scopeMenu:d,...e}=a,f=aD(d);return(0,c.jsx)(Z.Anchor,{...f,...e,ref:b})});aK.displayName="MenuAnchor";var aL="MenuPortal",[aM,aN]=aB(aL,{forceMount:void 0}),aO=a=>{let{__scopeMenu:b,forceMount:d,children:e,container:f}=a,g=aG(aL,b);return(0,c.jsx)(aM,{scope:b,forceMount:d,children:(0,c.jsx)(s.Presence,{present:d||g.open,children:(0,c.jsx)($.Portal,{asChild:!0,container:f,children:e})})})};aO.displayName=aL;var aP="MenuContent",[aQ,aR]=aB(aP),aS=d.forwardRef((a,b)=>{let d=aN(aP,a.__scopeMenu),{forceMount:e=d.forceMount,...f}=a,g=aG(aP,a.__scopeMenu),h=aI(aP,a.__scopeMenu);return(0,c.jsx)(ay.Provider,{scope:a.__scopeMenu,children:(0,c.jsx)(s.Presence,{present:e||g.open,children:(0,c.jsx)(ay.Slot,{scope:a.__scopeMenu,children:h.modal?(0,c.jsx)(aT,{...f,ref:b}):(0,c.jsx)(aU,{...f,ref:b})})})})}),aT=d.forwardRef((a,b)=>{let e=aG(aP,a.__scopeMenu),f=d.useRef(null),g=(0,q.useComposedRefs)(b,f);return d.useEffect(()=>{let a=f.current;if(a)return(0,aq.hideOthers)(a)},[]),(0,c.jsx)(aW,{...a,ref:g,trapFocus:e.open,disableOutsidePointerEvents:e.open,disableOutsideScroll:!0,onFocusOutside:(0,m.composeEventHandlers)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>e.onOpenChange(!1)})}),aU=d.forwardRef((a,b)=>{let d=aG(aP,a.__scopeMenu);return(0,c.jsx)(aW,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>d.onOpenChange(!1)})}),aV=(0,P.createSlot)("MenuContent.ScrollLock"),aW=d.forwardRef((a,b)=>{let{__scopeMenu:e,loop:f=!1,trapFocus:g,onOpenAutoFocus:h,onCloseAutoFocus:i,disableOutsidePointerEvents:j,onEntryFocus:k,onEscapeKeyDown:l,onPointerDownOutside:n,onFocusOutside:o,onInteractOutside:p,onDismiss:r,disableOutsideScroll:s,...t}=a,u=aG(aP,e),v=aI(aP,e),w=aD(e),x=aE(e),y=az(e),[z,A]=d.useState(null),B=d.useRef(null),C=(0,q.useComposedRefs)(b,B,u.onContentChange),D=d.useRef(0),E=d.useRef(""),F=d.useRef(0),G=d.useRef(null),H=d.useRef("right"),I=d.useRef(0),J=s?ar.RemoveScroll:d.Fragment;d.useEffect(()=>()=>window.clearTimeout(D.current),[]),(0,X.useFocusGuards)();let K=d.useCallback(a=>H.current===G.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,G.current?.area),[]);return(0,c.jsx)(aQ,{scope:e,searchRef:E,onItemEnter:d.useCallback(a=>{K(a)&&a.preventDefault()},[K]),onItemLeave:d.useCallback(a=>{K(a)||(B.current?.focus(),A(null))},[K]),onTriggerLeave:d.useCallback(a=>{K(a)&&a.preventDefault()},[K]),pointerGraceTimerRef:F,onPointerGraceIntentChange:d.useCallback(a=>{G.current=a},[]),children:(0,c.jsx)(J,{...s?{as:aV,allowPinchZoom:!0}:void 0,children:(0,c.jsx)(Y.FocusScope,{asChild:!0,trapped:g,onMountAutoFocus:(0,m.composeEventHandlers)(h,a=>{a.preventDefault(),B.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,c.jsx)(W.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:j,onEscapeKeyDown:l,onPointerDownOutside:n,onFocusOutside:o,onInteractOutside:p,onDismiss:r,children:(0,c.jsx)(ak,{asChild:!0,...x,dir:v.dir,orientation:"vertical",loop:f,currentTabStopId:z,onCurrentTabStopIdChange:A,onEntryFocus:(0,m.composeEventHandlers)(k,a=>{v.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,c.jsx)(Z.Content,{role:"menu","aria-orientation":"vertical","data-state":bk(u.open),"data-radix-menu-content":"",dir:v.dir,...w,...t,ref:C,style:{outline:"none",...t.style},onKeyDown:(0,m.composeEventHandlers)(t.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=E.current+a,c=y().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){E.current=b,window.clearTimeout(D.current),""!==b&&(D.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=B.current;if(a.target!==e||!au.includes(a.key))return;a.preventDefault();let f=y().filter(a=>!a.disabled).map(a=>a.ref.current);at.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,m.composeEventHandlers)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(D.current),E.current="")}),onPointerMove:(0,m.composeEventHandlers)(a.onPointerMove,bn(a=>{let b=a.target,c=I.current!==a.clientX;a.currentTarget.contains(b)&&c&&(H.current=a.clientX>I.current?"right":"left",I.current=a.clientX)}))})})})})})})});aS.displayName=aP;var aX=d.forwardRef((a,b)=>{let{__scopeMenu:d,...e}=a;return(0,c.jsx)(r.Primitive.div,{role:"group",...e,ref:b})});aX.displayName="MenuGroup";var aY=d.forwardRef((a,b)=>{let{__scopeMenu:d,...e}=a;return(0,c.jsx)(r.Primitive.div,{...e,ref:b})});aY.displayName="MenuLabel";var aZ="MenuItem",a$="menu.itemSelect",a_=d.forwardRef((a,b)=>{let{disabled:e=!1,onSelect:f,...g}=a,h=d.useRef(null),i=aI(aZ,a.__scopeMenu),j=aR(aZ,a.__scopeMenu),k=(0,q.useComposedRefs)(b,h),l=d.useRef(!1);return(0,c.jsx)(a0,{...g,ref:k,disabled:e,onClick:(0,m.composeEventHandlers)(a.onClick,()=>{let a=h.current;if(!e&&a){let b=new CustomEvent(a$,{bubbles:!0,cancelable:!0});a.addEventListener(a$,a=>f?.(a),{once:!0}),(0,r.dispatchDiscreteCustomEvent)(a,b),b.defaultPrevented?l.current=!1:i.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),l.current=!0},onPointerUp:(0,m.composeEventHandlers)(a.onPointerUp,a=>{l.current||a.currentTarget?.click()}),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,a=>{let b=""!==j.searchRef.current;e||b&&" "===a.key||as.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});a_.displayName=aZ;var a0=d.forwardRef((a,b)=>{let{__scopeMenu:e,disabled:f=!1,textValue:g,...h}=a,i=aR(aZ,e),j=aE(e),k=d.useRef(null),l=(0,q.useComposedRefs)(b,k),[n,o]=d.useState(!1),[p,s]=d.useState("");return d.useEffect(()=>{let a=k.current;a&&s((a.textContent??"").trim())},[h.children]),(0,c.jsx)(ay.ItemSlot,{scope:e,disabled:f,textValue:g??p,children:(0,c.jsx)(an,{asChild:!0,...j,focusable:!f,children:(0,c.jsx)(r.Primitive.div,{role:"menuitem","data-highlighted":n?"":void 0,"aria-disabled":f||void 0,"data-disabled":f?"":void 0,...h,ref:l,onPointerMove:(0,m.composeEventHandlers)(a.onPointerMove,bn(a=>{f?i.onItemLeave(a):(i.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,m.composeEventHandlers)(a.onPointerLeave,bn(a=>i.onItemLeave(a))),onFocus:(0,m.composeEventHandlers)(a.onFocus,()=>o(!0)),onBlur:(0,m.composeEventHandlers)(a.onBlur,()=>o(!1))})})})}),a1=d.forwardRef((a,b)=>{let{checked:d=!1,onCheckedChange:e,...f}=a;return(0,c.jsx)(a9,{scope:a.__scopeMenu,checked:d,children:(0,c.jsx)(a_,{role:"menuitemcheckbox","aria-checked":bl(d)?"mixed":d,...f,ref:b,"data-state":bm(d),onSelect:(0,m.composeEventHandlers)(f.onSelect,()=>e?.(!!bl(d)||!d),{checkForDefaultPrevented:!1})})})});a1.displayName="MenuCheckboxItem";var a2="MenuRadioGroup",[a3,a4]=aB(a2,{value:void 0,onValueChange:()=>{}}),a5=d.forwardRef((a,b)=>{let{value:d,onValueChange:e,...f}=a,g=(0,_.useCallbackRef)(e);return(0,c.jsx)(a3,{scope:a.__scopeMenu,value:d,onValueChange:g,children:(0,c.jsx)(aX,{...f,ref:b})})});a5.displayName=a2;var a6="MenuRadioItem",a7=d.forwardRef((a,b)=>{let{value:d,...e}=a,f=a4(a6,a.__scopeMenu),g=d===f.value;return(0,c.jsx)(a9,{scope:a.__scopeMenu,checked:g,children:(0,c.jsx)(a_,{role:"menuitemradio","aria-checked":g,...e,ref:b,"data-state":bm(g),onSelect:(0,m.composeEventHandlers)(e.onSelect,()=>f.onValueChange?.(d),{checkForDefaultPrevented:!1})})})});a7.displayName=a6;var a8="MenuItemIndicator",[a9,ba]=aB(a8,{checked:!1}),bb=d.forwardRef((a,b)=>{let{__scopeMenu:d,forceMount:e,...f}=a,g=ba(a8,d);return(0,c.jsx)(s.Presence,{present:e||bl(g.checked)||!0===g.checked,children:(0,c.jsx)(r.Primitive.span,{...f,ref:b,"data-state":bm(g.checked)})})});bb.displayName=a8;var bc=d.forwardRef((a,b)=>{let{__scopeMenu:d,...e}=a;return(0,c.jsx)(r.Primitive.div,{role:"separator","aria-orientation":"horizontal",...e,ref:b})});bc.displayName="MenuSeparator";var bd=d.forwardRef((a,b)=>{let{__scopeMenu:d,...e}=a,f=aD(d);return(0,c.jsx)(Z.Arrow,{...f,...e,ref:b})});bd.displayName="MenuArrow";var[be,bf]=aB("MenuSub"),bg="MenuSubTrigger",bh=d.forwardRef((a,b)=>{let e=aG(bg,a.__scopeMenu),f=aI(bg,a.__scopeMenu),g=bf(bg,a.__scopeMenu),h=aR(bg,a.__scopeMenu),i=d.useRef(null),{pointerGraceTimerRef:j,onPointerGraceIntentChange:k}=h,l={__scopeMenu:a.__scopeMenu},n=d.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return d.useEffect(()=>n,[n]),d.useEffect(()=>{let a=j.current;return()=>{window.clearTimeout(a),k(null)}},[j,k]),(0,c.jsx)(aK,{asChild:!0,...l,children:(0,c.jsx)(a0,{id:g.triggerId,"aria-haspopup":"menu","aria-expanded":e.open,"aria-controls":g.contentId,"data-state":bk(e.open),...a,ref:(0,q.composeRefs)(b,g.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),e.open||e.onOpenChange(!0))},onPointerMove:(0,m.composeEventHandlers)(a.onPointerMove,bn(b=>{h.onItemEnter(b),!b.defaultPrevented&&(a.disabled||e.open||i.current||(h.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{e.onOpenChange(!0),n()},100)))})),onPointerLeave:(0,m.composeEventHandlers)(a.onPointerLeave,bn(a=>{n();let b=e.content?.getBoundingClientRect();if(b){let c=e.content?.dataset.side,d="right"===c,f=b[d?"left":"right"],g=b[d?"right":"left"];h.onPointerGraceIntentChange({area:[{x:a.clientX+(d?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:c}),window.clearTimeout(j.current),j.current=window.setTimeout(()=>h.onPointerGraceIntentChange(null),300)}else{if(h.onTriggerLeave(a),a.defaultPrevented)return;h.onPointerGraceIntentChange(null)}})),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,b=>{let c=""!==h.searchRef.current;a.disabled||c&&" "===b.key||av[f.dir].includes(b.key)&&(e.onOpenChange(!0),e.content?.focus(),b.preventDefault())})})})});bh.displayName=bg;var bi="MenuSubContent",bj=d.forwardRef((a,b)=>{let e=aN(aP,a.__scopeMenu),{forceMount:f=e.forceMount,...g}=a,h=aG(aP,a.__scopeMenu),i=aI(aP,a.__scopeMenu),j=bf(bi,a.__scopeMenu),k=d.useRef(null),l=(0,q.useComposedRefs)(b,k);return(0,c.jsx)(ay.Provider,{scope:a.__scopeMenu,children:(0,c.jsx)(s.Presence,{present:f||h.open,children:(0,c.jsx)(ay.Slot,{scope:a.__scopeMenu,children:(0,c.jsx)(aW,{id:j.contentId,"aria-labelledby":j.triggerId,...g,ref:l,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{i.isUsingKeyboardRef.current&&k.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,m.composeEventHandlers)(a.onFocusOutside,a=>{a.target!==j.trigger&&h.onOpenChange(!1)}),onEscapeKeyDown:(0,m.composeEventHandlers)(a.onEscapeKeyDown,a=>{i.onClose(),a.preventDefault()}),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=aw[i.dir].includes(a.key);b&&c&&(h.onOpenChange(!1),j.trigger?.focus(),a.preventDefault())})})})})})});function bk(a){return a?"open":"closed"}function bl(a){return"indeterminate"===a}function bm(a){return bl(a)?"indeterminate":a?"checked":"unchecked"}function bn(a){return b=>"mouse"===b.pointerType?a(b):void 0}bj.displayName=bi;var bo="DropdownMenu",[bp,bq]=(0,n.createContextScope)(bo,[aC]),br=aC(),[bs,bt]=bp(bo),bu=a=>{let{__scopeDropdownMenu:b,children:e,dir:f,open:g,defaultOpen:h,onOpenChange:i,modal:j=!0}=a,k=br(b),l=d.useRef(null),[m,n]=(0,o.useControllableState)({prop:g,defaultProp:h??!1,onChange:i,caller:bo});return(0,c.jsx)(bs,{scope:b,triggerId:(0,t.useId)(),triggerRef:l,contentId:(0,t.useId)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,c.jsx)(aJ,{...k,open:m,onOpenChange:n,dir:f,modal:j,children:e})})};bu.displayName=bo;var bv="DropdownMenuTrigger",bw=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,disabled:e=!1,...f}=a,g=bt(bv,d),h=br(d);return(0,c.jsx)(aK,{asChild:!0,...h,children:(0,c.jsx)(r.Primitive.button,{type:"button",id:g.triggerId,"aria-haspopup":"menu","aria-expanded":g.open,"aria-controls":g.open?g.contentId:void 0,"data-state":g.open?"open":"closed","data-disabled":e?"":void 0,disabled:e,...f,ref:(0,q.composeRefs)(b,g.triggerRef),onPointerDown:(0,m.composeEventHandlers)(a.onPointerDown,a=>{!e&&0===a.button&&!1===a.ctrlKey&&(g.onOpenToggle(),g.open||a.preventDefault())}),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,a=>{!e&&(["Enter"," "].includes(a.key)&&g.onOpenToggle(),"ArrowDown"===a.key&&g.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});bw.displayName=bv;var bx=a=>{let{__scopeDropdownMenu:b,...d}=a,e=br(b);return(0,c.jsx)(aO,{...e,...d})};bx.displayName="DropdownMenuPortal";var by="DropdownMenuContent",bz=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:e,...f}=a,g=bt(by,e),h=br(e),i=d.useRef(!1);return(0,c.jsx)(aS,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...f,ref:b,onCloseAutoFocus:(0,m.composeEventHandlers)(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:(0,m.composeEventHandlers)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});bz.displayName=by;var bA=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(aX,{...f,...e,ref:b})});bA.displayName="DropdownMenuGroup";var bB=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(aY,{...f,...e,ref:b})});bB.displayName="DropdownMenuLabel";var bC=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(a_,{...f,...e,ref:b})});bC.displayName="DropdownMenuItem";var bD=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(a1,{...f,...e,ref:b})});bD.displayName="DropdownMenuCheckboxItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(a5,{...f,...e,ref:b})}).displayName="DropdownMenuRadioGroup",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(a7,{...f,...e,ref:b})}).displayName="DropdownMenuRadioItem";var bE=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(bb,{...f,...e,ref:b})});bE.displayName="DropdownMenuItemIndicator";var bF=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(bc,{...f,...e,ref:b})});bF.displayName="DropdownMenuSeparator",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(bd,{...f,...e,ref:b})}).displayName="DropdownMenuArrow",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(bh,{...f,...e,ref:b})}).displayName="DropdownMenuSubTrigger",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:d,...e}=a,f=br(d);return(0,c.jsx)(bj,{...f,...e,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent",a.s(["default",()=>bG],34157);let bG=(0,e.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);(0,e.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var bH=a.i(97895);function bI({...a}){return(0,c.jsx)(bu,{"data-slot":"dropdown-menu",...a})}function bJ({...a}){return(0,c.jsx)(bw,{"data-slot":"dropdown-menu-trigger",...a})}function bK({className:a,sideOffset:b=4,...d}){return(0,c.jsx)(bx,{children:(0,c.jsx)(bz,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,bH.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...d})})}function bL({...a}){return(0,c.jsx)(bA,{"data-slot":"dropdown-menu-group",...a})}function bM({className:a,inset:b,variant:d="default",...e}){return(0,c.jsx)(bC,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":d,className:(0,bH.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function bN({className:a,children:b,checked:d,...e}){return(0,c.jsxs)(bD,{"data-slot":"dropdown-menu-checkbox-item",className:(0,bH.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),checked:d,...e,children:[(0,c.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,c.jsx)(bE,{children:(0,c.jsx)(bG,{className:"size-4"})})}),b]})}function bO({className:a,inset:b,...d}){return(0,c.jsx)(bB,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,bH.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...d})}function bP({className:a,...b}){return(0,c.jsx)(bF,{"data-slot":"dropdown-menu-separator",className:(0,bH.cn)("bg-border -mx-1 my-1 h-px",a),...b})}function bQ({className:a,...b}){return(0,c.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,bH.cn)("text-muted-foreground ml-auto text-xs tracking-widest",a),...b})}function bR({projects:a}){let{isMobile:b}=(0,J.useSidebar)();return(0,c.jsxs)(J.SidebarGroup,{className:"group-data-[collapsible=icon]:hidden",children:[(0,c.jsx)(J.SidebarGroupLabel,{children:"Projects"}),(0,c.jsxs)(J.SidebarMenu,{children:[a.map(a=>(0,c.jsxs)(J.SidebarMenuItem,{children:[(0,c.jsx)(J.SidebarMenuButton,{asChild:!0,children:(0,c.jsxs)("a",{href:a.url,children:[(0,c.jsx)(a.icon,{}),(0,c.jsx)("span",{children:a.name})]})}),(0,c.jsxs)(bI,{children:[(0,c.jsx)(bJ,{asChild:!0,children:(0,c.jsxs)(J.SidebarMenuAction,{showOnHover:!0,children:[(0,c.jsx)(N,{}),(0,c.jsx)("span",{className:"sr-only",children:"More"})]})}),(0,c.jsxs)(bK,{className:"w-48 rounded-lg",side:b?"bottom":"right",align:b?"end":"start",children:[(0,c.jsxs)(bM,{children:[(0,c.jsx)(L,{className:"text-muted-foreground"}),(0,c.jsx)("span",{children:"View Project"})]}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(M,{className:"text-muted-foreground"}),(0,c.jsx)("span",{children:"Share Project"})]}),(0,c.jsx)(bP,{}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(O,{className:"text-muted-foreground"}),(0,c.jsx)("span",{children:"Delete Project"})]})]})]})]},a.name)),(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(J.SidebarMenuButton,{className:"text-sidebar-foreground/70",children:[(0,c.jsx)(N,{className:"text-sidebar-foreground/70"}),(0,c.jsx)("span",{children:"More"})]})})]})]})}let bS=(0,e.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),bT=(0,e.default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),bU=(0,e.default)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),bV=(0,e.default)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),bW=(0,e.default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);function bX(){let{isMobile:a}=(0,J.useSidebar)();return(0,c.jsx)(J.SidebarMenu,{children:(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(bI,{children:[(0,c.jsx)(bJ,{asChild:!0,children:(0,c.jsxs)(J.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",tooltip:"Settings",children:[(0,c.jsx)(bS,{className:"size-4"}),(0,c.jsx)("span",{className:"ml-2 truncate font-medium data-[collapsed]:hidden",children:"Settings"})]})}),(0,c.jsxs)(bK,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:a?"bottom":"right",align:"end",sideOffset:4,children:[(0,c.jsxs)(bL,{children:[(0,c.jsxs)(bM,{children:[(0,c.jsx)(bW,{}),"Account"]}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(bV,{}),"Security"]}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(bT,{}),"Notifications"]}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(bU,{}),"Appearance"]})]}),(0,c.jsx)(bP,{}),(0,c.jsxs)(bM,{children:[(0,c.jsx)(bS,{}),"Settings"]})]})]})})})}let bY=(0,e.default)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);a.s(["Plus",()=>bZ],15618);let bZ=(0,e.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),b$=(0,e.default)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),b_=(0,e.default)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);var b0=a.i(2439);function b1({onAddBackend:a,onEditBackend:b,refreshTrigger:e}){let{isMobile:f}=(0,J.useSidebar)(),[g,h]=d.useState([]),[i,j]=d.useState(null),[k,l]=d.useState(!0);d.useEffect(()=>{m()},[e]),d.useEffect(()=>{let a=setInterval(async()=>{try{let a=await (0,b0.GetBackends)();h(a.backends||[]),j(b=>b&&a.backends?.find(a=>a.id===b.id)||b)}catch(a){console.debug("Failed to refresh latency data:",a)}},2e3);return()=>clearInterval(a)},[]);let m=async()=>{try{l(!0);let a=await (0,b0.GetBackends)();h(a.backends||[]);try{let a=await (0,b0.GetActiveBackend)();j(a)}catch{a.backends&&a.backends.length>0?j(a.backends[0]):j(null)}}catch(a){console.error("Failed to load backends:",a)}finally{l(!1)}},n=async a=>{try{await (0,b0.SetActiveBackend)(a.id),j(a)}catch(a){console.error("Failed to set active backend:",a)}},o=a=>{if("connected"===a.status&&a.latency>=0)if(a.latency<100)return{text:`${a.latency}ms`,color:"text-green-600"};else if(a.latency<500)return{text:`${a.latency}ms`,color:"text-yellow-600"};else return{text:`${a.latency}ms`,color:"text-red-600"};return"error"===a.status?{text:"Error",color:"text-red-600"}:{text:"Testing...",color:"text-gray-500"}};return k?(0,c.jsx)(J.SidebarMenu,{children:(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(J.SidebarMenuButton,{size:"lg",disabled:!0,children:[(0,c.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,c.jsx)(b$,{className:"size-4"})}),(0,c.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,c.jsx)("span",{className:"truncate font-medium",children:"Loading..."}),(0,c.jsx)("span",{className:"truncate text-xs",children:"Please wait"})]})]})})}):i||0!==g.length?i?(0,c.jsx)(J.SidebarMenu,{children:(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(bI,{children:[(0,c.jsx)(bJ,{asChild:!0,children:(0,c.jsxs)(J.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,c.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,c.jsx)(b$,{className:"size-4"})}),(0,c.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,c.jsx)("span",{className:"truncate font-medium",children:i.name}),(0,c.jsx)("span",{className:`truncate text-xs ${o(i).color}`,children:o(i).text})]}),(0,c.jsx)(bY,{className:"ml-auto"})]})}),(0,c.jsxs)(bK,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"start",side:f?"bottom":"right",sideOffset:4,children:[(0,c.jsx)(bO,{className:"text-muted-foreground text-xs",children:"Backends"}),g.map((a,b)=>(0,c.jsxs)(bM,{onClick:()=>n(a),className:"gap-2 p-2",children:[(0,c.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border",children:(0,c.jsx)(b$,{className:"size-3.5 shrink-0"})}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("div",{className:"font-medium",children:a.name}),(0,c.jsx)("div",{className:`text-xs ${o(a).color}`,children:o(a).text})]}),(0,c.jsxs)(bQ,{children:["⌘",b+1]})]},a.id)),(0,c.jsx)(bP,{}),i&&(0,c.jsxs)(bM,{className:"gap-2 p-2",onClick:()=>b?.(i),children:[(0,c.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,c.jsx)(b_,{className:"size-4"})}),(0,c.jsx)("div",{className:"text-muted-foreground font-medium",children:"Edit Current Backend"})]}),(0,c.jsxs)(bM,{className:"gap-2 p-2",onClick:a,children:[(0,c.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,c.jsx)(bZ,{className:"size-4"})}),(0,c.jsx)("div",{className:"text-muted-foreground font-medium",children:"Add Backend"})]})]})]})})}):null:(0,c.jsx)(J.SidebarMenu,{children:(0,c.jsx)(J.SidebarMenuItem,{children:(0,c.jsxs)(J.SidebarMenuButton,{size:"lg",onClick:a,className:"cursor-pointer",children:[(0,c.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,c.jsx)(bZ,{className:"size-4"})}),(0,c.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,c.jsx)("span",{className:"truncate font-medium",children:"Add Backend"}),(0,c.jsx)("span",{className:"truncate text-xs",children:"No backends configured"})]})]})})})}var b2=a.i(40695);a.s(["Dialog",()=>b5,"DialogContent",()=>b8,"DialogDescription",()=>cc,"DialogFooter",()=>ca,"DialogHeader",()=>b9,"DialogTitle",()=>cb],65733);var b3=a.i(97942),b4=a.i(22262);function b5({...a}){return(0,c.jsx)(b3.Root,{"data-slot":"dialog",...a})}function b6({...a}){return(0,c.jsx)(b3.Portal,{"data-slot":"dialog-portal",...a})}function b7({className:a,...b}){return(0,c.jsx)(b3.Overlay,{"data-slot":"dialog-overlay",className:(0,bH.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function b8({className:a,children:b,showCloseButton:d=!0,...e}){return(0,c.jsxs)(b6,{"data-slot":"dialog-portal",children:[(0,c.jsx)(b7,{}),(0,c.jsxs)(b3.Content,{"data-slot":"dialog-content",className:(0,bH.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...e,children:[b,d&&(0,c.jsxs)(b3.Close,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,c.jsx)(b4.XIcon,{}),(0,c.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function b9({className:a,...b}){return(0,c.jsx)("div",{"data-slot":"dialog-header",className:(0,bH.cn)("flex flex-col gap-2 text-center sm:text-left",a),...b})}function ca({className:a,...b}){return(0,c.jsx)("div",{"data-slot":"dialog-footer",className:(0,bH.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...b})}function cb({className:a,...b}){return(0,c.jsx)(b3.Title,{"data-slot":"dialog-title",className:(0,bH.cn)("text-lg leading-none font-semibold",a),...b})}function cc({className:a,...b}){return(0,c.jsx)(b3.Description,{"data-slot":"dialog-description",className:(0,bH.cn)("text-muted-foreground text-sm",a),...b})}var cd=a.i(5522);a.s(["Label",()=>cf],17171);var ce=d.forwardRef((a,b)=>(0,c.jsx)(r.Primitive.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));function cf({className:a,...b}){return(0,c.jsx)(ce,{"data-slot":"label",className:(0,bH.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}ce.displayName="Label";let cg=(0,e.default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);a.s(["CheckCircle",()=>ch],16201);let ch=(0,e.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);a.s(["XCircle",()=>ci],62722);let ci=(0,e.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);a.s(["Clock",()=>cj],41710);let cj=(0,e.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function ck({formData:e,disabled:f=!1}){let[g,h]=d.useState(!1),[i,j]=d.useState(null),[k,l]=d.useState({}),m=async()=>{if(!e.url||!e.api_key)return void l({url:e.url?"":"URL is required for testing",api_key:e.api_key?"":"API Key is required for testing"});h(!0),j(null),l({});try{let{TestBackendConnectionDirect:b}=await a.A(3794),c=await b({name:e.name||"Test",url:e.url,api_key:e.api_key});j(c)}catch(a){j(new b.BackendConnectionTest({success:!1,message:`Test failed: ${a}`,status:0,latency:0,tested_at:new Date}))}finally{h(!1)}};return d.useEffect(()=>{j(null),l({})},[e.url,e.api_key]),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{children:"Connection Test"}),(0,c.jsx)(b2.Button,{type:"button",variant:"outline",size:"sm",onClick:m,disabled:f||g||!e.url||!e.api_key,children:g?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(cg,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing..."]}):"Test Connection"}),i&&(0,c.jsxs)("div",{className:`p-3 rounded-md border ${i.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"}`,children:[(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[i.success?(0,c.jsx)(ch,{className:"h-4 w-4"}):(0,c.jsx)(ci,{className:"h-4 w-4"}),(0,c.jsx)("span",{className:"font-medium",children:i.success?"Connection Successful":"Connection Failed"})]}),(0,c.jsx)("p",{className:"text-sm mt-1",children:i.message}),i.success&&(0,c.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs",children:[(0,c.jsxs)("div",{className:"flex items-center gap-1",children:[(0,c.jsx)(cj,{className:"h-3 w-3"}),(0,c.jsxs)("span",{children:["Latency: ",i.latency,"ms"]})]}),(0,c.jsxs)("div",{children:["Status: ",i.status]})]})]}),(k.url||k.api_key)&&(0,c.jsxs)("div",{className:"text-sm text-red-500",children:[k.url&&(0,c.jsx)("p",{children:k.url}),k.api_key&&(0,c.jsx)("p",{children:k.api_key})]})]})}function cl({open:a,onOpenChange:b,onBackendAdded:e}){let[f,g]=d.useState({name:"",url:"",api_key:""}),[h,i]=d.useState(!1),[j,k]=d.useState({}),l=()=>{g({name:"",url:"",api_key:""}),k({})},m=(a,b)=>{g(c=>({...c,[a]:b})),j[a]&&k(b=>({...b,[a]:""}))},n=async a=>{if(a.preventDefault(),(()=>{let a={};if(f.name.trim()||(a.name="Backend name is required"),f.url.trim())try{let a=f.url.startsWith("http")?f.url:`https://${f.url}`;new URL(a)}catch{a.url="Please enter a valid URL"}else a.url="Backend URL is required";return f.api_key.trim()||(a.api_key="API Key is required"),k(a),0===Object.keys(a).length})()){i(!0);try{await (0,b0.AddBackend)(f),e?.(),b(!1),l()}catch(a){k({submit:`Failed to add backend: ${a}`})}finally{i(!1)}}},o=a=>{a||l(),b(a)};return(0,c.jsx)(b5,{open:a,onOpenChange:o,children:(0,c.jsxs)(b8,{className:"sm:max-w-[500px]",children:[(0,c.jsxs)(b9,{children:[(0,c.jsx)(cb,{children:"Add New Backend"}),(0,c.jsx)(cc,{children:"Add a new Acunetix scanner backend. You can test the connection before saving."})]}),(0,c.jsxs)("form",{onSubmit:n,className:"space-y-4",children:[(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"name",children:"Backend Name"}),(0,c.jsx)(cd.Input,{id:"name",placeholder:"e.g., Production Scanner",value:f.name,onChange:a=>m("name",a.target.value),className:j.name?"border-red-500":""}),j.name&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:j.name})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"url",children:"Backend URL"}),(0,c.jsx)(cd.Input,{id:"url",placeholder:"https://scanner.example.com:3443",value:f.url,onChange:a=>m("url",a.target.value),className:j.url?"border-red-500":""}),j.url&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:j.url})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"api_key",children:"API Key"}),(0,c.jsx)(cd.Input,{id:"api_key",type:"password",placeholder:"Enter your API key",value:f.api_key,onChange:a=>m("api_key",a.target.value),className:j.api_key?"border-red-500":""}),j.api_key&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:j.api_key})]}),(0,c.jsx)(ck,{formData:f,disabled:h}),j.submit&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:j.submit})]}),(0,c.jsxs)(ca,{children:[(0,c.jsx)(b2.Button,{type:"button",variant:"outline",onClick:()=>o(!1),disabled:h,children:"Cancel"}),(0,c.jsx)(b2.Button,{type:"submit",onClick:n,disabled:h||!f.name||!f.url||!f.api_key,children:h?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(cg,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add Backend"})]})]})})}function cm({open:a,onOpenChange:b,backend:e,onBackendUpdated:f,onBackendDeleted:g}){let[h,i]=d.useState({name:"",url:"",api_key:""}),[j,k]=d.useState(!1),[l,m]=d.useState(!1),[n,o]=d.useState({});d.useEffect(()=>{e&&a&&(i({name:e.name||"",url:e.url||"",api_key:e.api_key||""}),o({}))},[e,a]);let p=(a,b)=>{i(c=>({...c,[a]:b})),n[a]&&o(b=>({...b,[a]:""}))},q=async a=>{if(a.preventDefault(),e&&(()=>{let a={};if(h.name.trim()||(a.name="Backend name is required"),h.url.trim())try{let a=h.url.startsWith("http")?h.url:`https://${h.url}`;new URL(a)}catch{a.url="Please enter a valid URL"}else a.url="Backend URL is required";return h.api_key.trim()||(a.api_key="API Key is required"),o(a),0===Object.keys(a).length})()){k(!0);try{await (0,b0.UpdateBackend)(e.id,h),f?.(),b(!1)}catch(a){o({submit:`Failed to update backend: ${a}`})}finally{k(!1)}}},r=async()=>{if(e&&confirm(`Are you sure you want to delete &quot;${e.name}&quot;? This action cannot be undone.`)){m(!0);try{await (0,b0.DeleteBackend)(e.id),g?.(),b(!1)}catch(a){o({delete:`Failed to delete backend: ${a}`})}finally{m(!1)}}},s=a=>{(a||!l)&&(b(a),a||setTimeout(()=>{i({name:"",url:"",api_key:""}),o({})},100))};return e?(0,c.jsx)(b5,{open:a,onOpenChange:s,children:(0,c.jsxs)(b8,{className:"sm:max-w-[500px]",children:[(0,c.jsxs)(b9,{children:[(0,c.jsx)(cb,{children:"Edit Backend"}),(0,c.jsxs)(cc,{children:['Update the configuration for "',e.name,'". You can test the connection after making changes.']})]}),(0,c.jsxs)("form",{onSubmit:q,className:"space-y-4",children:[(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"edit-name",children:"Backend Name"}),(0,c.jsx)(cd.Input,{id:"edit-name",placeholder:"e.g., Production Scanner",value:h.name,onChange:a=>p("name",a.target.value),className:n.name?"border-red-500":""}),n.name&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:n.name})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"edit-url",children:"Backend URL"}),(0,c.jsx)(cd.Input,{id:"edit-url",placeholder:"https://scanner.example.com:3443",value:h.url,onChange:a=>p("url",a.target.value),className:n.url?"border-red-500":""}),n.url&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:n.url})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)(cf,{htmlFor:"edit-api_key",children:"API Key"}),(0,c.jsx)(cd.Input,{id:"edit-api_key",type:"password",placeholder:"Enter your API key",value:h.api_key,onChange:a=>p("api_key",a.target.value),className:n.api_key?"border-red-500":""}),n.api_key&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:n.api_key})]}),(0,c.jsx)(ck,{formData:h,disabled:j||l}),n.submit&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:n.submit}),n.delete&&(0,c.jsx)("p",{className:"text-sm text-red-500",children:n.delete})]}),(0,c.jsxs)(ca,{className:"flex justify-between",children:[(0,c.jsx)(b2.Button,{type:"button",variant:"destructive",onClick:r,disabled:j||l,className:"mr-auto",children:l?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(cg,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(O,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(b2.Button,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:j||l,children:"Cancel"}),(0,c.jsx)(b2.Button,{type:"submit",onClick:q,disabled:j||l||!h.name||!h.url||!h.api_key,children:j?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(cg,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Backend"})]})]})]})}):null}function cn({onNavigate:a,...b}){let[e,l]=d.useState(!1),[m,n]=d.useState(!1),[o,p]=d.useState(null),[q,r]=d.useState(0),s=[{title:"Dashboard",url:"#",icon:k,isActive:!0,onClick:()=>a?.("dashboard")},{title:"Targets",url:"#",icon:j,onClick:()=>a?.("targets"),items:[{title:"Manage Targets",url:"#",onClick:()=>a?.("targets")},{title:"Target Groups",url:"#"}]},{title:"Scans",url:"#",icon:f},{title:"Vulnerabilities",url:"#",icon:i},{title:"Reports",url:"#",icon:h}];return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)(J.Sidebar,{collapsible:"icon",...b,children:[(0,c.jsx)(J.SidebarHeader,{children:(0,c.jsx)(b1,{onAddBackend:()=>{l(!0)},onEditBackend:a=>{p(a),n(!0)},refreshTrigger:q})}),(0,c.jsxs)(J.SidebarContent,{children:[(0,c.jsx)(K,{items:s,onNavigate:a}),(0,c.jsx)(bR,{projects:[{name:"Project A",url:"#",icon:g},{name:"Project B",url:"#",icon:g},{name:"Project C",url:"#",icon:g}]})]}),(0,c.jsx)(J.SidebarFooter,{children:(0,c.jsx)(bX,{})}),(0,c.jsx)(J.SidebarRail,{})]}),(0,c.jsx)(cl,{open:e,onOpenChange:l,onBackendAdded:()=>{r(a=>a+1)}}),(0,c.jsx)(cm,{open:m,onOpenChange:n,backend:o,onBackendUpdated:()=>{r(a=>a+1)},onBackendDeleted:()=>{r(a=>a+1)}})]})}!function(a){class b{id;name;url;api_key;is_active;created_at;updated_at;last_tested;status;latency;static createFrom(a={}){return new b(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.id=a.id,this.name=a.name,this.url=a.url,this.api_key=a.api_key,this.is_active=a.is_active,this.created_at=this.convertValues(a.created_at,null),this.updated_at=this.convertValues(a.updated_at,null),this.last_tested=this.convertValues(a.last_tested,null),this.status=a.status,this.latency=a.latency}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.Backend=b;class c{success;message;status;latency;version;tested_at;static createFrom(a={}){return new c(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.success=a.success,this.message=a.message,this.status=a.status,this.latency=a.latency,this.version=a.version,this.tested_at=this.convertValues(a.tested_at,null)}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.BackendConnectionTest=c;class d{backends;count;static createFrom(a={}){return new d(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.backends=this.convertValues(a.backends,b),this.count=a.count}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.BackendList=d;class e{auto_connect;check_interval;theme;language;log_level;max_retries;connection_timeout;static createFrom(a={}){return new e(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.auto_connect=a.auto_connect,this.check_interval=a.check_interval,this.theme=a.theme,this.language=a.language,this.log_level=a.log_level,this.max_retries=a.max_retries,this.connection_timeout=a.connection_timeout}}a.Settings=e;class f{version;backends;settings;last_save;static createFrom(a={}){return new f(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.version=a.version,this.backends=this.convertValues(a.backends,b),this.settings=this.convertValues(a.settings,e),this.last_save=this.convertValues(a.last_save,null)}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.Config=f;class g{auth;scan;static createFrom(a={}){return new g(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.auth=a.auth,this.scan=a.scan}}a.DefaultOverrides=g;class h{rel;href;static createFrom(a={}){return new h(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.rel=a.rel,this.href=a.href}}a.Link=h;class i{name;url;api_key;static createFrom(a={}){return new i(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.name=a.name,this.url=a.url,this.api_key=a.api_key}}a.NewBackendRequest=i;class j{address;description;criticality;static createFrom(a={}){return new j(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.address=a.address,this.description=a.description,this.criticality=a.criticality}}a.NewTargetRequest=j;class k{url;content;static createFrom(a={}){return new k(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.url=a.url,this.content=a.content}}a.ScanAuthorization=k;class l{critical;high;medium;low;info;static createFrom(a={}){return new l(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.critical=a.critical,this.high=a.high,this.medium=a.medium,this.low=a.low,this.info=a.info}}a.SeverityCounts=l;class m{agent_id;name;static createFrom(a={}){return new m(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.agent_id=a.agent_id,this.name=a.name}}a.TargetAgent=m;class n{address;description;type;criticality;fqdn_status;fqdn_tm_hash;deleted_at;fqdn;fqdn_hash;default_scanning_profile_id;agents;default_overrides;target_id;scan_authorization;continuous_mode;last_scan_date;last_scan_id;last_scan_session_id;last_scan_session_status;severity_counts;threat;links;manual_intervention;verification;static createFrom(a={}){return new n(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.address=a.address,this.description=a.description,this.type=a.type,this.criticality=a.criticality,this.fqdn_status=a.fqdn_status,this.fqdn_tm_hash=a.fqdn_tm_hash,this.deleted_at=a.deleted_at,this.fqdn=a.fqdn,this.fqdn_hash=a.fqdn_hash,this.default_scanning_profile_id=a.default_scanning_profile_id,this.agents=this.convertValues(a.agents,m),this.default_overrides=this.convertValues(a.default_overrides,g),this.target_id=a.target_id,this.scan_authorization=this.convertValues(a.scan_authorization,k),this.continuous_mode=a.continuous_mode,this.last_scan_date=a.last_scan_date,this.last_scan_id=a.last_scan_id,this.last_scan_session_id=a.last_scan_session_id,this.last_scan_session_status=a.last_scan_session_status,this.severity_counts=this.convertValues(a.severity_counts,l),this.threat=a.threat,this.links=this.convertValues(a.links,h),this.manual_intervention=a.manual_intervention,this.verification=a.verification}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.TargetItemResponse=n;class o{targets;count;static createFrom(a={}){return new o(a)}constructor(a={}){"string"==typeof a&&(a=JSON.parse(a)),this.targets=this.convertValues(a.targets,n),this.count=a.count}convertValues(a,b,c=!1){if(!a)return a;if(a.slice&&a.map)return a.map(a=>this.convertValues(a,b));if("object"==typeof a){if(c){for(let c of Object.keys(a))a[c]=new b(a[c]);return a}return new b(a)}return a}}a.TargetList=o}(b||(b={}))}];

//# sourceMappingURL=_accb501b._.js.map